# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Next.js
.next/
out/
*.tsbuildinfo
next-env.d.ts

# Electron
dist/
release/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Database
*.db
*.sqlite
*.sqlite3

# Media files (for testing)
uploads/
temp/
audio_temp/

# Backup files
*.bak
*.backup

# macOS
.AppleDouble
.LSOverride

# Windows
ehthumbs.db
Thumbs.db

# API配置文件 - 安全敏感信息
config/api_keys.py
config/secrets.json
.api_key
*.secret
config/.env*
backend/config/api_keys.py
backend/config/secrets.json

# 测试文件和调试文件
test_*.txt
test-*.js
diagnose-*.js
quick-test-*.sh

# 临时数据库文件
videosense.db

# 测试目录
test_files/ 