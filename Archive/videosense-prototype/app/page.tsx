"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Upload,
  Play,
  Download,
  FileText,
  Clock,
  XCircle,
  AlertCircle,
  Edit3,
  History,
  Settings,
  HelpCircle,
  Languages,
  Globe,
} from "lucide-react"

// 语言配置
const translations = {
  zh: {
    appName: "VideoSense",
    fileTranscription: "文件转录",
    liveCapture: "播放即录",
    taskManagement: "任务管理",
    history: "历史记录",
    fileTranscriptionTitle: "文件转录",
    fileTranscriptionDesc: "将音频、视频文件拖拽或选择上传进行转录",
    liveTitle: "播放即录",
    liveDesc: "自动检测当前播放的媒体内容，一键开始转录",
    tasksTitle: "任务管理",
    tasksDesc: "查看和管理所有转录任务的进行状态",
    historyTitle: "历史记录",
    historyDesc: "查看和编辑过往的转录结果",
    uploadFiles: "上传文件",
    selectFiles: "选择文件",
    supportedFormats: "支持 MP4, MOV, MKV, MP3, M4A, WAV 格式",
    highAccuracy: "高精度转录",
    highAccuracyDesc: "AI技术驱动的准确文字转换",
    timestamp: "时间戳",
    timestampDesc: "为每个段落添加精确时间标记",
    editFeatures: "编辑功能",
    editFeaturesDesc: "支持Markdown格式的强大编辑器",
    detectedMedia: "检测到的媒体",
    detectedMediaDesc: "以下媒体正在播放中，请选择需要转录的内容",
    source: "来源",
    duration: "时长",
    selected: "已选择",
    startTranscription: "开始转录",
    usageNotice: "使用须知",
    usageNoticeDesc: "首次使用时需要同意使用条款，请确认您对内容的使用权限",
    completed: "已完成",
    transcribing: "转录中",
    downloading: "下载中",
    encoding: "转码中",
    queued: "排队中",
    failed: "失败",
    progress: "进度",
    createdAt: "创建时间",
    cancel: "取消",
    agreeAndContinue: "同意并继续",
    importantNotice: "重要提示",
    authDialogContent: `为了帮助您更好地学习和研究，VideoSense的"播放即录"功能会在您的电脑上对正在播放的内容进行临时处理。

在使用前，请您理解并确认以下几点：

• 您是内容的主人: 您需要确保拥有处理这些内容的合法权利，或者您的使用行为符合您所在地区的"合理使用"或"信息解析"等法律规定。

• 我们是工具，您是作者: VideoSense仅作为您的个人工具，所有操作均由您发起。您需要为您的使用行为承担全部责任。

• 用后即焚，保护隐私: 处理完成后，所有临时文件都将被立即删除，我们不会保存您的任何视频内容。

更详细的信息，请查阅我们的《服务条款》。`,
    agreeTerms: "我已阅读、理解并同意以上条款，愿意承担相关责任",
  },
  en: {
    appName: "VideoSense",
    fileTranscription: "File Transcription",
    liveCapture: "Live Capture",
    taskManagement: "Task Management",
    history: "History",
    fileTranscriptionTitle: "File Transcription",
    fileTranscriptionDesc: "Drag and drop or select audio/video files for transcription",
    liveTitle: "Live Capture",
    liveDesc: "Automatically detect currently playing media and start transcription with one click",
    tasksTitle: "Task Management",
    tasksDesc: "View and manage the progress of all transcription tasks",
    historyTitle: "History",
    historyDesc: "View and edit past transcription results",
    uploadFiles: "Upload Files",
    selectFiles: "Select Files",
    supportedFormats: "Supports MP4, MOV, MKV, MP3, M4A, WAV formats",
    highAccuracy: "High Accuracy",
    highAccuracyDesc: "AI-powered accurate text conversion",
    timestamp: "Timestamps",
    timestampDesc: "Add precise time markers to each paragraph",
    editFeatures: "Edit Features",
    editFeaturesDesc: "Powerful editor with Markdown support",
    detectedMedia: "Detected Media",
    detectedMediaDesc: "The following media is currently playing. Please select content to transcribe",
    source: "Source",
    duration: "Duration",
    selected: "selected",
    startTranscription: "Start Transcription",
    usageNotice: "Usage Notice",
    usageNoticeDesc: "First-time use requires agreement to terms of use. Please confirm your content usage rights",
    completed: "Completed",
    transcribing: "Transcribing",
    downloading: "Downloading",
    encoding: "Encoding",
    queued: "Queued",
    failed: "Failed",
    progress: "Progress",
    createdAt: "Created",
    cancel: "Cancel",
    agreeAndContinue: "Agree and Continue",
    importantNotice: "Important Notice",
    authDialogContent: `To help you better learn and research, VideoSense's "Live Capture" feature will temporarily process content playing on your computer.

Before using, please understand and confirm the following:

• You own the content: You need to ensure you have legal rights to process this content, or your usage complies with "fair use" or "information analysis" laws in your region.

• We are the tool, you are the author: VideoSense serves only as your personal tool, with all operations initiated by you. You are fully responsible for your usage.

• Delete after use, protect privacy: After processing, all temporary files will be immediately deleted. We do not save any of your video content.

For more details, please review our Terms of Service.`,
    agreeTerms: "I have read, understood, and agree to the above terms and am willing to take responsibility",
  },
}

export default function VideoSenseApp() {
  const [activeTab, setActiveTab] = useState("upload")
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const [authAgreed, setAuthAgreed] = useState(false)
  const [language, setLanguage] = useState<"zh" | "en">("zh")

  const t = translations[language]

  const [tasks, setTasks] = useState([
    {
      id: 1,
      title: "在线讲座 - AI基础知识",
      status: "completed",
      progress: 100,
      duration: "45:32",
      createdAt: "2025-06-19 14:30",
    },
    {
      id: 2,
      title: "会议录像 - 项目进展讨论",
      status: "transcribing",
      progress: 65,
      duration: "1:23:15",
      createdAt: "2025-06-19 15:45",
    },
    {
      id: 3,
      title: "播客节目 - 科技前沿资讯",
      status: "downloading",
      progress: 30,
      duration: "52:18",
      createdAt: "2025-06-19 16:20",
    },
  ])

  const [detectedMedia, setDetectedMedia] = useState([
    {
      id: 1,
      title: "YouTube - Next.js 15 新特性详解",
      source: "YouTube",
      duration: "28:45",
      selected: false,
    },
    {
      id: 2,
      title: "Spotify - 技术播客 #123",
      source: "Spotify",
      duration: "1:15:30",
      selected: false,
    },
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500"
      case "transcribing":
        return "bg-blue-500"
      case "downloading":
        return "bg-yellow-500"
      case "failed":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      completed: t.completed,
      transcribing: t.transcribing,
      downloading: t.downloading,
      encoding: t.encoding,
      queued: t.queued,
      failed: t.failed,
    }
    return statusMap[status] || "未知"
  }

  const handleStartTranscription = () => {
    const hasSelected = detectedMedia.some((media) => media.selected)
    if (hasSelected && !authAgreed) {
      setShowAuthDialog(true)
    } else if (hasSelected) {
      console.log("Starting transcription...")
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl font-semibold text-gray-900">{t.appName}</h1>
            <Badge variant="secondary" className="text-xs">
              v1.0
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Languages className="w-4 h-4 mr-2" />
                  {language === "zh" ? "中文" : "English"}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setLanguage("zh")}>
                  <Globe className="w-4 h-4 mr-2" />
                  中文
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLanguage("en")}>
                  <Globe className="w-4 h-4 mr-2" />
                  English
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <HelpCircle className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white border-r border-gray-200 min-h-screen">
          <nav className="p-4 space-y-2">
            <Button
              variant={activeTab === "upload" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("upload")}
            >
              <Upload className="w-4 h-4 mr-2" />
              {t.fileTranscription}
            </Button>
            <Button
              variant={activeTab === "live" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("live")}
            >
              <Play className="w-4 h-4 mr-2" />
              {t.liveCapture}
            </Button>
            <Button
              variant={activeTab === "tasks" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("tasks")}
            >
              <Clock className="w-4 h-4 mr-2" />
              {t.taskManagement}
            </Button>
            <Button
              variant={activeTab === "history" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("history")}
            >
              <History className="w-4 h-4 mr-2" />
              {t.history}
            </Button>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {activeTab === "upload" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t.fileTranscriptionTitle}</h2>
                <p className="text-gray-600">{t.fileTranscriptionDesc}</p>
              </div>

              <Card className="border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Upload className="w-12 h-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{t.uploadFiles}</h3>
                  <p className="text-gray-500 text-center mb-4">{t.supportedFormats}</p>
                  <Button>{t.selectFiles}</Button>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <FileText className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                    <h4 className="font-medium">{t.highAccuracy}</h4>
                    <p className="text-sm text-gray-500">{t.highAccuracyDesc}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <Clock className="w-8 h-8 text-green-500 mx-auto mb-2" />
                    <h4 className="font-medium">{t.timestamp}</h4>
                    <p className="text-sm text-gray-500">{t.timestampDesc}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <Edit3 className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                    <h4 className="font-medium">{t.editFeatures}</h4>
                    <p className="text-sm text-gray-500">{t.editFeaturesDesc}</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === "live" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t.liveTitle}</h2>
                <p className="text-gray-600">{t.liveDesc}</p>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Play className="w-5 h-5" />
                    {t.detectedMedia}
                  </CardTitle>
                  <CardDescription>{t.detectedMediaDesc}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {detectedMedia.map((media) => (
                    <div key={media.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <Checkbox
                        checked={media.selected}
                        onCheckedChange={(checked) => {
                          setDetectedMedia((prev) =>
                            prev.map((m) => (m.id === media.id ? { ...m, selected: !!checked } : m)),
                          )
                        }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium">{media.title}</h4>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>
                            {t.source}: {media.source}
                          </span>
                          <span>
                            {t.duration}: {media.duration}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}

                  <div className="flex justify-between items-center pt-4">
                    <p className="text-sm text-gray-500">
                      {detectedMedia.filter((m) => m.selected).length} {language === "zh" ? "项" : "items"} {t.selected}
                    </p>
                    <Button onClick={handleStartTranscription} disabled={!detectedMedia.some((m) => m.selected)}>
                      {t.startTranscription}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">{t.usageNotice}</h4>
                      <p className="text-sm text-blue-700 mt-1">{t.usageNoticeDesc}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === "tasks" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t.tasksTitle}</h2>
                <p className="text-gray-600">{t.tasksDesc}</p>
              </div>

              <div className="space-y-4">
                {tasks.map((task) => (
                  <Card key={task.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(task.status)}`} />
                          <h3 className="font-medium">{task.title}</h3>
                          <Badge variant="outline">{getStatusText(task.status)}</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          {task.status === "downloading" && (
                            <Button variant="ghost" size="sm">
                              <XCircle className="w-4 h-4" />
                            </Button>
                          )}
                          {task.status === "completed" && (
                            <Button variant="ghost" size="sm">
                              <Edit3 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm text-gray-500">
                          <span>
                            {t.progress}: {task.progress}%
                          </span>
                          <span>
                            {t.duration}: {task.duration}
                          </span>
                        </div>
                        <Progress value={task.progress} className="h-2" />
                        <p className="text-xs text-gray-400">
                          {t.createdAt}: {task.createdAt}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {activeTab === "history" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t.historyTitle}</h2>
                <p className="text-gray-600">{t.historyDesc}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {tasks
                  .filter((task) => task.status === "completed")
                  .map((task) => (
                    <Card key={task.id} className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">{task.title}</CardTitle>
                        <CardDescription className="flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          {task.duration}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500">{task.createdAt}</span>
                          <div className="flex gap-1">
                            <Button variant="ghost" size="sm">
                              <Edit3 className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Download className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </div>
          )}
        </main>
      </div>

      {/* First-time Authorization Dialog */}
      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-xl">{t.importantNotice}</DialogTitle>
          </DialogHeader>
          <DialogDescription asChild>
            <div className="space-y-4 text-sm whitespace-pre-line">{t.authDialogContent}</div>
          </DialogDescription>

          <div className="flex items-center space-x-2 py-4">
            <Checkbox id="agree-terms" checked={authAgreed} onCheckedChange={setAuthAgreed} />
            <label htmlFor="agree-terms" className="text-sm">
              {t.agreeTerms}
            </label>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAuthDialog(false)}>
              {t.cancel}
            </Button>
            <Button
              disabled={!authAgreed}
              onClick={() => {
                setShowAuthDialog(false)
                // Start transcription logic here
              }}
            >
              {t.agreeAndContinue}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
