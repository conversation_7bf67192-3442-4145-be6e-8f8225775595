"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Save,
  Download,
  Copy,
  Search,
  Replace,
  Bold,
  Italic,
  List,
  Hash,
  Clock,
  User,
  Sparkles,
  ArrowLeft,
  Languages,
  Globe,
} from "lucide-react"

interface EditorViewProps {
  title: string
  onBack: () => void
}

const translations = {
  zh: {
    editing: "编辑中",
    save: "保存",
    formatting: "格式设置",
    aiToolbox: "AI工具箱",
    summarize: "生成摘要",
    extractKeyPoints: "提取要点",
    translate: "翻译",
    comingSoon: "※ 即将在后续版本中推出",
    documentInfo: "文档信息",
    speakers: "位发言者",
    wordCount: "字数",
    transcriptionPlaceholder: "转录内容将在这里显示...",
    sampleContent: `# 在线讲座 - AI基础知识

## 概述
本次讲座将深入探讨人工智能的基础概念和应用。

## [00:02:15] 开场介绍
**讲师**: 大家好，欢迎参加今天的AI基础知识讲座。

人工智能（AI）是让计算机能够执行通常需要人类智能的任务的技术。

## [00:05:30] AI发展历史
**讲师**: AI的发展可以追溯到1950年代，当时阿兰·图灵提出了著名的"图灵测试"。

主要发展阶段：
- 1950年代：AI概念诞生
- 1960年代：早期AI程序开发
- 1980年代：专家系统兴起
- 2010年代：深度学习革命

## [00:12:45] 机器学习基础
**讲师**: 机器学习是AI的一个重要分支，它让计算机能够从数据中学习模式。

### 主要类型
1. **监督学习**：使用标记数据进行训练
2. **无监督学习**：从未标记数据中发现模式
3. **强化学习**：通过试错学习最优策略

## [00:18:20] 深度学习
**讲师**: 深度学习使用多层神经网络来模拟人脑的工作方式。

特点：
- 多层神经网络结构
- 需要大量训练数据
- 在图像识别和自然语言处理方面表现出色

## [00:25:10] 实际应用
**讲师**: 目前AI在各个领域都有广泛应用。

- **医疗**：医学影像诊断、药物发现
- **交通**：自动驾驶汽车
- **金融**：欺诈检测、算法交易
- **娱乐**：推荐系统

## [00:30:45] 总结
**讲师**: 今天我们学习了AI的基础知识，这是一个快速发展的领域，将继续改变我们的生活方式。

下次课程我们将深入学习具体的AI算法。`,
  },
  en: {
    editing: "Editing",
    save: "Save",
    formatting: "Formatting",
    aiToolbox: "AI Toolbox",
    summarize: "Generate Summary",
    extractKeyPoints: "Extract Key Points",
    translate: "Translate",
    comingSoon: "※ Coming in future updates",
    documentInfo: "Document Info",
    speakers: "speakers",
    wordCount: "Word count",
    transcriptionPlaceholder: "Transcription content will appear here...",
    sampleContent: `# Online Lecture - AI Fundamentals

## Overview
This lecture will explore the fundamental concepts and applications of artificial intelligence.

## [00:02:15] Introduction
**Instructor**: Hello everyone, welcome to today's AI fundamentals lecture.

Artificial Intelligence (AI) is technology that enables computers to perform tasks that typically require human intelligence.

## [00:05:30] History of AI
**Instructor**: AI development can be traced back to the 1950s when Alan Turing proposed the famous "Turing Test."

Major development stages:
- 1950s: Birth of AI concepts
- 1960s: Early AI program development
- 1980s: Rise of expert systems
- 2010s: Deep learning revolution

## [00:12:45] Machine Learning Basics
**Instructor**: Machine learning is an important branch of AI that enables computers to learn patterns from data.

### Main Types
1. **Supervised Learning**: Training with labeled data
2. **Unsupervised Learning**: Discovering patterns from unlabeled data
3. **Reinforcement Learning**: Learning optimal strategies through trial and error

## [00:18:20] Deep Learning
**Instructor**: Deep learning uses multi-layer neural networks to simulate how the human brain works.

Features:
- Multi-layer neural network structure
- Requires large amounts of training data
- Excellent performance in image recognition and natural language processing

## [00:25:10] Practical Applications
**Instructor**: AI currently has widespread applications across various fields.

- **Healthcare**: Medical imaging diagnosis, drug discovery
- **Transportation**: Autonomous vehicles
- **Finance**: Fraud detection, algorithmic trading
- **Entertainment**: Recommendation systems

## [00:30:45] Summary
**Instructor**: Today we learned AI fundamentals. This is a rapidly developing field that will continue to change how we live.

Next class we'll dive deeper into specific AI algorithms.`,
  },
}

export default function EditorView({ title, onBack }: EditorViewProps) {
  const [language, setLanguage] = useState<"zh" | "en">("zh")
  const t = translations[language]

  const [content, setContent] = useState(t.sampleContent)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
            <Badge variant="secondary">{t.editing}</Badge>
          </div>
          <div className="flex items-center gap-2">
            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Languages className="w-4 h-4 mr-2" />
                  {language === "zh" ? "中文" : "EN"}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setLanguage("zh")}>
                  <Globe className="w-4 h-4 mr-2" />
                  中文
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLanguage("en")}>
                  <Globe className="w-4 h-4 mr-2" />
                  English
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="ghost" size="sm">
              <Search className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Replace className="w-4 h-4" />
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button variant="ghost" size="sm">
              <Copy className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Download className="w-4 h-4" />
            </Button>
            <Button size="sm">
              <Save className="w-4 h-4 mr-2" />
              {t.save}
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Toolbar */}
        <aside className="w-64 bg-white border-r border-gray-200 min-h-screen">
          <div className="p-4 space-y-4">
            {/* Formatting Tools */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">{t.formatting}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" size="sm">
                    <Bold className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Italic className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Hash className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* AI Tools */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Sparkles className="w-4 h-4" />
                  {t.aiToolbox}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                  {t.summarize}
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                  {t.extractKeyPoints}
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start" disabled>
                  {t.translate}
                </Button>
                <p className="text-xs text-gray-500 mt-2">{t.comingSoon}</p>
              </CardContent>
            </Card>

            {/* Document Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">{t.documentInfo}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span>45:32</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-gray-400" />
                  <span>1{t.speakers}</span>
                </div>
                <div className="text-gray-500">{t.wordCount}: 1,247</div>
              </CardContent>
            </Card>
          </div>
        </aside>

        {/* Editor */}
        <main className="flex-1 p-6">
          <Card className="h-full">
            <CardContent className="p-0 h-full">
              <ScrollArea className="h-full">
                <Textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="min-h-screen border-0 resize-none focus-visible:ring-0 text-sm leading-relaxed"
                  placeholder={t.transcriptionPlaceholder}
                />
              </ScrollArea>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
