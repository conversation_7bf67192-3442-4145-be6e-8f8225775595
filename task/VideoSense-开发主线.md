# VideoSense 开发主线

## 🎯 总体目标
创建"一次安装后续无打扰"的全系统音视频资源抓取与AI转录工具

### 用户体验目标
```
安装时：一键安装 → 自动证书 → 获取权限
使用时：点击启动 → 打开任意应用 → 自动抓取  
停止时：点击停止 → 自动恢复 → 其他工具正常
```

## 📅 开发计划（10周）

### ✅ 前期准备（已完成）
- 项目重组和架构设计
- 问题分析：Clash Pro冲突解决方案
- 技术方案确定：保持Python+FastAPI+Next.js+Electron

### 🔥 Phase 1: 核心代理引擎（2周）- **当前阶段**
**目标**: 解决系统代理冲突，实现智能代理链

#### Week 1: 证书管理 + 系统代理管理
- [x] Day 1-2: 证书生成和跨平台安装 ✅
- [x] Day 3-4: 系统代理设置和恢复机制 ✅
- [x] Day 5: 跨平台统一接口 ✅

#### Week 2: 智能冲突检测 + 基础集成
- [x] Day 6-7: 翻墙工具检测算法 ✅
- [ ] Day 8-9: 基础代理服务器改进
- [ ] Day 10: 集成测试和调试

### 🚀 Phase 2: 资源抓取引擎（3周）
- 智能资源识别和筛选
- 高性能并发下载
- 断点续传和重试机制

### 🎨 Phase 3: 用户界面优化（2周）  
- 一键启动界面
- 实时状态监控
- 批量管理功能

### 🤖 Phase 4: AI转录集成（1周）
- 无缝转录体验
- 多格式支持
- 智能字幕生成

## 📊 当前进度
- **整体进度**: 50% (Phase 1 Week 1完成，Week 2 Day 6-7完成)
- **Phase 1 进度**: 70% (Day 1-7 完成)
- **当前任务**: 基础代理服务器改进

## 🔧 核心技术栈
- **后端**: Python + FastAPI + mitmproxy
- **前端**: Next.js + React + TailwindCSS  
- **桌面**: Electron
- **AI**: 多平台转录API集成

## 📁 相关文档
- [Phase 1详细计划](./Phase1-核心代理引擎开发.md)
- [归档文档](./Archive/)

## 🎯 本周完成项目
### Day 1-2: 证书管理器 ✅
- 创建 `CertificateManager` 类
- 实现跨平台证书生成（macOS/Windows/Linux）
- 实现自动证书安装到系统信任库
- 支持证书有效性检查和管理

### Day 3-4: 系统代理管理器 ✅  
- 创建 `SystemProxyManager` 类
- 实现跨平台系统代理设置和恢复
- 实现代理软件检测（Clash、V2Ray等）
- 实现智能代理链配置建议

### Day 5: 跨平台统一接口 ✅
- 创建 `VideoSenseCore` 统一管理器
- 实现一键启动/停止/重启功能
- 提供统一配置管理和状态监控
- 创建 `/videosense/*` API路由

### Day 6-7: 翻墙工具检测算法 ✅
- 创建 `ProxyDetectionEngine` 增强检测引擎
- 实现多策略检测：进程、端口、配置文件、API
- 支持Clash Pro、V2Ray、Shadowsocks等主流工具
- 智能代理链配置建议

### API集成 ✅
- 创建完整的 RESTful API (`/certificate/*`, `/proxy/*`, `/videosense/*`)
- 提供证书管理、代理设置、冲突检测等功能
- 集成到主应用，可通过HTTP调用

### 测试验证 ✅
- 成功检测到5个代理软件配置
- 智能选择代理链模式避免冲突
- 统一接口工作正常，各模块协作良好

---
*最后更新: 2024-12-24* 