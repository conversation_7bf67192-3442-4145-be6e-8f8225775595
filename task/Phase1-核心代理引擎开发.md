# Phase 1: 核心代理引擎开发 - 具体实施计划

## 📅 时间安排：2周 (Week 1-2)

基于res-downloader的成功经验，开发VideoSense的核心代理引擎，实现自动证书管理、系统代理设置和智能冲突检测。

## 🎯 核心目标

完成VideoSense的"一键启动"基础能力：
- ✅ 用户点击启动按钮
- ✅ 自动处理证书和代理设置
- ✅ 智能检测并处理与翻墙工具的冲突
- ✅ 开始拦截系统所有网络流量

## 📋 详细任务清单

### Week 1: 证书管理 + 系统代理管理

#### Day 1-2: 证书管理系统
- [ ] **创建证书生成模块** 
  ```python
  # 文件: Project/backend/app/certificate_manager.py
  - 生成VideoSense自签名根证书和私钥
  - 证书包含必要的扩展（BasicConstraints, KeyUsage）
  - 设置合适的有效期（1年，支持自动续期）
  - 证书指纹记录和验证
  ```

- [ ] **macOS证书安装**
  ```python
  # 实现macOS钥匙串操作
  - 使用security命令行工具安装证书
  - 检测是否需要管理员权限
  - 实现静默安装和用户确认模式
  - 验证证书安装成功
  ```

- [ ] **Windows证书安装**
  ```python
  # 实现Windows证书存储操作
  - 使用certutil命令安装到受信任根
  - 处理UAC权限提示
  - 支持当前用户和本地计算机存储
  - 验证证书安装状态
  ```

- [ ] **Linux证书安装**
  ```python
  # 实现Linux系统证书安装
  - 支持update-ca-certificates (Debian/Ubuntu)
  - 支持update-ca-trust (RHEL/CentOS)
  - 处理不同发行版的差异
  - 验证证书生效状态
  ```

#### Day 3-4: 系统代理管理器
- [ ] **创建代理状态管理**
  ```python
  # 文件: Project/backend/app/system_proxy_manager.py
  - 检测当前系统代理设置
  - 保存原始代理配置（启动时备份）
  - 设置VideoSense代理（127.0.0.1:8899）
  - 恢复原始代理设置（停止时还原）
  ```

- [ ] **macOS代理设置**
  ```python
  # 使用networksetup命令
  - 获取网络服务列表（Wi-Fi, Ethernet等）
  - 设置HTTP/HTTPS代理指向VideoSense
  - 支持PAC文件配置（可选）
  - 处理多网络接口场景
  ```

- [ ] **Windows代理设置**
  ```python
  # 使用注册表和WinINet API
  - 修改Internet Settings注册表项
  - 调用WinINet API刷新代理设置
  - 支持按应用程序设置代理
  - 处理企业环境的代理策略
  ```

- [ ] **异常退出恢复机制**
  ```python
  # 实现robust的恢复机制
  - 启动时检查是否有残留的VideoSense代理设置
  - 自动清理并恢复原始设置
  - 创建系统锁文件防止多实例
  - 注册系统关闭事件处理器
  ```

#### Day 5: 跨平台统一接口
- [ ] **统一代理管理接口**
  ```python
  # 创建跨平台统一的API接口
  class SystemProxyManager:
      def get_current_proxy() -> ProxyConfig
      def backup_current_proxy() -> bool
      def set_videosense_proxy() -> bool  
      def restore_original_proxy() -> bool
      def verify_proxy_settings() -> bool
  ```

- [ ] **权限检测和处理**
  ```python
  # 实现权限检测和获取
  - 检测当前用户权限级别
  - 提供权限提升指导
  - 实现降级功能（用户权限模式）
  - 记录权限获取结果
  ```

### Week 2: 智能冲突检测 + 基础集成

#### Day 6-7: 智能冲突检测器
- [ ] **翻墙工具检测算法**
  ```python
  # 文件: Project/backend/app/conflict_detector.py
  - Clash Pro检测：端口扫描65327，配置文件检测
  - V2Ray检测：进程名称，配置文件位置
  - Shadowsocks检测：常见端口1080，配置文件
  - Surge检测：macOS进程，配置文件
  - 通用代理检测：端口扫描常见代理端口
  ```

- [ ] **代理配置分析**
  ```python
  # 分析现有代理配置
  - 解析代理类型（HTTP/HTTPS/SOCKS）
  - 获取代理服务器地址和端口
  - 检测代理认证方式
  - 评估代理性能和可用性
  ```

- [ ] **冲突解决策略**
  ```python
  # 实现智能冲突解决
  - 代理链模式：VideoSense -> 翻墙工具 -> Internet
  - 端口避让模式：使用不同端口避免冲突
  - 应用分离模式：按应用分别处理
  - 用户选择模式：提供选项让用户决定
  ```

#### Day 8-9: 基础代理服务器
- [ ] **集成现有mitmproxy代码**
  ```python
  # 基于现有proxy_capture.py改进
  - 添加证书管理集成
  - 添加系统代理管理集成
  - 添加冲突检测集成
  - 保持现有资源识别功能
  ```

- [ ] **改进代理服务器性能**
  ```python
  # 优化代理转发性能
  - 实现连接池减少握手开销
  - 添加缓存机制加速重复请求
  - 优化内存使用降低资源占用
  - 实现异步处理提升并发能力
  ```

- [ ] **代理链模式实现**
  ```python
  # 实现upstream代理支持
  - 自动配置上游代理指向翻墙工具
  - 智能路由：媒体流量本地处理，其他透传
  - 支持代理认证传递
  - 实现故障切换机制
  ```

#### Day 10: 集成测试和调试
- [ ] **端到端测试**
  ```python
  # 完整流程测试
  - 安装证书 -> 设置代理 -> 启动服务 -> 处理流量
  - 测试各平台的完整流程
  - 测试与Clash Pro的共存
  - 测试异常情况的处理
  ```

- [ ] **性能基准测试**
  ```python
  # 建立性能基线
  - 测量启动时间（目标<3秒）
  - 测量内存占用（目标<100MB）
  - 测量网络延迟（目标<50ms）
  - 测量CPU占用（目标<5%）
  ```

- [ ] **错误处理和日志**
  ```python
  # 完善错误处理
  - 统一错误码和错误信息
  - 详细的操作日志记录
  - 用户友好的错误提示
  - 问题诊断和自动修复建议
  ```

## 🔧 技术实现细节

### 证书生成代码结构
```python
class CertificateManager:
    def __init__(self):
        self.cert_dir = Path.home() / ".videosense" / "certs"
        self.cert_file = self.cert_dir / "videosense.crt"
        self.key_file = self.cert_dir / "videosense.key"
    
    def generate_certificate(self) -> bool:
        """生成自签名证书"""
        
    def install_certificate(self) -> bool:
        """安装证书到系统信任存储"""
        
    def verify_certificate(self) -> bool:
        """验证证书安装状态"""
        
    def uninstall_certificate(self) -> bool:
        """卸载时清理证书"""
```

### 系统代理管理接口
```python
class SystemProxyManager:
    def __init__(self):
        self.original_config = None
        self.videosense_config = {
            'http': '127.0.0.1:8899',
            'https': '127.0.0.1:8899'
        }
    
    def backup_and_set_proxy(self) -> bool:
        """备份现有设置并设置VideoSense代理"""
        
    def restore_proxy(self) -> bool:
        """恢复原始代理设置"""
```

### 冲突检测器接口
```python
class ConflictDetector:
    def detect_existing_proxies(self) -> List[ProxyInfo]:
        """检测系统中现有的代理工具"""
        
    def suggest_resolution_strategy(self, proxies: List[ProxyInfo]) -> ResolutionStrategy:
        """建议冲突解决策略"""
        
    def apply_resolution(self, strategy: ResolutionStrategy) -> bool:
        """应用冲突解决策略"""
```

## 📊 验收标准

### 功能验收
- [ ] **证书管理**: 在macOS/Windows/Linux上成功安装和卸载证书
- [ ] **代理设置**: 正确设置和恢复系统代理，不影响其他工具
- [ ] **冲突检测**: 准确识别Clash Pro等翻墙工具，提供合理建议
- [ ] **代理服务**: 能够拦截和转发HTTP/HTTPS流量
- [ ] **异常处理**: 异常退出后能够自动恢复系统设置

### 性能验收
- [ ] **启动时间**: 完整启动流程在3秒内完成
- [ ] **内存占用**: 空闲时内存使用不超过50MB
- [ ] **网络延迟**: 代理转发延迟不超过30ms
- [ ] **稳定性**: 连续运行8小时无崩溃

### 用户体验验收
- [ ] **一键启动**: 用户只需点击一个按钮即可完成所有配置
- [ ] **状态显示**: 清晰显示当前系统状态和可能的问题
- [ ] **错误提示**: 出现问题时提供明确的错误信息和解决建议
- [ ] **兼容性**: 与Clash Pro共存时网络功能正常

## 🚨 风险点和缓解措施

### 高风险点
1. **权限获取失败**
   - 风险：无法安装证书或设置系统代理
   - 缓解：提供详细权限指导，支持手动安装模式

2. **证书信任问题**
   - 风险：系统或浏览器不信任自签名证书
   - 缓解：完善证书安装流程，提供手动添加指导

3. **与翻墙工具冲突**
   - 风险：导致网络连接中断或翻墙失效
   - 缓解：智能检测，安全的回退机制

### 缓解策略
- 所有系统修改都要有完整的回退方案
- 提供详细的安装日志便于问题排查
- 实现渐进式降级：全自动 -> 半自动 -> 手动指导

## 📝 交付物

### 代码交付
- [ ] `certificate_manager.py` - 证书管理模块
- [ ] `system_proxy_manager.py` - 系统代理管理模块  
- [ ] `conflict_detector.py` - 冲突检测模块
- [ ] `enhanced_proxy_server.py` - 改进的代理服务器
- [ ] 相关的单元测试和集成测试

### 文档交付
- [ ] API文档：核心模块的接口说明
- [ ] 部署文档：安装和配置指南
- [ ] 故障排除指南：常见问题和解决方案
- [ ] 性能测试报告：基准测试结果

---

**负责人**: VideoSense开发团队
**时间线**: 2024年12月 - Week 1-2
**依赖项**: 现有的mitmproxy代理基础
**里程碑**: Milestone 1 - 基础代理能力完成 