# 客户端授权与证书安装

## 任务描述
本任务旨在解决项目核心瓶颈：由于缺失证书安装流程和跨平台兼容性，应用无法捕获HTTPS流量，导致核心功能失效。目标是重构 `electron/permission-manager.js`，实现一个健壮、跨平台、用户友好的授权与设置流程，能同时处理**系统代理设置**和**根证书安装/信任**两项关键操作。

## 验收标准
- [ ] 应用能够成功捕获并解密主流网站（如 Bilibili, YouTube）的 HTTPS 流量。
- [ ] 授权与安装流程能够在 macOS、Windows 和主流 Linux (Ubuntu/Debian) 上正常工作。
- [ ] 用户在首次设置时，能够通过清晰的原生密码提示框完成授权，尽可能减少手动操作。
- [ ] 应用能够正确地安装/卸载证书，并启用/禁用系统代理。

## 任务分解
### 阶段1：证书管理核心逻辑
- [ ] **步骤1.1**：在 `permission-manager.js` 中实现 `installCertificate(certPath)` 方法，包含各操作系统的证书安装命令 (macOS, Windows, Linux)。
- [ ] **步骤1.2**：实现 `uninstallCertificate()` 方法，用于在卸载或重置时清理证书。
- [ ] **步骤1.3**：实现 `checkCertificateStatus()` 方法，用于检查我们的根证书是否已安装并被信任。
- [ ] **步骤1.4**：将证书状态 `certificateInstalled` 集成到 `loadPermissionState` 和 `savePermissionState` 中，实现状态持久化。

### 阶段2：实现跨平台提权
- [ ] **步骤2.1**：将现有的 macOS 提权逻辑 (AppleScript) 封装成独立的模块。
- [ ] **步骤2.2**：为 Windows 实现提权逻辑 (可研究使用 PowerShell 的 `Start-Process -Verb runAs` 或 `sudo-prompt` 库)。
- [ ] **步骤2.3**：为 Linux 实现提权逻辑 (使用 `pkexec` 或 `gksudo` 调用图形化密码输入框)。
- [ ] **步骤2.4**：创建一个统一的 `runAsAdmin(command)` 包装函数，该函数能自动检测当前操作系统并调用对应的提权方法。

### 阶段3：整合与流程优化
- [ ] **步骤3.1**：在后端 `proxy_capture.py` 或相关API中，增加一个 `GET /api/proxy/certificate` 端点，用于提供 `mitmproxy` 根证书文件的内容或路径。
- [ ] **步骤3.2**：在 Electron 主进程中，实现一个 `getCertificate` 函数，该函数调用后端API，获取证书并将其保存到本地临时目录。
- [ ] **步骤3.3**：创建一个统一的 `requestInitialSetup()` 引导函数，在用户首次启动应用时，按顺序执行：① 获取证书 -> ② 请求权限并安装证书 -> ③ 请求权限并设置代理。
- [ ] **步骤3.4**：更新前端UI，清晰地向用户展示证书和代理的安装状态，并在需要时引导用户完成设置。

## 进度追踪
| 完成百分比 | 状态更新 |
|------------|---------|
| 0% | 任务创建，计划已制定。 |

## 问题与风险
- [ ] **问题1**：Electron客户端目前无法直接获取 `mitmproxy` 的证书路径。
    - **解决方案**：通过阶段3.1和3.2，由后端提供API，客户端下载并使用。
- [ ] **风险1**：不同Linux发行版的证书管理方式有差异。
    - **缓解措施**：初期先支持最主流的Debian/Ubuntu系，并在文档中注明其他系统的可能操作。
- [ ] **风险2**：Windows上的杀毒软件或安全策略可能会阻止证书安装或代理修改。
    - **缓解措施**：在授权流程中向用户提供清晰的说明，解释应用正在进行的操作及其原因，增加用户信任。

## 备注
此任务是当前项目的最高优先级，它的完成将解锁产品的核心价值。 