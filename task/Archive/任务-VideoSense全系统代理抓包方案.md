# VideoSense全系统代理抓包方案

## 📋 项目概述

基于res-downloader的成功经验，重新设计VideoSense为全系统级音视频资源抓取工具，实现"一次安装，终身使用"的用户体验。

**参考项目**: [res-downloader](https://github.com/putyy/res-downloader) - 7.4k stars的成功案例

## 🎯 目标用户体验

```
安装时：一键安装 → 自动证书 → 获取权限 ✅
使用时：点击启动 → 打开任意应用 → 自动抓取 ✅  
停止时：点击停止 → 自动恢复 → 其他工具正常 ✅
```

## 🏗️ 技术架构设计

### 核心组件

```
VideoSense客户端
├── 证书管理器 (Certificate Manager)
├── 系统代理管理器 (System Proxy Manager) 
├── 智能冲突检测器 (Conflict Detector)
├── 代理服务器 (Proxy Server)
├── 资源筛选器 (Resource Filter)
├── AI转录引擎 (Transcription Engine)
└── 用户界面 (UI)
```

### 工作流程

```mermaid
graph TD
    A[启动VideoSense] --> B[检测现有代理]
    B --> C{发现Clash Pro?}
    C -->|是| D[启用代理链模式]
    C -->|否| E[启用直接模式]
    D --> F[设置系统代理到VideoSense]
    E --> F
    F --> G[开始抓取所有应用流量]
    G --> H[智能筛选音视频资源]
    H --> I[展示给用户]
    I --> J[用户选择转录]
```

## 📋 实施计划

### Phase 1: 核心代理引擎 (2周)

#### 1.1 证书管理系统
- [ ] **自动证书生成和安装**
  - 生成VideoSense自签名根证书
  - macOS: 自动安装到系统钥匙串
  - Windows: 自动安装到证书存储
  - Linux: 自动添加到系统证书目录
  - 支持静默安装和用户确认两种模式

- [ ] **证书生命周期管理**
  - 证书有效期检查和自动续期
  - 卸载时完全清理证书
  - 证书损坏时自动修复

#### 1.2 系统代理管理器
- [ ] **跨平台代理设置**
  - macOS: networksetup命令行工具
  - Windows: 注册表修改 + WinINet API
  - Linux: 环境变量 + GNOME/KDE设置
  - 支持HTTP/HTTPS/SOCKS代理设置

- [ ] **代理状态管理**
  - 启动时保存原始代理设置
  - 运行时维护VideoSense代理
  - 停止时完整恢复原始设置
  - 异常退出时的自动恢复机制

#### 1.3 智能冲突检测器
- [ ] **翻墙工具检测**
  - Clash Pro: 端口扫描 + 配置文件检测
  - V2Ray: 进程检测 + 端口检测
  - Shadowsocks: 配置文件 + 进程检测
  - Surge/Quantumult: macOS特定检测
  - 通用代理检测算法

- [ ] **冲突解决策略**
  - 自动选择最佳工作模式
  - 代理链模式：VideoSense → 翻墙工具 → 互联网
  - 并行模式：智能路由不同类型流量
  - 用户手动选择模式

### Phase 2: 资源抓取引擎 (3周)

#### 2.1 高性能代理服务器
- [ ] **基于mitmproxy的定制化代理**
  - 异步处理提升性能
  - 内存优化减少资源占用
  - 支持HTTP/HTTPS/WebSocket
  - 支持HTTP/2和HTTP/3

- [ ] **智能流量路由**
  - 目标应用识别：User-Agent + 进程名称
  - 媒体流量优先处理
  - 非媒体流量快速转发
  - 实时性能监控

#### 2.2 智能资源识别
- [ ] **多层次资源检测**
  - URL模式匹配 (*.mp4, *.m3u8, etc.)
  - Content-Type分析
  - 文件头魔数检测
  - 平台特征识别

- [ ] **平台适配器**
  - 微信小程序：特殊协议解析
  - 抖音/快手：APP专用协议
  - 视频号：加密流解密
  - B站：多清晰度流处理
  - YouTube：DASH/HLS流合并

#### 2.3 资源管理系统
- [ ] **资源去重和分类**
  - URL规范化去重
  - 智能分类：视频/音频/直播
  - 质量评估：分辨率/码率/时长
  - 重复资源合并

- [ ] **下载管理器**
  - 多线程下载引擎
  - 断点续传支持
  - 下载进度实时显示
  - 自动重试和错误处理

### Phase 3: 用户界面优化 (2周)

#### 3.1 现代化UI界面
- [ ] **系统状态面板**
  - 代理服务状态实时显示
  - 冲突检测结果展示
  - 网络流量统计
  - 性能指标监控

- [ ] **资源管理界面**
  - 实时资源发现列表
  - 智能分类和过滤
  - 批量操作支持
  - 详细资源信息展示

#### 3.2 一键操作体验
- [ ] **智能启动按钮**
  - 单击启动全自动配置
  - 状态指示灯：绿色运行/红色停止/黄色冲突
  - 启动进度条和状态提示
  - 错误时自动诊断和修复建议

- [ ] **快捷操作菜单**
  - 系统托盘快速控制
  - 快捷键支持
  - 右键菜单常用功能
  - 开机自启动选项

### Phase 4: AI转录集成 (1周)

#### 4.1 无缝转录体验
- [ ] **一键转录功能**
  - 资源列表直接点击转录
  - 批量转录支持
  - 转录进度实时显示
  - 转录历史管理

- [ ] **智能音频提取**
  - 视频文件自动音频提取
  - 直播流实时音频捕获
  - 音频格式自动转换
  - 音频质量优化

## 🔧 技术实现细节

### 证书安装策略
```bash
# macOS
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain videosense.crt

# Windows  
certutil -addstore "Root" videosense.crt

# Linux
sudo cp videosense.crt /usr/local/share/ca-certificates/
sudo update-ca-certificates
```

### 系统代理设置
```bash
# macOS
networksetup -setwebproxy "Wi-Fi" 127.0.0.1 8899
networksetup -setsecurewebproxy "Wi-Fi" 127.0.0.1 8899

# Windows
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer /d "127.0.0.1:8899"
```

### 智能代理链配置
```yaml
# 检测到Clash Pro时的配置
upstream_proxy: "http://127.0.0.1:65327"
mode: "upstream"
routing_rules:
  - match: "media"
    action: "capture"
  - match: "other" 
    action: "passthrough"
```

## 📊 成功指标

### 用户体验指标
- [ ] **安装成功率 > 95%**: 各平台证书自动安装成功
- [ ] **启动成功率 > 98%**: 代理服务正常启动
- [ ] **冲突解决率 > 90%**: 与翻墙工具共存成功
- [ ] **资源检测准确率 > 85%**: 有效音视频资源识别

### 性能指标  
- [ ] **启动时间 < 3秒**: 从点击到代理服务运行
- [ ] **内存占用 < 100MB**: 运行时资源消耗
- [ ] **CPU占用 < 5%**: 空闲时处理器使用率
- [ ] **网络延迟 < 50ms**: 代理转发延迟

### 兼容性指标
- [ ] **应用覆盖率 > 95%**: 主流应用正常工作
- [ ] **系统兼容性 100%**: Windows 10+, macOS 11+, Ubuntu 20+
- [ ] **翻墙工具兼容 > 80%**: 主流翻墙工具共存

## 🚨 风险评估和缓解

### 高风险项
1. **证书安装失败**
   - 风险：用户无法使用HTTPS抓包
   - 缓解：提供手动安装指南，多种安装方式

2. **系统代理冲突**
   - 风险：与现有工具冲突导致网络中断
   - 缓解：智能检测，优雅回退，用户确认机制

3. **权限获取失败**
   - 风险：无法修改系统网络设置
   - 缓解：清晰的权限说明，替代方案

### 中风险项
1. **性能影响**
   - 风险：代理转发影响网络性能
   - 缓解：性能优化，用户可选择性启用

2. **应用兼容性**
   - 风险：部分应用无法正常工作
   - 缓解：白名单机制，用户反馈快速修复

## 📅 开发时间线

```
Week 1-2: 证书管理 + 系统代理管理
Week 3-4: 智能冲突检测 + 代理服务器基础
Week 5-6: 资源识别引擎 + 平台适配器
Week 7-8: 性能优化 + 用户界面
Week 9: AI转录集成 + 测试
Week 10: 文档 + 发布准备
```

## 🎯 里程碑验收标准

### Milestone 1: 基础代理能力 (Week 2)
- ✅ 自动证书安装在三个平台工作
- ✅ 系统代理设置和恢复正常
- ✅ 基础HTTP/HTTPS代理转发功能

### Milestone 2: 智能冲突处理 (Week 4)  
- ✅ 检测Clash Pro等主流翻墙工具
- ✅ 代理链模式正常工作
- ✅ 启停时不影响现有网络工具

### Milestone 3: 资源抓取能力 (Week 6)
- ✅ 识别主流平台音视频资源
- ✅ 支持微信小程序、浏览器、客户端
- ✅ 资源去重和分类功能

### Milestone 4: 用户体验完整 (Week 8)
- ✅ 一键启动停止功能
- ✅ 直观的状态显示界面
- ✅ 错误处理和用户提示

### Final: 生产就绪 (Week 10)
- ✅ 完整的安装和卸载流程
- ✅ 用户文档和故障排除指南
- ✅ 全平台测试通过

## 📝 后续优化方向

1. **更多平台支持**: Android、iOS（越狱）
2. **云端AI能力**: 更强大的转录和翻译
3. **团队协作**: 资源共享和批量处理
4. **插件系统**: 第三方平台适配
5. **性能监控**: 详细的使用统计和优化

---

**创建时间**: 2024年12月
**负责人**: VideoSense开发团队
**优先级**: P0 (最高优先级)
**预期完成**: 10周内完成MVP版本 