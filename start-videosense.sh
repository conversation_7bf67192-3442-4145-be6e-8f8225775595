#!/bin/bash

# VideoSense 完整启动脚本
# 包含证书安装测试和项目启动

echo "🚀 VideoSense 启动脚本"
echo "======================"

# 检查系统平台
PLATFORM=$(uname)
echo "📋 检测到系统: $PLATFORM"

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查 Python
if ! command -v python &> /dev/null; then
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 未安装，请先安装 Python"
        exit 1
    else
        PYTHON=python3
    fi
else
    PYTHON=python
fi

echo "✅ Python 版本: $($PYTHON --version)"

# 函数：启动后端
start_backend() {
    echo "📦 启动后端服务..."
    cd Project/backend
    
    # 检查依赖
    if [ ! -d "venv" ]; then
        echo "📥 创建虚拟环境..."
        $PYTHON -m venv venv
    fi
    
    # 激活虚拟环境
    if [ "$PLATFORM" = "Windows_NT" ]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    # 安装依赖
    echo "📥 安装 Python 依赖..."
    pip install -r requirements.txt 2>/dev/null || echo "⚠️ requirements.txt 不存在，跳过依赖安装"
    
    # 启动后端
    echo "🔄 启动后端服务 (端口 8000)..."
    $PYTHON main.py &
    BACKEND_PID=$!
    
    cd ..
    
    # 等待后端启动
    echo "⏳ 等待后端服务启动..."
    sleep 3
    
    # 检查后端是否启动成功
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 后端服务启动成功"
        return 0
    else
        echo "❌ 后端服务启动失败"
        return 1
    fi
}

# 函数：启动前端
start_frontend() {
    echo "🌐 启动前端服务..."
    cd Project/frontend
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "📥 安装前端依赖..."
        npm install
    fi
    
    # 启动前端
    echo "🔄 启动前端开发服务 (端口 3000)..."
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
    
    # 等待前端启动
    echo "⏳ 等待前端服务启动..."
    sleep 5
    
    return 0
}

# 函数：启动 Electron
start_electron() {
    echo "⚡ 启动 Electron 客户端..."
    cd Project/electron
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "📥 安装 Electron 依赖..."
        npm install
    fi
    
    # 启动 Electron
    echo "🔄 启动 Electron 应用..."
    npm start &
    ELECTRON_PID=$!
    
    cd ..
    return 0
}

# 函数：测试证书安装
test_certificate() {
    echo ""
    echo "🔒 证书安装测试"
    echo "==============="
    echo "💡 这将测试 HTTPS 抓包所需的证书安装功能"
    echo ""
    
    read -p "是否要测试证书安装？(y/N): " -n 1 -r
    echo
    
    echo "⏭️ 暂时跳过证书测试"
}

# 函数：清理进程
cleanup() {
    echo ""
    echo "🧹 清理进程..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo "🔄 后端服务已停止"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo "🔄 前端服务已停止"
    fi
    
    if [ ! -z "$ELECTRON_PID" ]; then
        kill $ELECTRON_PID 2>/dev/null || true
        echo "🔄 Electron 应用已停止"
    fi
    
    echo "✅ 清理完成"
    exit 0
}

# 捕获退出信号
trap cleanup SIGINT SIGTERM

# 主流程
main() {
    echo ""
    echo "🎯 VideoSense 跨平台 HTTPS 抓包工具"
    echo "🔧 解决方案已实施：支持 macOS/Windows/Linux"
    echo ""
    
    # 证书测试
    test_certificate
    
    # 启动服务
    echo "🚀 开始启动 VideoSense 服务..."
    echo ""
    
    # 启动后端
    if start_backend; then
        echo ""
        # 启动前端
        start_frontend
        echo ""
        # 启动 Electron
        start_electron
        echo ""
        
        echo "🎉 VideoSense 已启动完成!"
        echo ""
        echo "📋 服务状态:"
        echo "  • 后端服务: http://localhost:8000"
        echo "  • 前端服务: http://localhost:3000"
        echo "  • Electron 客户端: 正在运行"
        echo ""
        echo "💡 使用说明:"
        echo "  1. 在 Electron 客户端中点击 '高级设置' → '安装证书'"
        echo "  2. 启动代理抓包功能"
        echo "  3. 在浏览器中播放音视频即可自动抓包"
        echo ""
        echo "📝 注意: 按 Ctrl+C 可安全停止所有服务"
        echo ""
        
        # 等待用户输入或信号
        wait
    else
        echo "❌ 后端服务启动失败，请检查 Python 环境和依赖"
        exit 1
    fi
}

# 执行主流程
main 