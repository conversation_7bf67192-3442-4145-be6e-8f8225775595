# VideoSense 项目根目录

这里是VideoSense项目的根目录，采用清晰的模块化目录结构。

## 📁 目录结构

```
video_note/
├── Project/                    # 📦 主要开发代码
│   ├── frontend/              # 🌐 Next.js前端界面
│   ├── backend/               # ⚙️ FastAPI后端服务
│   ├── electron/              # 💻 Electron桌面客户端
│   ├── shared/                # 🔗 共享类型定义和常量
│   ├── scripts/               # 🛠️ 构建和部署脚本
│   └── README.md              # 📖 项目技术文档
│
├── task/                      # 📋 项目管理
│   ├── 任务-VideoSense项目总览.md
│   ├── 任务-智能代理与代理链实现.md
│   └── README.md
│
├── prd/                       # 📋 产品文档
│   └── VideoSense-PRD-v1.0.0.md
│
├── Archive/                   # 📁 归档内容
│   └── videosense-prototype/  # 早期原型代码
│
├── .promptx/                  # 🤖 PromptX配置（如果存在）
├── start-videosense.sh       # 🚀 项目启动脚本
└── README.md                  # 📖 本文件
```

## 🚀 快速开始

1. **克隆并进入项目**
   ```bash
   git clone <repository-url>
   cd video_note
   ```

2. **一键启动开发环境**
   ```bash
   ./start-videosense.sh
   ```

3. **查看技术文档**
   ```bash
   cd Project && cat README.md
   ```

## 📖 文档导航

### 🎯 产品相关
- [产品需求文档](./prd/VideoSense-PRD-v1.0.0.md) - 详细的产品功能和需求规格
- [项目总览](./task/任务-VideoSense项目总览.md) - 项目进度和模块状态

### 🔧 技术相关
- [项目技术文档](./Project/README.md) - 详细的技术架构和开发指南
- [智能代理实现](./task/任务-智能代理与代理链实现.md) - 核心技术突破文档

## 💡 项目特色

- **🔒 隐私安全**: 纯客户端架构，所有处理都在本地完成
- **🧠 AI驱动**: 集成先进的语音识别技术
- **🌐 智能代理**: 与翻墙工具完美兼容的代理模式
- **📱 跨平台**: 支持 macOS/Windows/Linux 全平台

## 🎯 项目状态

VideoSense目前处于**功能完善阶段**，核心功能已实现并可正常使用：

- ✅ **智能抓包模块** (90%)
- ✅ **AI转录模块** (100%)
- 🔄 **桌面客户端** (80%)
- ✅ **前端界面** (100%)

## 🤝 贡献

欢迎提交Issue和Pull Request！详细的贡献指南请查看[项目技术文档](./Project/README.md)。

## 📄 许可证

MIT License - 详见 LICENSE 文件 