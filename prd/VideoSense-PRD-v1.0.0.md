# **产品需求文档：VideoSense - V1.0**

## 1. 修订历史
| 版本号 | 修订日期 | 修订人 | 修订内容 |
| :--- | :--- | :--- | :--- |
| V1.0 | 2025-06-19 | 产品经理 (AI) | 基于深度讨论创建初稿，明确了客户端架构、核心功能、风险规避及未来规划。 |

## 2. 项目背景与目标
### 2.1 核心问题
在数字化学习和内容创作的时代，用户（如知识工作者、学生、研究者、内容创作者）面临着从海量音视频内容中高效获取信息并将其转化为个人知识资产的巨大挑战。手动整理1小时的音视频通常耗时数小时，过程枯燥且效率低下，严重阻碍了知识的吸收和再创造。

### 2.2 产品定位与架构
VideoSense是一款运行在用户个人电脑上的**客户端智能笔记工具**。它通过"文件转录"和创新的"播放即录"功能，帮助用户将音视频内容一键转化为结构化的文字，并提供强大的编辑和二次创作能力。

**核心架构决策**：为最大化规避版权风险并保护用户隐私，本产品采用纯**客户端架构**。所有的数据抓取、转换和处理均在用户本地完成，我方服务器不触碰、不存储任何用户的原始媒体内容。

### 2.3 需求目标
1.  **体验目标**：提供"一键式"的流畅体验，将用户从繁琐的下载和手动整理中解放出来。
2.  **功能目标**：实现对本地文件和流媒体（视频/音频）的稳定转录，并提供高效的文本编辑和管理功能。
3.  **合规目标**：通过产品设计、用户告知和法律条款，建立坚固的风险"防火墙"，确保产品在国际市场（尤其日本、北美）的长期、合规运营。
4.  **商业目标**：通过免费提供强大的基础功能吸引用户，为未来围绕"内容价值提炼"的增值服务和商业化打下基础。

## 3. 市场与用户
### 3.1 目标用户
- **学生/研究者**: 需要回顾和整理在线课程、网络研讨会、访谈等视频内容，快速提取要点和参考文献。
- **内容创作者**: 从大量视频素材中快速寻找灵感，或将已有视频内容转化为文字稿。
- **任何需要从视频中高效获取信息的人**: 避免在长时间的视频中反复拖动进度条，快速消化信息。

### 3.2 首发市场策略
* **优先市场**: 日本。鉴于其《著作权法》对"信息解析"用途的豁免，该市场法律环境相对有利，适合作为产品冷启动和模式验证的首选。
* **产品本地化**: V1版本需优先支持日语UI和高精度的日语语音识别模型。

## 4. 功能需求详述
### 4.1 媒体输入模块
#### 4.1.1 文件转录
* **作为** 用户，**我希望** 能直接将电脑里的音视频文件拖入应用，**以便于** 快速转录我已有的材料。
* **业务规则**:
    1.  界面提供清晰的拖拽区域或"选择文件"按钮。
    2.  支持主流格式：视频(MP4, MOV, MKV)，音频(MP3, M4A, WAV)。

#### 4.1.2 "播放即录"
* **作为** 用户，**我希望** 应用能自动检测我正在播放的媒体，让我一键转录，**以便于** 无缝地记录和学习。
* **业务规则**:
    1.  应用在后台检测系统中的媒体流播放（视频和音频/播客）。
    2.  所有检测到的可处理媒体源，将以列表形式呈现在"待处理列表"中，每项均带一个复选框。
    3.  用户可勾选一个或多个媒体源，然后点击"开始转录"进行批量处理。

#### 4.1.3 首次使用授权 (关键合规功能)
* **作为** 首次使用"播放即录"的用户，**我必须** 在一个强制弹窗中阅读并同意相关条款，**以便于** 我明确知晓我的责任和功能风险。
* **业务规则**:
    1.  **触发**: 首次点击"开始转录"时，触发模态对话框。
    2.  **内容**: 清晰告知用户"功能原理"、"用户责任"、"工具定位"，并要求用户勾选"我已阅读、理解并同意..."后方可继续。（详细文案见附录A）
    3.  **行为**: 用户同意后，此弹窗不再出现；若取消，则转录中止。

### 4.2 任务管理模块
#### 4.2.1 任务队列管理器
* **作为** 用户，**我希望** 能看到所有转录任务的实时状态，并能管理它们，**以便于** 我对处理过程有清晰的掌控。
* **业务规则**:
    1.  所有启动的转录任务进入"任务队列"，并实时展示其状态。
    2.  **任务状态**定义为：排队中、下载中、转码中、转录中、完成、失败。
    3.  **任务取消逻辑**:
        * 当任务处于"**下载中**"状态时，用户**可以**取消。
        * 一旦任务进入"**转码中**"或"**转录中**"状态，**不可取消**，以匹配未来的成本核算。

### 4.3 内容输出与管理模块
#### 4.3.1 内容编辑器
* **作为** 用户，**我希望** 在一个功能强大的编辑器中查看和整理文稿，**以便于** 提升内容处理效率。
* **业务规则**:
    1.  **基础编辑**: 支持有限度的Markdown语法（加粗、标题、列表等）和标准的"查找与替换"功能。
    2.  **时间戳**: (成本可控前提下) 需在每段文字旁展示对应的时间戳，方便定位。
    3.  **发言人识别**: (若AI模型支持) 需自动区分并标记不同发言人（如 `发言人A:`）。
    4.  **AI工具箱**: 预留"AI工具箱"的UI位置，为未来上线"提炼金句"、"生成摘要"等增值功能做准备。

#### 4.3.2 历史记录
* **作为** 用户，**我希望** 应用能自动保存我所有完成的转录，**以便于** 我随时回顾和查找。
* **业务规则**:
    1.  所有成功完成的转录任务，其结果将自动存入"历史记录"。
    2.  **【关键约束】**: 历史记录**仅保存纯文本内容**和标题、日期等元数据，**绝不保存或链接到原始音视频文件**。
    3.  用户可从历史记录中加载、编辑或导出任何一篇文稿。

## 5. 异常与边缘情况处理
* **网络中断**: "下载中"的任务在网络中断后，应自动尝试续传3次，失败后标记为"失败"并允许用户手动重试。
* **API错误**: 建立错误映射机制，向用户展示如"AI服务超时"等可理解的错误提示，而非技术代码。
* **超长内容**: 对超过60分钟的媒体，后台自动分割为多个不超过60分钟的块进行处理。每个块按独立任务计费，但前端需将结果无缝拼接为一篇完整文稿。
* **应用退出**: 若有任务在执行，退出前弹窗提示用户选择[全部暂停]或[取消可取消的任务]。若有暂停的任务，下次启动时提示用户是否恢复。

## 6. 非功能性需求
* **法律合规**:
    * 应用必须在用户协议(ToS)中明确用户责任与赔偿条款。
    * 所有营销宣传材料必须将产品定位为"学习/研究工具"，严禁使用"下载器"等暗示性词汇。
* **安全性**:
    * "播放即录"功能的技术实现，绝不能包含任何对DRM等技术保护措施的破解或规避行为。
* **性能**:
    * 应用在后台检测时不应过度占用系统资源。
    * 转录处理流程（下载、转码、AI请求）应尽可能高效。

## 7. 数据指标 (V1)
* **激活与留存**: 日活跃用户(DAU)、次日/7日留存率。
* **功能使用**: "文件转录"与"播放即录"功能的使用次数比例。
* **核心转化**: "任务创建-任务成功"的转化率。
* **技术质量**: 任务失败率（区分网络、API、格式等原因）。

---
### **附录A："首次使用授权"对话框文案**
```
标题：重要提示 (Important Notice)

正文：
为了帮您更好地学习和研究，VideoSense的"播放即录"功能会在您的电脑上对正在播放的内容进行临时处理。

在使用前，请您理解并确认以下几点：

• **您是内容的主人**: 您需要确保拥有处理这些内容的合法权利，或者您的使用行为符合您所在地区的"合理使用"或"信息解析"等法律规定。
• **我们是工具，您是作者**: VideoSense仅作为您的个人工具，所有操作均由您发起。您需要为您的使用行为承担全部责任。
• **用后即焚，保护隐私**: 处理完成后，所有临时文件都将被立即删除，我们不会保存您的任何视频内容。

更详细的信息，请查阅我们的《服务条款》。

勾选框：[ ] 我已阅读、理解并同意以上条款，愿意承担相关责任

按钮：[ 取消 ]  [ 同意并继续 (勾选后激活) ]
```