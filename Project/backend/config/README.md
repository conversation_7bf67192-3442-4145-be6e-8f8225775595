# VideoSense API配置指南

## 🔐 安全配置 SiliconFlow API

VideoSense使用[SiliconFlow](https://docs.siliconflow.cn/cn/api-reference/audio/create-audio-transcriptions)提供的语音转文本服务。

### 1. 获取API密钥

1. 访问 [SiliconFlow官网](https://siliconflow.cn) 注册账户
2. 在控制台中获取API密钥
3. 确保账户有足够的余额使用语音转文本服务

### 2. 配置API密钥

#### 方法一：配置文件（推荐）

1. 复制示例配置文件：
```bash
cd backend/config
cp api_keys.example.py api_keys.py
```

2. 编辑 `api_keys.py` 文件，填入真实的API密钥：
```python
# SiliconFlow 语音转文本API配置
SILICONFLOW_API_KEY = "sk-你的真实API密钥"
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
SILICONFLOW_MODEL = "FunAudioLLM/SenseVoiceSmall"
```

#### 方法二：环境变量（备选）

在启动服务前设置环境变量：
```bash
export SILICONFLOW_API_KEY="sk-你的真实API密钥"
export SILICONFLOW_BASE_URL="https://api.siliconflow.cn/v1"
export SILICONFLOW_MODEL="FunAudioLLM/SenseVoiceSmall"
```

### 3. 验证配置

启动后端服务后，访问以下端点验证配置：

- **状态检查**: `GET /api/transcription/status`
- **配置验证**: `POST /api/transcription/validate-config`
- **健康检查**: `GET /api/transcription/health`

### 4. 安全注意事项

⚠️ **重要安全提示**：

1. **绝不要将API密钥提交到Git**
   - `api_keys.py` 已被添加到 `.gitignore`
   - 请勿在任何公开场合分享密钥

2. **密钥管理最佳实践**
   - 定期更换API密钥
   - 限制密钥访问权限
   - 监控API使用量

3. **生产环境部署**
   - 使用环境变量而非配置文件
   - 使用密钥管理服务（如AWS Secrets Manager）
   - 启用HTTPS加密传输

### 5. 故障排除

#### 配置未生效
- 检查 `api_keys.py` 文件是否存在
- 确认密钥格式正确（以`sk-`开头）
- 重启后端服务

#### API调用失败
- 检查网络连接
- 验证账户余额
- 查看服务日志获取详细错误信息

#### Mock模式
如果未配置API密钥，系统将自动使用Mock模式：
- 返回示例转录文本
- 不消耗API配额
- 适用于开发和测试

### 6. API使用限制

根据SiliconFlow文档，请注意：
- 最大文件大小：50MB
- 支持格式：WAV, MP3, M4A, FLAC等
- 状态码：200(成功), 400(无效请求), 401(未授权), 429(限流), 503(服务不可用)

### 7. 联系支持

- SiliconFlow文档：https://docs.siliconflow.cn
- VideoSense项目：详见根目录README.md 