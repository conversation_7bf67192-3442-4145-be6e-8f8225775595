# API配置示例文件
# 复制此文件为 api_keys.py 并填入真实的API密钥
# 注意：api_keys.py 已被添加到 .gitignore 中，不会被提交到git

# SiliconFlow 语音转文本API配置
SILICONFLOW_API_KEY = "sk-dbxpveacblrkiekydtbuajklmotbnmrjpaoovcmhtulnipli"
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
SILICONFLOW_MODEL = "FunAudioLLM/SenseVoiceSmall"

# 其他API配置可以添加在这里
# OPENAI_API_KEY = "your_openai_api_key_here"
# GOOGLE_API_KEY = "your_google_api_key_here"

# 安全提示：
# 1. 请勿在任何公开场合分享此文件内容
# 2. 请勿将真实API密钥提交到版本控制系统
# 3. 定期更换API密钥以确保安全
# 4. 使用强密钥和安全的存储方式 