"""
安全的API客户端管理器
提供统一的API密钥管理和请求处理
"""
import os
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class APIConfig:
    """API配置管理类"""
    
    def __init__(self):
        self._api_keys = {}
        self._load_config()
    
    def _load_config(self):
        """安全加载API配置"""
        try:
            # 尝试导入真实的API密钥配置
            from .api_keys import (
                SILICONFLOW_API_KEY,
                SILICONFLOW_BASE_URL,
                SILICONFLOW_MODEL
            )
            
            self._api_keys = {
                'siliconflow': {
                    'api_key': SILICONFLOW_API_KEY,
                    'base_url': SILICONFLOW_BASE_URL,
                    'model': SILICONFLOW_MODEL
                }
            }
            
            # 验证API密钥是否为示例值
            if SILICONFLOW_API_KEY == "your_siliconflow_api_key_here":
                logger.warning("检测到示例API密钥，请配置真实的API密钥")
                
        except ImportError:
            logger.error(
                "未找到API密钥配置文件。请复制 api_keys.example.py 为 api_keys.py 并配置真实的API密钥"
            )
            # 使用环境变量作为备选方案
            self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置（备选方案）"""
        self._api_keys = {
            'siliconflow': {
                'api_key': os.getenv('SILICONFLOW_API_KEY', ''),
                'base_url': os.getenv('SILICONFLOW_BASE_URL', 'https://api.siliconflow.cn/v1'),
                'model': os.getenv('SILICONFLOW_MODEL', 'FunAudioLLM/SenseVoiceSmall')
            }
        }
    
    def get_siliconflow_config(self) -> Dict[str, str]:
        """获取SiliconFlow API配置"""
        config = self._api_keys.get('siliconflow', {})
        if not config.get('api_key'):
            raise ValueError("SiliconFlow API密钥未配置")
        return config
    
    def validate_api_key(self, service: str) -> bool:
        """验证API密钥是否有效"""
        config = self._api_keys.get(service, {})
        api_key = config.get('api_key', '')
        
        # 检查是否为空或示例值
        if not api_key or api_key.startswith('your_') or 'example' in api_key.lower():
            return False
            
        return True

# 全局配置实例
api_config = APIConfig() 