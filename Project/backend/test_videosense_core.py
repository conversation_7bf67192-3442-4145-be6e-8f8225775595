#!/usr/bin/env python3
"""
VideoSense 核心功能测试脚本
测试统一接口的各项功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.core.videosense_core import get_videosense_core, VideoSenseConfig, ProxyMode
from app.core.proxy_detection_engine import get_proxy_detection_engine


async def test_proxy_detection():
    """测试代理检测功能"""
    print("=" * 60)
    print("🔍 测试代理检测功能")
    print("=" * 60)
    
    try:
        engine = get_proxy_detection_engine()
        proxies = await engine.detect_all_proxies()
        
        print(f"✅ 检测到 {len(proxies)} 个代理软件:")
        for proxy in proxies:
            print(f"  📡 {proxy.name} - {proxy.host}:{proxy.port} (置信度: {proxy.confidence:.2f})")
            print(f"     状态: {proxy.status.value}, 类型: {proxy.type.value}")
            if proxy.pid:
                print(f"     进程: {proxy.process_name} (PID: {proxy.pid})")
            print()
        
        # 获取建议
        recommendations = engine.get_proxy_recommendations()
        print("💡 代理配置建议:")
        print(f"  推荐模式: {recommendations['recommended_mode']}")
        if recommendations['suggested_chain']:
            chain = recommendations['suggested_chain']
            print(f"  建议代理链: VideoSense -> {chain['proxy_name']} ({chain['upstream_host']}:{chain['upstream_port']})")
        
        for warning in recommendations['warnings']:
            print(f"  ⚠️  {warning}")
        
    except Exception as e:
        print(f"❌ 代理检测测试失败: {e}")


async def test_videosense_core():
    """测试VideoSense核心功能"""
    print("=" * 60)
    print("🚀 测试VideoSense核心功能")
    print("=" * 60)
    
    try:
        # 创建配置
        config = VideoSenseConfig(
            proxy_port=8899,
            proxy_mode=ProxyMode.AUTO,
            auto_install_cert=False,  # 测试时不自动安装证书
            auto_start_proxy=False    # 测试时不自动启动代理
        )
        
        core = get_videosense_core(config)
        
        # 1. 测试初始化
        print("1️⃣ 测试环境初始化...")
        init_result = await core.initialize()
        
        if init_result["success"]:
            print("✅ 环境初始化成功")
            print(f"  平台: {init_result['data']['environment']['platform']}")
            print(f"  端口可用: {init_result['data']['environment']['proxy_port_available']}")
            print(f"  检测到冲突: {init_result['data']['conflicts']['conflict_count']} 个")
            print(f"  推荐模式: {init_result['data']['proxy_mode']['selected_mode']}")
        else:
            print(f"❌ 环境初始化失败: {init_result['message']}")
            return
        
        # 2. 测试状态获取
        print("\n2️⃣ 测试状态获取...")
        status_result = core.get_status()
        
        if status_result["success"]:
            print("✅ 状态获取成功")
            print(f"  当前状态: {status_result['data']['status']}")
            print(f"  代理端口: {status_result['data']['config']['proxy_port']}")
            print(f"  代理模式: {status_result['data']['config']['proxy_mode']}")
        else:
            print(f"❌ 状态获取失败: {status_result['message']}")
        
        # 3. 测试模拟启动（不实际启动）
        print("\n3️⃣ 测试配置验证...")
        print(f"  配置端口: {core.config.proxy_port}")
        print(f"  配置模式: {core.config.proxy_mode.value}")
        print(f"  自动证书: {core.config.auto_install_cert}")
        print(f"  自动代理: {core.config.auto_start_proxy}")
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_system_integration():
    """测试系统集成"""
    print("=" * 60)
    print("🔧 测试系统集成")
    print("=" * 60)
    
    try:
        from app.core.system_proxy_manager import SystemProxyManager
        from app.core.certificate_manager import CertificateManager
        
        # 测试系统代理管理器
        print("1️⃣ 测试系统代理管理器...")
        proxy_manager = SystemProxyManager()
        
        current_proxy = proxy_manager.get_current_proxy_settings()
        print(f"✅ 当前系统代理: {current_proxy.get('platform', 'unknown')}")
        
        # 测试增强检测
        enhanced_proxies = await proxy_manager.detect_proxy_software_enhanced()
        print(f"✅ 增强检测发现 {len(enhanced_proxies)} 个代理")
        
        # 测试证书管理器
        print("\n2️⃣ 测试证书管理器...")
        cert_manager = CertificateManager()
        
        cert_status = cert_manager.check_certificate_validity()
        print(f"✅ 证书状态检查完成: {cert_status}")
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    print("🧪 VideoSense 核心功能测试开始")
    print("时间:", asyncio.get_event_loop().time())
    
    # 运行各项测试
    await test_proxy_detection()
    await test_videosense_core()
    await test_system_integration()
    
    print("\n🎉 所有测试完成！")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc() 