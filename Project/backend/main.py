from fastapi import FastAPI, UploadFile, File, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import uvicorn
import tempfile
import os
import logging
from typing import List

# 导入应用模块
from app.core.database import create_tables, get_db
from app.models.history import TranscriptionHistory
from app.crud.history_crud import (
    create_history_record,
    get_history_record_by_id,
    get_all_history_records,
    delete_history_record_by_id,
    HistoryCreate
)
from app.services.transcription import get_transcription_service
from app.api.proxy_routes import router as proxy_router
from app.api.transcription import router as transcription_router
from app.api.certificate_routes import router as certificate_router
from app.api.enhanced_routes import router as enhanced_router

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- 初始化 ---
# 在应用启动时创建数据库表
try:
    create_tables()
except Exception as e:
    logger.error(f"应用启动时数据库初始化失败: {e}")
    # 在某些环境中，如果数据库文件损坏或权限有问题，这里可能会失败
    # 实际生产中可能需要更复杂的恢复逻辑
    # 此处简单退出以避免应用在不健康的状态下运行
    exit(1)

# --- FastAPI 应用实例 ---
app = FastAPI(
    title="VideoSense Backend",
    version="1.0.1",
    description="本地AI音视频转录服务的后端API"
)

# --- 中间件配置 ---
# 配置CORS以允许前端开发服务器的访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- 服务实例化 ---
# 获取转录服务单例
transcription_service = get_transcription_service()

# --- API 路由 ---
# 注册代理抓包路由
app.include_router(proxy_router)

# 注册转录路由
app.include_router(transcription_router)

# 注册证书管理路由
app.include_router(certificate_router)

# 注册VideoSense统一核心路由
from app.api.videosense_routes import router as videosense_router
app.include_router(videosense_router)

# 注册增强功能路由
app.include_router(enhanced_router)

@app.get("/", summary="根路径", tags=["通用"])
async def root():
    """返回服务的基本信息和状态。"""
    return {
        "message": "VideoSense Backend is running!",
        "status": "ok",
        "version": app.version
    }

@app.get("/health", summary="健康检查", tags=["通用"])
async def health_check():
    """用于外部监控的健康检查端点。"""
    return {"status": "healthy"}

@app.get("/history", summary="获取转录历史", tags=["历史记录"])
async def get_history(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """
    分页获取所有转录历史记录。
    """
    try:
        records, total_count = get_all_history_records(db, page=page, page_size=page_size)
        return {
            "success": True,
            "data": records,
            "pagination": {
                "total_items": total_count,
                "total_pages": (total_count + page_size - 1) // page_size,
                "current_page": page,
                "page_size": page_size
            }
        }
    except Exception as e:
        logger.error(f"获取历史记录时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取历史记录失败")

@app.get("/history/{history_id}", summary="获取单条历史详情", tags=["历史记录"])
async def get_history_detail(history_id: int, db: Session = Depends(get_db)):
    """
    根据ID获取单条转录历史的详细信息。
    """
    record = get_history_record_by_id(db, history_id)
    if not record:
        raise HTTPException(status_code=404, detail="找不到指定的历史记录")
    return {"success": True, "data": record}

@app.delete("/history/{history_id}", summary="删除单条历史", tags=["历史记录"])
async def delete_history(history_id: int, db: Session = Depends(get_db)):
    """
    根据ID删除一条转录历史。
    """
    success = delete_history_record_by_id(db, history_id)
    if not success:
        raise HTTPException(status_code=404, detail="找不到要删除的历史记录")
    return {"success": True, "message": "历史记录删除成功"}


# --- 主程序入口 ---
if __name__ == "__main__":
    logger.info("启动 VideoSense 后端服务...")
    # host="0.0.0.0" 让服务可以被局域网访问
    # reload=True 会在代码变动时自动重启服务，非常适合开发
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 