"""
VideoSense 核心管理器
提供统一的跨平台接口，整合证书管理和系统代理管理
实现"一键启动"的用户体验
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from .certificate_manager import CertificateManager
from .system_proxy_manager import SystemProxyManager
from .enhanced_proxy_conflict_resolver import EnhancedProxyConflictResolver
from .enhanced_certificate_manager import EnhancedCertificateManager

logger = logging.getLogger(__name__)


class VideoSenseStatus(Enum):
    """VideoSense运行状态"""
    STOPPED = "stopped"           # 已停止
    STARTING = "starting"         # 启动中
    RUNNING = "running"           # 运行中
    ERROR = "error"               # 错误状态
    STOPPING = "stopping"        # 停止中


class ProxyMode(Enum):
    """代理模式"""
    DIRECT = "direct"             # 直连模式
    CHAIN = "chain"               # 代理链模式
    AUTO = "auto"                 # 自动模式


@dataclass
class VideoSenseConfig:
    """VideoSense配置"""
    proxy_port: int = 8899
    proxy_mode: ProxyMode = ProxyMode.AUTO
    auto_start_proxy: bool = True
    auto_install_cert: bool = True
    backup_proxy_settings: bool = True
    restore_on_exit: bool = True


class VideoSenseCore:
    """
    VideoSense核心管理器
    
    功能：
    1. 统一管理证书和代理
    2. 提供一键启动/停止
    3. 智能检测和配置
    4. 跨平台兼容性
    """
    
    def __init__(self, config: VideoSenseConfig = None):
        """
        初始化VideoSense核心管理器
        
        Args:
            config: VideoSense配置
        """
        self.config = config or VideoSenseConfig()
        self.status = VideoSenseStatus.STOPPED
        
        # 初始化子管理器
        self.cert_manager = CertificateManager()
        self.proxy_manager = SystemProxyManager()

        # 初始化增强管理器
        self.enhanced_cert_manager = EnhancedCertificateManager()
        self.conflict_resolver = EnhancedProxyConflictResolver(self.config.proxy_port)
        
        # 运行时状态
        self.startup_time = None
        self.proxy_server_process = None
        self.original_proxy_settings = None
        self.detected_conflicts = []
        
        logger.info("VideoSense核心管理器初始化完成")
    
    async def initialize(self) -> Dict:
        """
        初始化VideoSense环境
        
        Returns:
            Dict: 初始化结果
        """
        logger.info("🚀 开始初始化VideoSense环境...")
        
        try:
            # 1. 环境检查
            env_check = await self._check_environment()
            if not env_check["success"]:
                return env_check
            
            # 2. 证书检查和设置
            cert_result = await self._setup_certificates()
            if not cert_result["success"]:
                return cert_result
            
            # 3. 代理冲突检测
            conflict_result = await self._detect_conflicts()
            
            # 4. 选择最佳代理模式
            mode_result = await self._select_proxy_mode()
            
            return {
                "success": True,
                "message": "VideoSense环境初始化成功",
                "data": {
                    "environment": env_check["data"],
                    "certificates": cert_result["data"],
                    "conflicts": conflict_result["data"],
                    "proxy_mode": mode_result["data"]
                }
            }
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return {
                "success": False,
                "message": f"初始化失败: {str(e)}",
                "error": str(e)
            }
    
    async def start(self) -> Dict:
        """
        一键启动VideoSense
        
        Returns:
            Dict: 启动结果
        """
        if self.status != VideoSenseStatus.STOPPED:
            return {
                "success": False,
                "message": f"VideoSense当前状态为 {self.status.value}，无法启动"
            }
        
        logger.info("🚀 启动VideoSense...")
        self.status = VideoSenseStatus.STARTING
        
        try:
            # 1. 初始化环境
            init_result = await self.initialize()
            if not init_result["success"]:
                self.status = VideoSenseStatus.ERROR
                return init_result
            
            # 2. 备份当前代理设置
            if self.config.backup_proxy_settings:
                backup_success = self.proxy_manager.backup_current_proxy()
                if not backup_success:
                    logger.warning("代理设置备份失败，但继续启动")
            
            # 3. 启动代理服务器
            proxy_result = await self._start_proxy_server()
            if not proxy_result["success"]:
                self.status = VideoSenseStatus.ERROR
                return proxy_result
            
            # 4. 配置系统代理
            if self.config.auto_start_proxy:
                system_proxy_result = await self._configure_system_proxy()
                if not system_proxy_result["success"]:
                    logger.warning("系统代理配置失败，但代理服务器已启动")
            
            # 5. 启动成功
            self.status = VideoSenseStatus.RUNNING
            self.startup_time = datetime.now()
            
            logger.info("✅ VideoSense启动成功")
            
            return {
                "success": True,
                "message": "VideoSense启动成功",
                "data": {
                    "status": self.status.value,
                    "startup_time": self.startup_time.isoformat(),
                    "proxy_port": self.config.proxy_port,
                    "proxy_mode": self.config.proxy_mode.value,
                    "certificate_status": self.cert_manager.check_certificate_validity()
                }
            }
            
        except Exception as e:
            logger.error(f"启动失败: {e}")
            self.status = VideoSenseStatus.ERROR
            return {
                "success": False,
                "message": f"启动失败: {str(e)}",
                "error": str(e)
            }
    
    async def stop(self) -> Dict:
        """
        停止VideoSense
        
        Returns:
            Dict: 停止结果
        """
        if self.status == VideoSenseStatus.STOPPED:
            return {
                "success": True,
                "message": "VideoSense已经停止"
            }
        
        logger.info("🛑 停止VideoSense...")
        self.status = VideoSenseStatus.STOPPING
        
        try:
            # 1. 停止代理服务器
            if self.proxy_server_process:
                await self._stop_proxy_server()
            
            # 2. 恢复系统代理设置
            if self.config.restore_on_exit:
                restore_success = self.proxy_manager.restore_proxy_settings()
                if not restore_success:
                    logger.warning("系统代理恢复失败")
            
            # 3. 清理资源
            await self._cleanup_resources()
            
            self.status = VideoSenseStatus.STOPPED
            self.startup_time = None
            
            logger.info("✅ VideoSense停止成功")
            
            return {
                "success": True,
                "message": "VideoSense停止成功"
            }
            
        except Exception as e:
            logger.error(f"停止失败: {e}")
            self.status = VideoSenseStatus.ERROR
            return {
                "success": False,
                "message": f"停止失败: {str(e)}",
                "error": str(e)
            }
    
    async def restart(self) -> Dict:
        """
        重启VideoSense
        
        Returns:
            Dict: 重启结果
        """
        logger.info("🔄 重启VideoSense...")
        
        # 先停止
        stop_result = await self.stop()
        if not stop_result["success"]:
            return stop_result
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 再启动
        return await self.start()
    
    def get_status(self) -> Dict:
        """
        获取详细状态信息
        
        Returns:
            Dict: 状态信息
        """
        try:
            # 基本状态
            status_info = {
                "status": self.status.value,
                "startup_time": self.startup_time.isoformat() if self.startup_time else None,
                "config": {
                    "proxy_port": self.config.proxy_port,
                    "proxy_mode": self.config.proxy_mode.value,
                    "auto_start_proxy": self.config.auto_start_proxy,
                    "auto_install_cert": self.config.auto_install_cert
                }
            }
            
            # 证书状态
            cert_status = self.cert_manager.check_certificate_validity()
            status_info["certificates"] = cert_status
            
            # 系统代理状态
            proxy_status = self.proxy_manager.get_current_proxy_settings()
            status_info["system_proxy"] = proxy_status
            
            # 检测到的代理软件
            detected_proxies = self.proxy_manager.detect_proxy_software()
            status_info["detected_proxies"] = detected_proxies
            
            # 运行时统计
            if self.status == VideoSenseStatus.RUNNING and self.startup_time:
                uptime = datetime.now() - self.startup_time
                status_info["uptime_seconds"] = int(uptime.total_seconds())
            
            return {
                "success": True,
                "data": status_info
            }
            
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {
                "success": False,
                "message": f"获取状态失败: {str(e)}",
                "error": str(e)
            }
    
    async def _check_environment(self) -> Dict:
        """检查运行环境"""
        logger.info("🔍 检查运行环境...")
        
        try:
            import platform
            import socket
            
            env_info = {
                "platform": platform.system(),
                "platform_version": platform.version(),
                "python_version": platform.python_version(),
                "architecture": platform.machine()
            }
            
            # 检查端口可用性
            port_available = True
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', self.config.proxy_port))
            except OSError:
                port_available = False
            
            env_info["proxy_port_available"] = port_available
            
            # 检查系统权限（简单检查）
            has_admin_rights = os.access("/etc", os.W_OK) if env_info["platform"] != "Windows" else True
            env_info["admin_rights"] = has_admin_rights
            
            return {
                "success": True,
                "data": env_info
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"环境检查失败: {str(e)}"
            }
    
    async def _setup_certificates(self) -> Dict:
        """设置证书"""
        logger.info("🔐 设置证书...")
        
        try:
            if self.config.auto_install_cert:
                success = self.cert_manager.setup_certificates()
                if success:
                    return {
                        "success": True,
                        "data": self.cert_manager.get_certificate_info()
                    }
                else:
                    return {
                        "success": False,
                        "message": "证书设置失败"
                    }
            else:
                # 只检查证书状态
                status = self.cert_manager.check_certificate_validity()
                return {
                    "success": True,
                    "data": {
                        "auto_install": False,
                        "status": status
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"证书设置失败: {str(e)}"
            }
    
    async def _detect_conflicts(self) -> Dict:
        """检测代理冲突"""
        logger.info("🔍 检测代理冲突...")
        
        try:
            detected_proxies = self.proxy_manager.detect_proxy_software()
            current_proxy = self.proxy_manager.get_current_proxy_settings()
            
            self.detected_conflicts = detected_proxies
            
            return {
                "success": True,
                "data": {
                    "detected_proxies": detected_proxies,
                    "current_proxy": current_proxy,
                    "conflict_count": len(detected_proxies)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"冲突检测失败: {str(e)}"
            }
    
    async def _select_proxy_mode(self) -> Dict:
        """选择代理模式"""
        logger.info("🧠 选择代理模式...")
        
        try:
            if self.config.proxy_mode == ProxyMode.AUTO:
                # 自动选择模式
                if len(self.detected_conflicts) > 0:
                    # 有冲突，使用代理链模式
                    selected_mode = ProxyMode.CHAIN
                    reason = f"检测到{len(self.detected_conflicts)}个代理工具，建议使用代理链模式"
                else:
                    # 无冲突，使用直连模式
                    selected_mode = ProxyMode.DIRECT
                    reason = "未检测到代理冲突，使用直连模式"
            else:
                # 手动指定模式
                selected_mode = self.config.proxy_mode
                reason = "用户手动指定模式"
            
            # 更新配置
            self.config.proxy_mode = selected_mode
            
            return {
                "success": True,
                "data": {
                    "selected_mode": selected_mode.value,
                    "reason": reason,
                    "detected_conflicts": len(self.detected_conflicts)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"代理模式选择失败: {str(e)}"
            }
    
    async def _start_proxy_server(self) -> Dict:
        """启动代理服务器"""
        logger.info("🌐 启动代理服务器...")
        
        try:
            from ..proxy_capture import get_proxy_capture
            
            # 获取代理抓包实例
            proxy_capture = get_proxy_capture()
            proxy_capture.port = self.config.proxy_port
            
            # 根据检测到的冲突配置上游代理
            use_upstream = False
            upstream_proxy = None
            
            if self.config.proxy_mode == ProxyMode.CHAIN and len(self.detected_conflicts) > 0:
                # 选择最佳上游代理 - 从conflicts数据结构中获取
                conflicts = self.detected_conflicts
                
                # 检查是否有current_proxy配置
                if isinstance(conflicts, dict) and 'current_proxy' in conflicts:
                    current_proxy = conflicts['current_proxy']
                    if current_proxy.get('services'):
                        # 从系统代理配置中获取第一个可用服务
                        for service_name, service_config in current_proxy['services'].items():
                            http_config = service_config.get('http', {})
                            if http_config.get('enabled'):
                                proxy_host = http_config.get('server', '127.0.0.1')
                                proxy_port = http_config.get('port', '65327')
                                upstream_proxy = f"http://{proxy_host}:{proxy_port}"
                                use_upstream = True
                                logger.info(f"配置上游代理: {upstream_proxy}")
                                break
                elif isinstance(conflicts, list) and len(conflicts) > 0:
                    # 如果是代理列表，选择第一个
                    best_proxy = conflicts[0]
                    if hasattr(best_proxy, 'host') and hasattr(best_proxy, 'port'):
                        upstream_proxy = f"http://{best_proxy.host}:{best_proxy.port}"
                        use_upstream = True
                        logger.info(f"配置上游代理: {upstream_proxy}")
            
            # 启动mitmproxy代理服务器
            result = proxy_capture.start(
                auto_setup_system_proxy=self.config.auto_start_proxy,
                smart_mode=True
            )
            
            if result.get("success"):
                self.proxy_server_process = proxy_capture
                
                return {
                    "success": True,
                    "data": {
                        "proxy_port": self.config.proxy_port,
                        "proxy_mode": self.config.proxy_mode.value,
                        "upstream_proxy": upstream_proxy if use_upstream else None,
                        "mitmproxy_available": True,
                        "message": "mitmproxy代理服务器启动成功"
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"代理服务器启动失败: {result.get('message', '未知错误')}"
                }
            
        except ImportError:
            return {
                "success": False,
                "message": "mitmproxy不可用，请安装mitmproxy依赖"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"代理服务器启动失败: {str(e)}"
            }
    
    async def _stop_proxy_server(self) -> bool:
        """停止代理服务器"""
        logger.info("🛑 停止代理服务器...")
        
        try:
            if self.proxy_server_process:
                # 停止mitmproxy服务器
                result = self.proxy_server_process.stop(auto_restore_system_proxy=True)
                self.proxy_server_process = None
                
                if result.get("success"):
                    logger.info("mitmproxy代理服务器停止成功")
                    return True
                else:
                    logger.error(f"代理服务器停止失败: {result.get('message', '未知错误')}")
                    return False
            else:
                logger.info("代理服务器未运行")
                return True
            
        except Exception as e:
            logger.error(f"停止代理服务器失败: {e}")
            return False
    
    async def _configure_system_proxy(self) -> Dict:
        """配置系统代理"""
        logger.info("🔧 配置系统代理...")
        
        try:
            success = self.proxy_manager.set_system_proxy(
                proxy_host="127.0.0.1",
                proxy_port=self.config.proxy_port,
                proxy_type="http"
            )
            
            if success:
                return {
                    "success": True,
                    "data": {
                        "proxy_host": "127.0.0.1",
                        "proxy_port": self.config.proxy_port
                    }
                }
            else:
                return {
                    "success": False,
                    "message": "系统代理配置失败"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"系统代理配置失败: {str(e)}"
            }
    
    async def _cleanup_resources(self) -> None:
        """清理资源"""
        logger.info("🧹 清理资源...")
        
        try:
            # 清理临时文件、进程等
            pass
            
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


# 全局VideoSense实例
_videosense_instance: Optional[VideoSenseCore] = None


def get_videosense_core(config: VideoSenseConfig = None) -> VideoSenseCore:
    """
    获取VideoSense核心管理器单例
    
    Args:
        config: 配置（仅在首次调用时生效）
        
    Returns:
        VideoSenseCore: 核心管理器实例
    """
    global _videosense_instance
    
    if _videosense_instance is None:
        _videosense_instance = VideoSenseCore(config)
    
    return _videosense_instance


# 便捷函数
async def start_videosense(config: VideoSenseConfig = None) -> Dict:
    """一键启动VideoSense"""
    core = get_videosense_core(config)
    return await core.start()


async def stop_videosense() -> Dict:
    """停止VideoSense"""
    core = get_videosense_core()
    return await core.stop()


def get_videosense_status() -> Dict:
    """获取VideoSense状态"""
    core = get_videosense_core()
    return core.get_status() 