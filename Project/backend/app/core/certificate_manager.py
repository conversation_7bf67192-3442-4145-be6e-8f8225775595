"""
VideoSense 证书管理器
负责生成、安装和管理HTTPS代理所需的自签名证书
支持 macOS、Windows、Linux 跨平台证书安装
"""

import os
import sys
import subprocess
import platform
import tempfile
from pathlib import Path
from datetime import datetime, timedelta
from typing import Tuple, Optional, Dict
import shutil

from cryptography import x509
from cryptography.x509.oid import NameOID, ExtendedKeyUsageOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa


class CertificateManager:
    """
    证书管理器
    
    功能：
    1. 生成自签名根证书和服务器证书
    2. 跨平台安装证书到系统信任库
    3. 管理证书的生命周期
    4. 检查证书有效性
    """
    
    def __init__(self, cert_dir: str = None):
        """
        初始化证书管理器
        
        Args:
            cert_dir: 证书存储目录，默认为用户主目录下的.videosense/certs
        """
        if cert_dir is None:
            cert_dir = os.path.join(Path.home(), '.videosense', 'certs')
        
        self.cert_dir = Path(cert_dir)
        self.cert_dir.mkdir(parents=True, exist_ok=True)
        
        # 证书文件路径
        self.ca_key_path = self.cert_dir / 'videosense-ca-key.pem'
        self.ca_cert_path = self.cert_dir / 'videosense-ca-cert.pem'
        self.server_key_path = self.cert_dir / 'videosense-server-key.pem'
        self.server_cert_path = self.cert_dir / 'videosense-server-cert.pem'
        
        # 平台信息
        self.platform = platform.system().lower()
        
    def generate_ca_certificate(self) -> Tuple[str, str]:
        """
        生成根证书颁发机构(CA)证书
        
        Returns:
            Tuple[str, str]: (私钥路径, 证书路径)
        """
        print("🔐 生成VideoSense根证书...")
        
        # 生成私钥
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # 证书主题信息
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Beijing"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Beijing"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "VideoSense"),
            x509.NameAttribute(NameOID.COMMON_NAME, "VideoSense Root CA"),
        ])
        
        # 构建证书
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            # 证书有效期3年
            datetime.utcnow() + timedelta(days=3*365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.DNSName("*.localhost"),
                x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
                x509.IPAddress(ipaddress.IPv6Address("::1")),
            ]),
            critical=False,
        ).add_extension(
            x509.BasicConstraints(ca=True, path_length=None),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                key_cert_sign=True,
                crl_sign=True,
                digital_signature=False,
                content_commitment=False,
                key_encipherment=False,
                data_encipherment=False,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).sign(private_key, hashes.SHA256())
        
        # 保存私钥
        with open(self.ca_key_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # 保存证书
        with open(self.ca_cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        print(f"✅ 根证书已生成：{self.ca_cert_path}")
        return str(self.ca_key_path), str(self.ca_cert_path)
    
    def generate_server_certificate(self) -> Tuple[str, str]:
        """
        生成服务器证书
        
        Returns:
            Tuple[str, str]: (私钥路径, 证书路径)
        """
        print("🔐 生成VideoSense服务器证书...")
        
        # 加载CA证书和私钥
        if not self.ca_cert_path.exists() or not self.ca_key_path.exists():
            self.generate_ca_certificate()
        
        with open(self.ca_key_path, "rb") as f:
            ca_key = serialization.load_pem_private_key(f.read(), password=None)
        
        with open(self.ca_cert_path, "rb") as f:
            ca_cert = x509.load_pem_x509_certificate(f.read())
        
        # 生成服务器私钥
        server_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # 服务器证书主题
        subject = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Beijing"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Beijing"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "VideoSense"),
            x509.NameAttribute(NameOID.COMMON_NAME, "VideoSense Proxy"),
        ])
        
        # 构建服务器证书
        server_cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            ca_cert.subject
        ).public_key(
            server_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.DNSName("*.localhost"),
                x509.DNSName("*.videosense.local"),
                x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
                x509.IPAddress(ipaddress.IPv6Address("::1")),
            ]),
            critical=False,
        ).add_extension(
            x509.ExtendedKeyUsage([
                ExtendedKeyUsageOID.SERVER_AUTH,
            ]),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                key_cert_sign=False,
                crl_sign=False,
                digital_signature=True,
                content_commitment=False,
                key_encipherment=True,
                data_encipherment=False,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).sign(ca_key, hashes.SHA256())
        
        # 保存服务器私钥
        with open(self.server_key_path, "wb") as f:
            f.write(server_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # 保存服务器证书
        with open(self.server_cert_path, "wb") as f:
            f.write(server_cert.public_bytes(serialization.Encoding.PEM))
        
        print(f"✅ 服务器证书已生成：{self.server_cert_path}")
        return str(self.server_key_path), str(self.server_cert_path)
    
    def install_ca_certificate(self) -> bool:
        """
        将CA证书安装到系统信任库
        
        Returns:
            bool: 安装是否成功
        """
        if not self.ca_cert_path.exists():
            print("❌ CA证书不存在，请先生成证书")
            return False
        
        print(f"📋 正在安装CA证书到系统信任库 ({self.platform})...")
        
        try:
            if self.platform == "darwin":  # macOS
                return self._install_ca_macos()
            elif self.platform == "windows":  # Windows
                return self._install_ca_windows()
            elif self.platform == "linux":  # Linux
                return self._install_ca_linux()
            else:
                print(f"❌ 不支持的操作系统: {self.platform}")
                return False
        except Exception as e:
            print(f"❌ 证书安装失败: {e}")
            return False
    
    def _install_ca_macos(self) -> bool:
        """macOS系统证书安装"""
        try:
            # 使用 security 命令安装证书
            cmd = [
                "security", "add-trusted-cert",
                "-d", "-r", "trustRoot",
                "-k", "/Library/Keychains/System.keychain",
                str(self.ca_cert_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ CA证书已成功安装到macOS系统钥匙串")
                return True
            else:
                print(f"❌ macOS证书安装失败: {result.stderr}")
                # 尝试用户钥匙串
                cmd_user = [
                    "security", "add-trusted-cert",
                    "-d", "-r", "trustRoot",
                    str(self.ca_cert_path)
                ]
                result_user = subprocess.run(cmd_user, capture_output=True, text=True)
                if result_user.returncode == 0:
                    print("✅ CA证书已安装到用户钥匙串")
                    return True
                return False
                
        except Exception as e:
            print(f"❌ macOS证书安装异常: {e}")
            return False
    
    def _install_ca_windows(self) -> bool:
        """Windows系统证书安装"""
        try:
            # 使用 certutil 安装证书到根证书颁发机构
            cmd = ["certutil", "-addstore", "-user", "Root", str(self.ca_cert_path)]
            result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                print("✅ CA证书已成功安装到Windows证书存储")
                return True
            else:
                print(f"❌ Windows证书安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Windows证书安装异常: {e}")
            return False
    
    def _install_ca_linux(self) -> bool:
        """Linux系统证书安装"""
        try:
            # Ubuntu/Debian系统
            cert_dir = Path("/usr/local/share/ca-certificates")
            if cert_dir.exists():
                dest_path = cert_dir / "videosense-ca.crt"
                shutil.copy2(self.ca_cert_path, dest_path)
                
                # 更新证书库
                result = subprocess.run(["update-ca-certificates"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ CA证书已成功安装到Linux系统")
                    return True
            
            # CentOS/RHEL系统
            cert_dir = Path("/etc/pki/ca-trust/source/anchors")
            if cert_dir.exists():
                dest_path = cert_dir / "videosense-ca.crt"
                shutil.copy2(self.ca_cert_path, dest_path)
                
                result = subprocess.run(["update-ca-trust"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ CA证书已成功安装到Linux系统")
                    return True
            
            print("❌ 未找到支持的Linux证书目录")
            return False
            
        except Exception as e:
            print(f"❌ Linux证书安装异常: {e}")
            return False
    
    def uninstall_ca_certificate(self) -> bool:
        """
        从系统信任库卸载CA证书
        
        Returns:
            bool: 卸载是否成功
        """
        print(f"🗑️ 正在从系统信任库卸载CA证书 ({self.platform})...")
        
        try:
            if self.platform == "darwin":
                return self._uninstall_ca_macos()
            elif self.platform == "windows":
                return self._uninstall_ca_windows()
            elif self.platform == "linux":
                return self._uninstall_ca_linux()
            else:
                return False
        except Exception as e:
            print(f"❌ 证书卸载失败: {e}")
            return False
    
    def _uninstall_ca_macos(self) -> bool:
        """macOS系统证书卸载"""
        try:
            # 查找并删除证书
            cmd = ["security", "delete-certificate", "-c", "VideoSense Root CA"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ CA证书已从macOS系统钥匙串卸载")
                return True
            else:
                print("ℹ️ 未找到已安装的CA证书")
                return True
                
        except Exception as e:
            print(f"❌ macOS证书卸载异常: {e}")
            return False
    
    def _uninstall_ca_windows(self) -> bool:
        """Windows系统证书卸载"""
        try:
            # 删除证书
            cmd = ["certutil", "-delstore", "-user", "Root", "VideoSense Root CA"]
            result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
            
            print("✅ 尝试从Windows证书存储卸载CA证书")
            return True
                
        except Exception as e:
            print(f"❌ Windows证书卸载异常: {e}")
            return False
    
    def _uninstall_ca_linux(self) -> bool:
        """Linux系统证书卸载"""
        try:
            # Ubuntu/Debian
            cert_path = Path("/usr/local/share/ca-certificates/videosense-ca.crt")
            if cert_path.exists():
                cert_path.unlink()
                subprocess.run(["update-ca-certificates"], capture_output=True)
                print("✅ CA证书已从Linux系统卸载")
                return True
            
            # CentOS/RHEL
            cert_path = Path("/etc/pki/ca-trust/source/anchors/videosense-ca.crt")
            if cert_path.exists():
                cert_path.unlink()
                subprocess.run(["update-ca-trust"], capture_output=True)
                print("✅ CA证书已从Linux系统卸载")
                return True
            
            print("ℹ️ 未找到已安装的CA证书")
            return True
            
        except Exception as e:
            print(f"❌ Linux证书卸载异常: {e}")
            return False
    
    def check_certificate_validity(self) -> Dict[str, bool]:
        """
        检查证书有效性
        
        Returns:
            Dict[str, bool]: 各证书的有效性状态
        """
        status = {
            "ca_exists": self.ca_cert_path.exists(),
            "server_exists": self.server_cert_path.exists(),
            "ca_valid": False,
            "server_valid": False
        }
        
        # 检查CA证书有效性
        if status["ca_exists"]:
            try:
                with open(self.ca_cert_path, "rb") as f:
                    ca_cert = x509.load_pem_x509_certificate(f.read())
                    now = datetime.utcnow()
                    status["ca_valid"] = (now >= ca_cert.not_valid_before and 
                                        now <= ca_cert.not_valid_after)
            except Exception:
                pass
        
        # 检查服务器证书有效性
        if status["server_exists"]:
            try:
                with open(self.server_cert_path, "rb") as f:
                    server_cert = x509.load_pem_x509_certificate(f.read())
                    now = datetime.utcnow()
                    status["server_valid"] = (now >= server_cert.not_valid_before and 
                                            now <= server_cert.not_valid_after)
            except Exception:
                pass
        
        return status
    
    def setup_certificates(self, force_regenerate: bool = False) -> bool:
        """
        一键设置证书（生成+安装）
        
        Args:
            force_regenerate: 是否强制重新生成证书
            
        Returns:
            bool: 设置是否成功
        """
        print("🚀 开始设置VideoSense证书...")
        
        # 检查现有证书
        if not force_regenerate:
            status = self.check_certificate_validity()
            if status["ca_valid"] and status["server_valid"]:
                print("ℹ️ 检测到有效证书，跳过生成步骤")
                return self.install_ca_certificate()
        
        # 生成证书
        try:
            self.generate_ca_certificate()
            self.generate_server_certificate()
        except Exception as e:
            print(f"❌ 证书生成失败: {e}")
            return False
        
        # 安装CA证书
        return self.install_ca_certificate()
    
    def get_certificate_info(self) -> Dict:
        """
        获取证书信息
        
        Returns:
            Dict: 证书详细信息
        """
        info = {
            "cert_dir": str(self.cert_dir),
            "platform": self.platform,
            "ca_cert_path": str(self.ca_cert_path),
            "server_cert_path": str(self.server_cert_path),
            "status": self.check_certificate_validity()
        }
        
        return info


# 添加缺失的导入
import ipaddress


def main():
    """
    证书管理器命令行工具
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="VideoSense证书管理器")
    parser.add_argument("--setup", action="store_true", help="一键设置证书")
    parser.add_argument("--install", action="store_true", help="安装CA证书")
    parser.add_argument("--uninstall", action="store_true", help="卸载CA证书")
    parser.add_argument("--check", action="store_true", help="检查证书状态")
    parser.add_argument("--force", action="store_true", help="强制重新生成证书")
    
    args = parser.parse_args()
    
    cert_manager = CertificateManager()
    
    if args.setup:
        success = cert_manager.setup_certificates(force_regenerate=args.force)
        if success:
            print("🎉 证书设置完成！")
        else:
            print("❌ 证书设置失败！")
    
    elif args.install:
        success = cert_manager.install_ca_certificate()
        if success:
            print("🎉 证书安装完成！")
    
    elif args.uninstall:
        success = cert_manager.uninstall_ca_certificate()
        if success:
            print("🎉 证书卸载完成！")
    
    elif args.check:
        info = cert_manager.get_certificate_info()
        print("📋 证书信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main() 