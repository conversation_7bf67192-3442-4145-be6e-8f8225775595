"""
数据库配置模块
配置SQLite数据库连接和会话管理
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import logging
import os

# 日志配置
logger = logging.getLogger(__name__)

# 数据库文件路径 (SQLite) - 使用项目根目录，避免权限问题
# 获取项目根目录路径（从backend/app/core向上三级）
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
DB_PATH = os.path.join(PROJECT_ROOT, 'videosense.db')
SQLALCHEMY_DATABASE_URL = f"sqlite:///{DB_PATH}"

print(f"数据库路径: {DB_PATH}")  # 调试信息

# SQLAlchemy 引擎
# connect_args 是 SQLite 特有的配置，允许多线程共享同一个连接
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, 
    connect_args={"check_same_thread": False},
    echo=False  # 在生产环境中建议关闭echo
)

# 数据库会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 数据模型基类
Base = declarative_base()

# --- 依赖注入 ---
def get_db():
    """
    FastAPI 依赖注入函数，为每个请求提供一个数据库会话。
    确保会话在使用后能被正确关闭。
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# --- 数据库初始化 ---
def create_tables():
    """
    创建数据库表。
    只有在表不存在时才会创建。
    """
    try:
        logger.info("正在创建数据库表...")
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功。")
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise 