"""
增强代理检测引擎
实现智能的翻墙工具检测算法，支持多种检测策略和精确识别
"""

import os
import sys
import json
import psutil
import socket
import subprocess
import platform
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
import aiohttp
import time
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class ProxyType(Enum):
    """代理类型"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"
    SHADOWSOCKS = "shadowsocks"
    VMESS = "vmess"
    TROJAN = "trojan"
    UNKNOWN = "unknown"


class ProxyStatus(Enum):
    """代理状态"""
    RUNNING = "running"        # 运行中
    STOPPED = "stopped"        # 已停止
    ERROR = "error"           # 错误状态
    UNKNOWN = "unknown"       # 未知状态


@dataclass
class ProxyInfo:
    """代理信息"""
    name: str                     # 代理软件名称
    type: ProxyType              # 代理类型
    host: str                    # 主机地址
    port: int                    # 端口号
    status: ProxyStatus          # 运行状态
    pid: Optional[int] = None    # 进程ID
    process_name: str = ""       # 进程名称
    config_path: str = ""        # 配置文件路径
    executable_path: str = ""    # 可执行文件路径
    version: str = ""            # 版本信息
    protocols: List[str] = None  # 支持的协议
    confidence: float = 0.0      # 检测置信度 (0-1)
    
    def __post_init__(self):
        if self.protocols is None:
            self.protocols = []


class ProxyDetectionEngine:
    """
    增强代理检测引擎
    
    功能：
    1. 多策略检测代理软件
    2. 精确识别代理类型和配置
    3. 实时状态监控
    4. 智能冲突分析
    """
    
    # 已知代理软件的特征数据库
    PROXY_SIGNATURES = {
        "clash": {
            "process_names": ["clash", "clash-core", "clash-meta", "Clash", "Clash for Windows"],
            "executable_patterns": ["*clash*", "*Clash*"],
            "config_patterns": ["config.yaml", "config.yml", ".clash"],
            "default_ports": [7890, 7891, 9090],
            "protocols": ["http", "https", "socks5"],
            "detection_methods": ["process", "port", "config", "api"],
            "api_endpoints": ["/version", "/configs", "/proxies"]
        },
        "clash_pro": {
            "process_names": ["ClashX Pro", "Clash Verge", "clash-verge", "ClashForWindows"],
            "executable_patterns": ["*ClashX*", "*Clash Verge*", "*ClashForWindows*"],
            "config_patterns": ["config.yaml", "config.yml"],
            "default_ports": [65327, 7890, 7891, 9090],  # 65327是常见的Clash Pro端口
            "protocols": ["http", "https", "socks5"],
            "detection_methods": ["process", "port", "config", "api"],
            "api_endpoints": ["/version", "/configs", "/proxies"]
        },
        "v2ray": {
            "process_names": ["v2ray", "v2ray-core", "V2Ray", "xray", "Xray"],
            "executable_patterns": ["*v2ray*", "*V2Ray*", "*xray*", "*Xray*"],
            "config_patterns": ["config.json", "v2ray.json"],
            "default_ports": [1080, 8080, 10808, 10809],
            "protocols": ["vmess", "vless", "shadowsocks", "trojan", "socks5"],
            "detection_methods": ["process", "port", "config"]
        },
        "shadowsocks": {
            "process_names": ["ss-local", "ss-server", "sslocal", "shadowsocks", "ShadowsocksX-NG"],
            "executable_patterns": ["*shadowsocks*", "*ss-*"],
            "config_patterns": ["ss.json", "shadowsocks.json", "config.json"],
            "default_ports": [1080, 1081, 8388],
            "protocols": ["shadowsocks", "socks5"],
            "detection_methods": ["process", "port", "config"]
        },
        "qv2ray": {
            "process_names": ["qv2ray", "Qv2ray"],
            "executable_patterns": ["*qv2ray*", "*Qv2ray*"],
            "config_patterns": ["qv2ray.conf"],
            "default_ports": [1080, 8080, 10808],
            "protocols": ["vmess", "vless", "shadowsocks", "socks5"],
            "detection_methods": ["process", "port", "config"]
        },
        "trojan": {
            "process_names": ["trojan", "trojan-go", "Trojan-Qt5"],
            "executable_patterns": ["*trojan*", "*Trojan*"],
            "config_patterns": ["config.json", "trojan.json"],
            "default_ports": [1080, 443],
            "protocols": ["trojan", "socks5"],
            "detection_methods": ["process", "port", "config"]
        },
        "surge": {
            "process_names": ["Surge", "surge"],
            "executable_patterns": ["*Surge*"],
            "config_patterns": ["surge.conf"],
            "default_ports": [6152, 6153],
            "protocols": ["http", "https", "socks5"],
            "detection_methods": ["process", "port", "config"]
        }
    }
    
    def __init__(self):
        """初始化代理检测引擎"""
        self.detected_proxies: Dict[str, ProxyInfo] = {}
        self.system_type = platform.system()
        self.last_scan_time = 0
        self.scan_cache_ttl = 5  # 缓存5秒
        
        logger.info("代理检测引擎初始化完成")
    
    async def detect_all_proxies(self, force_refresh: bool = False) -> List[ProxyInfo]:
        """
        检测所有运行中的代理软件
        
        Args:
            force_refresh: 是否强制刷新检测
            
        Returns:
            List[ProxyInfo]: 检测到的代理信息列表
        """
        # 检查缓存
        current_time = time.time()
        if not force_refresh and (current_time - self.last_scan_time) < self.scan_cache_ttl:
            return list(self.detected_proxies.values())
        
        logger.info("🔍 开始检测代理软件...")
        
        # 清空之前的检测结果
        self.detected_proxies.clear()
        
        # 并行运行多种检测方法
        detection_tasks = [
            self._detect_by_process(),
            self._detect_by_port(),
            self._detect_by_config(),
            self._detect_by_api()
        ]
        
        try:
            await asyncio.gather(*detection_tasks)
        except Exception as e:
            logger.error(f"代理检测过程中出现错误: {e}")
        
        # 合并和验证检测结果
        await self._merge_and_validate_results()
        
        # 更新缓存时间
        self.last_scan_time = current_time
        
        detected_list = list(self.detected_proxies.values())
        logger.info(f"✅ 检测完成，发现 {len(detected_list)} 个代理软件")
        
        return detected_list
    
    async def _detect_by_process(self) -> None:
        """通过进程检测代理软件"""
        logger.debug("通过进程检测代理软件...")
        
        try:
            for process in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
                try:
                    pinfo = process.info
                    process_name = pinfo['name'] or ""
                    
                    # 检查每个已知代理软件
                    for proxy_name, signature in self.PROXY_SIGNATURES.items():
                        if self._match_process_signature(pinfo, signature):
                            await self._add_detected_proxy(
                                proxy_name, 
                                ProxyInfo(
                                    name=proxy_name,
                                    type=self._guess_proxy_type(proxy_name),
                                    host="127.0.0.1",
                                    port=0,  # 稍后通过端口检测确定
                                    status=ProxyStatus.RUNNING,
                                    pid=pinfo['pid'],
                                    process_name=process_name,
                                    executable_path=pinfo.get('exe', ''),
                                    confidence=0.8
                                )
                            )
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        except Exception as e:
            logger.error(f"进程检测失败: {e}")
    
    async def _detect_by_port(self) -> None:
        """通过端口检测代理软件"""
        logger.debug("通过端口检测代理软件...")
        
        try:
            # 获取所有网络连接
            connections = psutil.net_connections(kind='inet')
            
            for conn in connections:
                if conn.status == psutil.CONN_LISTEN and conn.laddr:
                    port = conn.laddr.port
                    
                    # 检查是否为已知代理端口
                    for proxy_name, signature in self.PROXY_SIGNATURES.items():
                        if port in signature.get('default_ports', []):
                            # 尝试获取进程信息
                            pid = conn.pid
                            process_name = ""
                            executable_path = ""
                            
                            if pid:
                                try:
                                    process = psutil.Process(pid)
                                    process_name = process.name()
                                    executable_path = process.exe()
                                except (psutil.NoSuchProcess, psutil.AccessDenied):
                                    pass
                            
                            await self._add_detected_proxy(
                                f"{proxy_name}_port_{port}",
                                ProxyInfo(
                                    name=proxy_name,
                                    type=self._guess_proxy_type(proxy_name),
                                    host="127.0.0.1",
                                    port=port,
                                    status=ProxyStatus.RUNNING,
                                    pid=pid,
                                    process_name=process_name,
                                    executable_path=executable_path,
                                    confidence=0.6
                                )
                            )
                            
        except Exception as e:
            logger.error(f"端口检测失败: {e}")
    
    async def _detect_by_config(self) -> None:
        """通过配置文件检测代理软件"""
        logger.debug("通过配置文件检测代理软件...")
        
        try:
            # 常见配置文件位置
            config_dirs = self._get_common_config_dirs()
            
            for config_dir in config_dirs:
                if not os.path.exists(config_dir):
                    continue
                    
                # 遍历配置目录
                for root, dirs, files in os.walk(config_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        
                        # 检查是否匹配已知配置文件模式
                        for proxy_name, signature in self.PROXY_SIGNATURES.items():
                            for pattern in signature.get('config_patterns', []):
                                if self._match_file_pattern(file, pattern):
                                    # 尝试解析配置文件获取端口信息
                                    port_info = await self._parse_config_file(file_path, proxy_name)
                                    
                                    await self._add_detected_proxy(
                                        f"{proxy_name}_config",
                                        ProxyInfo(
                                            name=proxy_name,
                                            type=self._guess_proxy_type(proxy_name),
                                            host="127.0.0.1",
                                            port=port_info.get('port', 0),
                                            status=ProxyStatus.UNKNOWN,
                                            config_path=file_path,
                                            protocols=port_info.get('protocols', []),
                                            confidence=0.4
                                        )
                                    )
                                    
        except Exception as e:
            logger.error(f"配置文件检测失败: {e}")
    
    async def _detect_by_api(self) -> None:
        """通过API接口检测代理软件"""
        logger.debug("通过API接口检测代理软件...")
        
        try:
            # 测试常见的代理API端点
            api_tests = []
            
            for proxy_name, signature in self.PROXY_SIGNATURES.items():
                if 'api' in signature.get('detection_methods', []):
                    for port in signature.get('default_ports', []):
                        for endpoint in signature.get('api_endpoints', []):
                            api_tests.append(
                                self._test_api_endpoint(proxy_name, port, endpoint)
                            )
            
            # 并行测试所有API端点
            if api_tests:
                results = await asyncio.gather(*api_tests, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, dict) and result.get('success'):
                        proxy_info = result['proxy_info']
                        await self._add_detected_proxy(
                            f"{proxy_info.name}_api_{proxy_info.port}",
                            proxy_info
                        )
                        
        except Exception as e:
            logger.error(f"API检测失败: {e}")
    
    async def _test_api_endpoint(self, proxy_name: str, port: int, endpoint: str) -> Dict:
        """测试单个API端点"""
        url = f"http://127.0.0.1:{port}{endpoint}"
        
        try:
            timeout = aiohttp.ClientTimeout(total=2)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        return {
                            'success': True,
                            'proxy_info': ProxyInfo(
                                name=proxy_name,
                                type=self._guess_proxy_type(proxy_name),
                                host="127.0.0.1",
                                port=port,
                                status=ProxyStatus.RUNNING,
                                version=data.get('version', ''),
                                confidence=0.9
                            )
                        }
                        
        except Exception:
            pass
        
        return {'success': False}
    
    async def _add_detected_proxy(self, key: str, proxy_info: ProxyInfo) -> None:
        """添加检测到的代理信息"""
        # 如果已存在相同代理，选择置信度更高的
        if key in self.detected_proxies:
            existing = self.detected_proxies[key]
            if proxy_info.confidence > existing.confidence:
                self.detected_proxies[key] = proxy_info
        else:
            self.detected_proxies[key] = proxy_info
    
    async def _merge_and_validate_results(self) -> None:
        """合并和验证检测结果"""
        logger.debug("合并和验证检测结果...")
        
        # 按代理名称分组
        grouped_proxies: Dict[str, List[ProxyInfo]] = {}
        
        for proxy_info in self.detected_proxies.values():
            proxy_name = proxy_info.name
            if proxy_name not in grouped_proxies:
                grouped_proxies[proxy_name] = []
            grouped_proxies[proxy_name].append(proxy_info)
        
        # 合并同一代理的多个检测结果
        merged_proxies: Dict[str, ProxyInfo] = {}
        
        for proxy_name, proxy_list in grouped_proxies.items():
            if len(proxy_list) == 1:
                merged_proxies[proxy_name] = proxy_list[0]
            else:
                # 合并多个检测结果
                merged_proxy = await self._merge_proxy_results(proxy_list)
                merged_proxies[proxy_name] = merged_proxy
        
        # 更新检测结果
        self.detected_proxies = merged_proxies
        
        # 验证代理是否真正可用
        for proxy_name, proxy_info in self.detected_proxies.items():
            if proxy_info.port > 0:
                is_available = await self._verify_proxy_availability(proxy_info)
                if not is_available:
                    proxy_info.status = ProxyStatus.ERROR
    
    async def _merge_proxy_results(self, proxy_list: List[ProxyInfo]) -> ProxyInfo:
        """合并同一代理的多个检测结果"""
        # 选择置信度最高的作为基础
        base_proxy = max(proxy_list, key=lambda p: p.confidence)
        
        # 合并其他信息
        for proxy in proxy_list:
            if proxy.port > 0 and base_proxy.port == 0:
                base_proxy.port = proxy.port
            
            if proxy.pid and not base_proxy.pid:
                base_proxy.pid = proxy.pid
            
            if proxy.process_name and not base_proxy.process_name:
                base_proxy.process_name = proxy.process_name
            
            if proxy.executable_path and not base_proxy.executable_path:
                base_proxy.executable_path = proxy.executable_path
            
            if proxy.config_path and not base_proxy.config_path:
                base_proxy.config_path = proxy.config_path
            
            if proxy.version and not base_proxy.version:
                base_proxy.version = proxy.version
            
            # 合并协议列表
            for protocol in proxy.protocols:
                if protocol not in base_proxy.protocols:
                    base_proxy.protocols.append(protocol)
        
        # 提高合并后的置信度
        base_proxy.confidence = min(1.0, base_proxy.confidence + 0.2)
        
        return base_proxy
    
    async def _verify_proxy_availability(self, proxy_info: ProxyInfo) -> bool:
        """验证代理是否真正可用"""
        try:
            # 尝试连接代理端口
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(proxy_info.host, proxy_info.port),
                timeout=2.0
            )
            writer.close()
            await writer.wait_closed()
            return True
            
        except Exception:
            return False
    
    def _match_process_signature(self, process_info: Dict, signature: Dict) -> bool:
        """匹配进程签名"""
        process_name = process_info.get('name', '').lower()
        exe_path = process_info.get('exe', '').lower()
        
        # 检查进程名称
        for pattern in signature.get('process_names', []):
            if pattern.lower() in process_name:
                return True
        
        # 检查可执行文件路径
        for pattern in signature.get('executable_patterns', []):
            pattern = pattern.replace('*', '').lower()
            if pattern in exe_path:
                return True
        
        return False
    
    def _match_file_pattern(self, filename: str, pattern: str) -> bool:
        """匹配文件模式"""
        if '*' in pattern:
            # 简单的通配符匹配
            pattern_parts = pattern.split('*')
            filename_lower = filename.lower()
            pattern_lower = pattern.lower()
            
            if pattern_lower.startswith('*'):
                return filename_lower.endswith(pattern_parts[-1])
            elif pattern_lower.endswith('*'):
                return filename_lower.startswith(pattern_parts[0])
            else:
                return pattern_parts[0] in filename_lower and filename_lower.endswith(pattern_parts[-1])
        else:
            return filename.lower() == pattern.lower()
    
    def _guess_proxy_type(self, proxy_name: str) -> ProxyType:
        """根据代理名称猜测代理类型"""
        proxy_name_lower = proxy_name.lower()
        
        if 'shadowsocks' in proxy_name_lower or 'ss-' in proxy_name_lower:
            return ProxyType.SHADOWSOCKS
        elif 'v2ray' in proxy_name_lower or 'vmess' in proxy_name_lower:
            return ProxyType.VMESS
        elif 'trojan' in proxy_name_lower:
            return ProxyType.TROJAN
        elif 'clash' in proxy_name_lower:
            return ProxyType.HTTP  # Clash通常提供HTTP代理
        else:
            return ProxyType.UNKNOWN
    
    def _get_common_config_dirs(self) -> List[str]:
        """获取常见配置文件目录"""
        config_dirs = []
        
        if self.system_type == "Windows":
            # Windows配置目录
            user_home = os.path.expanduser("~")
            config_dirs.extend([
                os.path.join(user_home, ".config"),
                os.path.join(user_home, "AppData", "Roaming"),
                os.path.join(user_home, "AppData", "Local"),
                "C:\\ProgramData",
                "C:\\Program Files",
                "C:\\Program Files (x86)"
            ])
        elif self.system_type == "Darwin":
            # macOS配置目录
            user_home = os.path.expanduser("~")
            config_dirs.extend([
                os.path.join(user_home, ".config"),
                os.path.join(user_home, "Library", "Application Support"),
                os.path.join(user_home, "Library", "Preferences"),
                "/usr/local/etc",
                "/etc"
            ])
        else:
            # Linux配置目录
            user_home = os.path.expanduser("~")
            config_dirs.extend([
                os.path.join(user_home, ".config"),
                os.path.join(user_home, ".local", "share"),
                "/usr/local/etc",
                "/etc",
                "/opt"
            ])
        
        return [d for d in config_dirs if os.path.exists(d)]
    
    async def _parse_config_file(self, file_path: str, proxy_name: str) -> Dict:
        """解析配置文件获取端口信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 根据不同代理类型解析配置
            if proxy_name == "clash":
                return await self._parse_clash_config(content)
            elif proxy_name == "v2ray":
                return await self._parse_v2ray_config(content)
            elif proxy_name == "shadowsocks":
                return await self._parse_shadowsocks_config(content)
            else:
                return {}
                
        except Exception as e:
            logger.debug(f"解析配置文件失败 {file_path}: {e}")
            return {}
    
    async def _parse_clash_config(self, content: str) -> Dict:
        """解析Clash配置文件"""
        try:
            import yaml
            config = yaml.safe_load(content)
            
            port = config.get('port', 0)
            socks_port = config.get('socks-port', 0)
            
            protocols = []
            if port > 0:
                protocols.append('http')
            if socks_port > 0:
                protocols.append('socks5')
            
            return {
                'port': port or socks_port,
                'protocols': protocols
            }
            
        except Exception:
            return {}
    
    async def _parse_v2ray_config(self, content: str) -> Dict:
        """解析V2Ray配置文件"""
        try:
            config = json.loads(content)
            inbounds = config.get('inbounds', [])
            
            if inbounds:
                inbound = inbounds[0]
                port = inbound.get('port', 0)
                protocol = inbound.get('protocol', '')
                
                return {
                    'port': port,
                    'protocols': [protocol] if protocol else []
                }
                
        except Exception:
            return {}
        
        return {}
    
    async def _parse_shadowsocks_config(self, content: str) -> Dict:
        """解析Shadowsocks配置文件"""
        try:
            config = json.loads(content)
            port = config.get('local_port', 0)
            
            return {
                'port': port,
                'protocols': ['shadowsocks', 'socks5']
            }
            
        except Exception:
            return {}
    
    def get_proxy_by_name(self, name: str) -> Optional[ProxyInfo]:
        """根据名称获取代理信息"""
        return self.detected_proxies.get(name)
    
    def get_all_detected_proxies(self) -> List[ProxyInfo]:
        """获取所有检测到的代理"""
        return list(self.detected_proxies.values())
    
    def get_proxy_recommendations(self) -> Dict[str, any]:
        """获取代理配置建议"""
        recommendations = {
            "has_conflicts": len(self.detected_proxies) > 0,
            "recommended_mode": "direct",
            "suggested_chain": None,
            "warnings": []
        }
        
        if len(self.detected_proxies) > 0:
            # 有代理冲突，建议使用代理链模式
            recommendations["recommended_mode"] = "chain"
            
            # 寻找最佳上游代理
            best_proxy = max(
                self.detected_proxies.values(),
                key=lambda p: p.confidence
            )
            
            if best_proxy.status == ProxyStatus.RUNNING and best_proxy.port > 0:
                recommendations["suggested_chain"] = {
                    "upstream_host": best_proxy.host,
                    "upstream_port": best_proxy.port,
                    "upstream_type": best_proxy.type.value,
                    "proxy_name": best_proxy.name
                }
            else:
                recommendations["warnings"].append(
                    f"检测到代理软件 {best_proxy.name}，但无法确定其运行状态"
                )
        
        return recommendations


# 全局检测引擎实例
_detection_engine: Optional[ProxyDetectionEngine] = None


def get_proxy_detection_engine() -> ProxyDetectionEngine:
    """获取代理检测引擎单例"""
    global _detection_engine
    
    if _detection_engine is None:
        _detection_engine = ProxyDetectionEngine()
    
    return _detection_engine 