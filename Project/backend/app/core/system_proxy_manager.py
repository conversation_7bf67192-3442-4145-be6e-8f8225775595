"""
VideoSense 系统代理管理器
负责跨平台系统代理的设置、恢复和管理
支持智能冲突检测和代理链配置
"""

import os
import sys
import subprocess
import platform
import json
import psutil
import logging
from pathlib import Path
from typing import Dict, Optional, Tuple, List
from datetime import datetime
import re

logger = logging.getLogger(__name__)


class SystemProxyManager:
    """
    系统代理管理器
    
    功能：
    1. 跨平台系统代理设置和恢复
    2. 检测现有代理软件（Clash、V2Ray等）
    3. 智能代理链配置
    4. 代理状态监控
    """
    
    def __init__(self, backup_file: str = None):
        """
        初始化系统代理管理器
        
        Args:
            backup_file: 代理配置备份文件路径
        """
        self.platform = platform.system().lower()
        
        if backup_file is None:
            backup_dir = Path.home() / '.videosense'
            backup_dir.mkdir(exist_ok=True)
            backup_file = backup_dir / 'proxy_backup.json'
        
        self.backup_file = Path(backup_file)
        self.videosense_proxy_port = 8899  # VideoSense默认代理端口
        
    def get_current_proxy_settings(self) -> Dict:
        """
        获取当前系统代理设置
        
        Returns:
            Dict: 当前代理配置信息
        """
        print(f"🔍 检测系统代理设置 ({self.platform})...")
        
        try:
            if self.platform == "darwin":
                return self._get_proxy_macos()
            elif self.platform == "windows":
                return self._get_proxy_windows()
            elif self.platform == "linux":
                return self._get_proxy_linux()
            else:
                return {"error": f"不支持的操作系统: {self.platform}"}
        except Exception as e:
            return {"error": f"获取代理设置失败: {e}"}
    
    def _get_proxy_macos(self) -> Dict:
        """获取macOS系统代理设置"""
        settings = {}
        
        try:
            # 获取网络服务列表
            cmd = ["networksetup", "-listallnetworkservices"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                return {"error": "无法获取网络服务列表"}
            
            services = []
            for line in result.stdout.strip().split('\n')[1:]:  # 跳过标题行
                if line.strip() and not line.startswith('*'):
                    services.append(line.strip())
            
            # 获取每个服务的代理设置
            for service in services:
                # HTTP代理
                cmd_http = ["networksetup", "-getwebproxy", service]
                result_http = subprocess.run(cmd_http, capture_output=True, text=True)
                
                # HTTPS代理
                cmd_https = ["networksetup", "-getsecurewebproxy", service]
                result_https = subprocess.run(cmd_https, capture_output=True, text=True)
                
                # SOCKS代理
                cmd_socks = ["networksetup", "-getsocksfirewallproxy", service]
                result_socks = subprocess.run(cmd_socks, capture_output=True, text=True)
                
                service_settings = {
                    "http": self._parse_macos_proxy_output(result_http.stdout),
                    "https": self._parse_macos_proxy_output(result_https.stdout),
                    "socks": self._parse_macos_proxy_output(result_socks.stdout)
                }
                
                settings[service] = service_settings
            
            return {"platform": "macos", "services": settings}
            
        except Exception as e:
            return {"error": f"macOS代理检测失败: {e}"}
    
    def _parse_macos_proxy_output(self, output: str) -> Dict:
        """解析macOS代理命令输出"""
        proxy_info = {"enabled": False, "server": "", "port": ""}
        
        for line in output.strip().split('\n'):
            if "Enabled: Yes" in line:
                proxy_info["enabled"] = True
            elif "Server:" in line:
                proxy_info["server"] = line.split("Server:")[1].strip()
            elif "Port:" in line:
                proxy_info["port"] = line.split("Port:")[1].strip()
        
        return proxy_info
    
    def _get_proxy_windows(self) -> Dict:
        """获取Windows系统代理设置"""
        try:
            import winreg
            
            settings = {}
            
            # 打开注册表项
            key_path = r"Software\Microsoft\Windows\CurrentVersion\Internet Settings"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path) as key:
                try:
                    proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
                    settings["enabled"] = bool(proxy_enable)
                except FileNotFoundError:
                    settings["enabled"] = False
                
                try:
                    proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
                    settings["server"] = proxy_server
                except FileNotFoundError:
                    settings["server"] = ""
                
                try:
                    proxy_override, _ = winreg.QueryValueEx(key, "ProxyOverride")
                    settings["bypass"] = proxy_override
                except FileNotFoundError:
                    settings["bypass"] = ""
            
            return {"platform": "windows", "settings": settings}
            
        except Exception as e:
            return {"error": f"Windows代理检测失败: {e}"}
    
    def _get_proxy_linux(self) -> Dict:
        """获取Linux系统代理设置"""
        settings = {}
        
        # 检查环境变量
        env_proxies = {}
        for var in ["http_proxy", "https_proxy", "ftp_proxy", "socks_proxy", "no_proxy"]:
            value = os.environ.get(var) or os.environ.get(var.upper())
            if value:
                env_proxies[var] = value
        
        settings["environment"] = env_proxies
        
        # 检查GNOME代理设置（如果可用）
        try:
            cmd = ["gsettings", "get", "org.gnome.system.proxy", "mode"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                settings["gnome_mode"] = result.stdout.strip().strip("'")
                
                # 获取具体代理设置
                for proxy_type in ["http", "https", "ftp", "socks"]:
                    host_cmd = ["gsettings", "get", f"org.gnome.system.proxy.{proxy_type}", "host"]
                    port_cmd = ["gsettings", "get", f"org.gnome.system.proxy.{proxy_type}", "port"]
                    
                    host_result = subprocess.run(host_cmd, capture_output=True, text=True)
                    port_result = subprocess.run(port_cmd, capture_output=True, text=True)
                    
                    if host_result.returncode == 0 and port_result.returncode == 0:
                        host = host_result.stdout.strip().strip("'")
                        port = port_result.stdout.strip()
                        if host and port != "0":
                            settings[f"gnome_{proxy_type}"] = f"{host}:{port}"
        except:
            pass
        
        return {"platform": "linux", "settings": settings}
    
    async def detect_proxy_software_enhanced(self) -> List[Dict]:
        """
        增强的代理软件检测（使用新的检测引擎）
        
        Returns:
            List[Dict]: 检测到的代理软件信息列表
        """
        try:
            from .proxy_detection_engine import get_proxy_detection_engine
            
            detection_engine = get_proxy_detection_engine()
            detected_proxies = await detection_engine.detect_all_proxies()
            
            # 转换为兼容格式
            result = []
            for proxy in detected_proxies:
                result.append({
                    "name": proxy.name,
                    "type": proxy.type.value,
                    "host": proxy.host,
                    "port": proxy.port,
                    "status": proxy.status.value,
                    "pid": proxy.pid,
                    "process_name": proxy.process_name,
                    "executable_path": proxy.executable_path,
                    "config_path": proxy.config_path,
                    "version": proxy.version,
                    "protocols": proxy.protocols,
                    "confidence": proxy.confidence
                })
            
            return result
            
        except Exception as e:
            logger.error(f"增强代理检测失败: {e}")
            # 降级到旧的检测方法
            return self.detect_proxy_software()

    def detect_proxy_software(self) -> List[Dict]:
        """
        检测运行中的代理软件
        
        Returns:
            List[Dict]: 检测到的代理软件信息
        """
        print("🔍 检测运行中的代理软件...")
        
        detected_proxies = []
        
        # 常见代理软件的进程名和端口
        proxy_patterns = {
            "clash": {"processes": ["clash", "Clash for Windows"], "ports": [7890, 7891, 65327]},
            "v2ray": {"processes": ["v2ray", "v2ray-core"], "ports": [10808, 10809]},
            "shadowsocks": {"processes": ["ss-local", "shadowsocks"], "ports": [1080, 1081]},
            "proxifier": {"processes": ["proxifier"], "ports": []},
            "charles": {"processes": ["charles"], "ports": [8888]},
            "fiddler": {"processes": ["fiddler"], "ports": [8888]},
        }
        
        # 检查进程
        for process in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                process_name = process.info['name'].lower()
                cmdline = ' '.join(process.info['cmdline'] or []).lower()
                
                for proxy_name, config in proxy_patterns.items():
                    for pattern in config["processes"]:
                        if pattern.lower() in process_name or pattern.lower() in cmdline:
                            detected_proxies.append({
                                "name": proxy_name,
                                "process_name": process.info['name'],
                                "pid": process.info['pid'],
                                "type": "process_detected"
                            })
                            break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 检查端口占用
        for proxy_name, config in proxy_patterns.items():
            for port in config["ports"]:
                if self._is_port_in_use("127.0.0.1", port):
                    # 检查是否已经通过进程检测到
                    already_detected = any(p["name"] == proxy_name for p in detected_proxies)
                    if not already_detected:
                        detected_proxies.append({
                            "name": proxy_name,
                            "port": port,
                            "host": "127.0.0.1",
                            "type": "port_detected"
                        })
        
        print(f"✅ 检测到 {len(detected_proxies)} 个代理软件")
        return detected_proxies
    
    def _is_port_in_use(self, host: str, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.ip == host and conn.laddr.port == port:
                    return True
            return False
        except:
            return False
    
    def backup_current_proxy(self) -> bool:
        """
        备份当前系统代理设置
        
        Returns:
            bool: 备份是否成功
        """
        print("💾 备份当前代理设置...")
        
        try:
            current_settings = self.get_current_proxy_settings()
            detected_proxies = self.detect_proxy_software()
            
            backup_data = {
                "timestamp": datetime.now().isoformat(),
                "proxy_settings": current_settings,
                "detected_proxies": detected_proxies,
                "videosense_active": False
            }
            
            # 保存备份文件
            with open(self.backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 代理设置已备份到: {self.backup_file}")
            return True
            
        except Exception as e:
            print(f"❌ 代理设置备份失败: {e}")
            return False
    
    def set_system_proxy(self, proxy_host: str = "127.0.0.1", 
                        proxy_port: int = None, 
                        proxy_type: str = "http") -> bool:
        """
        设置系统代理
        
        Args:
            proxy_host: 代理服务器地址
            proxy_port: 代理服务器端口
            proxy_type: 代理类型 (http/https/socks)
            
        Returns:
            bool: 设置是否成功
        """
        if proxy_port is None:
            proxy_port = self.videosense_proxy_port
        
        print(f"🔧 设置系统代理: {proxy_host}:{proxy_port} ({proxy_type})")
        
        try:
            # 先备份当前设置
            self.backup_current_proxy()
            
            if self.platform == "darwin":
                return self._set_proxy_macos(proxy_host, proxy_port, proxy_type)
            elif self.platform == "windows":
                return self._set_proxy_windows(proxy_host, proxy_port, proxy_type)
            elif self.platform == "linux":
                return self._set_proxy_linux(proxy_host, proxy_port, proxy_type)
            else:
                print(f"❌ 不支持的操作系统: {self.platform}")
                return False
                
        except Exception as e:
            print(f"❌ 设置系统代理失败: {e}")
            return False
    
    def _set_proxy_macos(self, host: str, port: int, proxy_type: str) -> bool:
        """设置macOS系统代理"""
        try:
            # 获取网络服务列表
            cmd = ["networksetup", "-listallnetworkservices"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                return False
            
            services = []
            for line in result.stdout.strip().split('\n')[1:]:
                if line.strip() and not line.startswith('*'):
                    services.append(line.strip())
            
            success = True
            for service in services:
                try:
                    if proxy_type in ["http", "all"]:
                        cmd_http = ["networksetup", "-setwebproxy", service, host, str(port)]
                        subprocess.run(cmd_http, check=True)
                    
                    if proxy_type in ["https", "all"]:
                        cmd_https = ["networksetup", "-setsecurewebproxy", service, host, str(port)]
                        subprocess.run(cmd_https, check=True)
                    
                    if proxy_type in ["socks", "all"]:
                        cmd_socks = ["networksetup", "-setsocksfirewallproxy", service, host, str(port)]
                        subprocess.run(cmd_socks, check=True)
                        
                except subprocess.CalledProcessError:
                    success = False
                    continue
            
            if success:
                print("✅ macOS系统代理设置成功")
            return success
            
        except Exception as e:
            print(f"❌ macOS代理设置失败: {e}")
            return False
    
    def _set_proxy_windows(self, host: str, port: int, proxy_type: str) -> bool:
        """设置Windows系统代理"""
        try:
            import winreg
            
            key_path = r"Software\Microsoft\Windows\CurrentVersion\Internet Settings"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE) as key:
                # 启用代理
                winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
                
                # 设置代理服务器
                proxy_server = f"{host}:{port}"
                winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, proxy_server)
                
                # 设置绕过本地地址
                bypass_list = "localhost;127.*;10.*;172.16.*;172.17.*;172.18.*;172.19.*;172.20.*;172.21.*;172.22.*;172.23.*;172.24.*;172.25.*;172.26.*;172.27.*;172.28.*;172.29.*;172.30.*;172.31.*;192.168.*;<local>"
                winreg.SetValueEx(key, "ProxyOverride", 0, winreg.REG_SZ, bypass_list)
            
            print("✅ Windows系统代理设置成功")
            return True
            
        except Exception as e:
            print(f"❌ Windows代理设置失败: {e}")
            return False
    
    def _set_proxy_linux(self, host: str, port: int, proxy_type: str) -> bool:
        """设置Linux系统代理"""
        try:
            proxy_url = f"http://{host}:{port}"
            
            # 设置环境变量（临时）
            if proxy_type in ["http", "all"]:
                os.environ["http_proxy"] = proxy_url
                os.environ["HTTP_PROXY"] = proxy_url
                
            if proxy_type in ["https", "all"]:
                os.environ["https_proxy"] = proxy_url
                os.environ["HTTPS_PROXY"] = proxy_url
            
            # 尝试设置GNOME代理（如果可用）
            try:
                if proxy_type in ["http", "all"]:
                    subprocess.run(["gsettings", "set", "org.gnome.system.proxy.http", "host", host], check=True)
                    subprocess.run(["gsettings", "set", "org.gnome.system.proxy.http", "port", str(port)], check=True)
                    subprocess.run(["gsettings", "set", "org.gnome.system.proxy", "mode", "manual"], check=True)
            except:
                pass
            
            print("✅ Linux系统代理设置成功")
            return True
            
        except Exception as e:
            print(f"❌ Linux代理设置失败: {e}")
            return False
    
    def restore_proxy_settings(self) -> bool:
        """
        恢复之前备份的代理设置
        
        Returns:
            bool: 恢复是否成功
        """
        print("🔄 恢复代理设置...")
        
        if not self.backup_file.exists():
            print("ℹ️ 没有找到代理备份文件")
            return self.clear_system_proxy()
        
        try:
            with open(self.backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            proxy_settings = backup_data.get("proxy_settings", {})
            
            if self.platform == "darwin":
                return self._restore_proxy_macos(proxy_settings)
            elif self.platform == "windows":
                return self._restore_proxy_windows(proxy_settings)
            elif self.platform == "linux":
                return self._restore_proxy_linux(proxy_settings)
            else:
                return False
                
        except Exception as e:
            print(f"❌ 恢复代理设置失败: {e}")
            return self.clear_system_proxy()
    
    def clear_system_proxy(self) -> bool:
        """
        清除系统代理设置
        
        Returns:
            bool: 清除是否成功
        """
        print("🧹 清除系统代理设置...")
        
        try:
            if self.platform == "darwin":
                return self._clear_proxy_macos()
            elif self.platform == "windows":
                return self._clear_proxy_windows()
            elif self.platform == "linux":
                return self._clear_proxy_linux()
            else:
                return False
                
        except Exception as e:
            print(f"❌ 清除代理设置失败: {e}")
            return False
    
    def _restore_proxy_macos(self, settings: Dict) -> bool:
        """恢复macOS代理设置"""
        # 简化实现：清除代理设置
        return self._clear_proxy_macos()
    
    def _restore_proxy_windows(self, settings: Dict) -> bool:
        """恢复Windows代理设置"""
        # 简化实现：清除代理设置
        return self._clear_proxy_windows()
    
    def _restore_proxy_linux(self, settings: Dict) -> bool:
        """恢复Linux代理设置"""
        # 简化实现：清除代理设置
        return self._clear_proxy_linux()
    
    def _clear_proxy_macos(self) -> bool:
        """清除macOS代理设置"""
        try:
            cmd = ["networksetup", "-listallnetworkservices"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                return False
            
            services = []
            for line in result.stdout.strip().split('\n')[1:]:
                if line.strip() and not line.startswith('*'):
                    services.append(line.strip())
            
            for service in services:
                try:
                    # 关闭HTTP代理
                    subprocess.run(["networksetup", "-setwebproxystate", service, "off"], check=True)
                    # 关闭HTTPS代理
                    subprocess.run(["networksetup", "-setsecurewebproxystate", service, "off"], check=True)
                    # 关闭SOCKS代理
                    subprocess.run(["networksetup", "-setsocksfirewallproxystate", service, "off"], check=True)
                except subprocess.CalledProcessError:
                    continue
            
            print("✅ macOS代理设置已清除")
            return True
            
        except Exception as e:
            print(f"❌ macOS代理清除失败: {e}")
            return False
    
    def _clear_proxy_windows(self) -> bool:
        """清除Windows代理设置"""
        try:
            import winreg
            
            key_path = r"Software\Microsoft\Windows\CurrentVersion\Internet Settings"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE) as key:
                # 禁用代理
                winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
                # 清除代理服务器设置
                winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, "")
            
            print("✅ Windows代理设置已清除")
            return True
            
        except Exception as e:
            print(f"❌ Windows代理清除失败: {e}")
            return False
    
    def _clear_proxy_linux(self) -> bool:
        """清除Linux代理设置"""
        try:
            # 清除环境变量
            for var in ["http_proxy", "HTTP_PROXY", "https_proxy", "HTTPS_PROXY", "ftp_proxy", "FTP_PROXY"]:
                if var in os.environ:
                    del os.environ[var]
            
            # 清除GNOME代理设置
            try:
                subprocess.run(["gsettings", "set", "org.gnome.system.proxy", "mode", "none"], check=True)
            except:
                pass
            
            print("✅ Linux代理设置已清除")
            return True
            
        except Exception as e:
            print(f"❌ Linux代理清除失败: {e}")
            return False
    
    def get_smart_proxy_config(self) -> Dict:
        """
        获取智能代理配置建议
        
        Returns:
            Dict: 代理配置建议
        """
        print("🧠 分析智能代理配置...")
        
        detected_proxies = self.detect_proxy_software()
        current_proxy = self.get_current_proxy_settings()
        
        config = {
            "detected_proxies": detected_proxies,
            "current_proxy": current_proxy,
            "recommendation": "direct",  # 默认直连
            "proxy_chain": [],
            "videosense_port": self.videosense_proxy_port
        }
        
        # 如果检测到翻墙工具，推荐代理链模式
        if detected_proxies:
            config["recommendation"] = "chain"
            
            # 构建代理链：浏览器 -> VideoSense -> 翻墙工具 -> 互联网
            for proxy in detected_proxies:
                if proxy["name"] == "clash" and "port" in proxy:
                    config["proxy_chain"].append({
                        "name": "clash",
                        "host": proxy.get("host", "127.0.0.1"),
                        "port": proxy["port"]
                    })
                    break
        
        return config


def main():
    """
    系统代理管理器命令行工具
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="VideoSense系统代理管理器")
    parser.add_argument("--status", action="store_true", help="查看代理状态")
    parser.add_argument("--detect", action="store_true", help="检测代理软件")
    parser.add_argument("--backup", action="store_true", help="备份当前代理设置")
    parser.add_argument("--set", help="设置代理 (格式: host:port)")
    parser.add_argument("--restore", action="store_true", help="恢复代理设置")
    parser.add_argument("--clear", action="store_true", help="清除代理设置")
    parser.add_argument("--smart", action="store_true", help="智能代理配置分析")
    
    args = parser.parse_args()
    
    proxy_manager = SystemProxyManager()
    
    if args.status:
        settings = proxy_manager.get_current_proxy_settings()
        print("📋 当前代理设置:")
        print(json.dumps(settings, indent=2, ensure_ascii=False))
    
    elif args.detect:
        proxies = proxy_manager.detect_proxy_software()
        print("🔍 检测到的代理软件:")
        for proxy in proxies:
            print(f"  - {proxy}")
    
    elif args.backup:
        success = proxy_manager.backup_current_proxy()
        if success:
            print("🎉 代理设置备份完成！")
    
    elif args.set:
        if ":" in args.set:
            host, port = args.set.split(":", 1)
            success = proxy_manager.set_system_proxy(host, int(port))
            if success:
                print("🎉 代理设置完成！")
        else:
            print("❌ 代理格式错误，请使用 host:port 格式")
    
    elif args.restore:
        success = proxy_manager.restore_proxy_settings()
        if success:
            print("🎉 代理设置恢复完成！")
    
    elif args.clear:
        success = proxy_manager.clear_system_proxy()
        if success:
            print("🎉 代理设置清除完成！")
    
    elif args.smart:
        config = proxy_manager.get_smart_proxy_config()
        print("🧠 智能代理配置分析:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main() 