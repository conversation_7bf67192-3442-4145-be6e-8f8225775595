"""
增强代理冲突解决器
专门解决VideoSense与Clash Pro等翻墙工具的代理冲突问题
基于res-downloader的成功经验，实现智能代理链模式
"""

import os
import sys
import json
import psutil
import socket
import subprocess
import platform
import logging
import time
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import requests

logger = logging.getLogger(__name__)


class ProxyConflictType(Enum):
    """代理冲突类型"""
    NO_CONFLICT = "no_conflict"           # 无冲突
    SYSTEM_PROXY_CONFLICT = "system_proxy_conflict"  # 系统代理冲突
    PORT_CONFLICT = "port_conflict"       # 端口冲突
    PROCESS_CONFLICT = "process_conflict" # 进程冲突


@dataclass
class ProxyConflictInfo:
    """代理冲突信息"""
    conflict_type: ProxyConflictType
    conflicting_tool: str
    conflicting_port: int
    conflicting_host: str = "127.0.0.1"
    resolution_strategy: str = ""
    upstream_proxy_url: str = ""
    confidence: float = 0.0


@dataclass
class ClashProxyInfo:
    """Clash代理信息"""
    tool_name: str
    http_port: int
    socks_port: int
    mixed_port: int
    api_port: int
    api_secret: str = ""
    config_path: str = ""
    is_running: bool = False
    process_id: Optional[int] = None


class EnhancedProxyConflictResolver:
    """
    增强代理冲突解决器
    
    核心功能：
    1. 精确检测Clash Pro等翻墙工具
    2. 智能分析代理冲突类型
    3. 自动选择最佳解决策略
    4. 实现无缝代理链模式
    """
    
    def __init__(self, videosense_port: int = 8899):
        """
        初始化冲突解决器
        
        Args:
            videosense_port: VideoSense代理端口
        """
        self.videosense_port = videosense_port
        self.system_type = platform.system()
        self.detected_clash_info: Optional[ClashProxyInfo] = None
        self.current_system_proxy: Optional[Dict] = None
        self.conflict_resolution_cache: Dict[str, Any] = {}
        
        logger.info(f"增强代理冲突解决器初始化完成 (VideoSense端口: {videosense_port})")
    
    async def detect_clash_pro_detailed(self) -> Optional[ClashProxyInfo]:
        """
        详细检测Clash Pro配置
        
        Returns:
            Optional[ClashProxyInfo]: Clash代理信息，如果未检测到则返回None
        """
        logger.info("🔍 开始详细检测Clash Pro...")
        
        # 1. 通过进程检测
        clash_process = await self._detect_clash_process()
        if not clash_process:
            logger.info("未检测到Clash相关进程")
            return None
        
        # 2. 通过端口扫描确定具体配置
        clash_ports = await self._scan_clash_ports()
        if not clash_ports:
            logger.info("未检测到Clash代理端口")
            return None
        
        # 3. 尝试通过API获取详细信息
        api_info = await self._query_clash_api(clash_ports)
        
        # 4. 构建Clash信息
        clash_info = ClashProxyInfo(
            tool_name=clash_process.get("name", "Clash"),
            http_port=clash_ports.get("http", 0),
            socks_port=clash_ports.get("socks", 0),
            mixed_port=clash_ports.get("mixed", 0),
            api_port=clash_ports.get("api", 0),
            is_running=True,
            process_id=clash_process.get("pid")
        )
        
        # 5. 合并API信息
        if api_info:
            clash_info.api_secret = api_info.get("secret", "")
            clash_info.config_path = api_info.get("config_path", "")
        
        self.detected_clash_info = clash_info
        logger.info(f"✅ 检测到Clash Pro: {clash_info.tool_name} (HTTP:{clash_info.http_port}, SOCKS:{clash_info.socks_port})")
        
        return clash_info
    
    async def _detect_clash_process(self) -> Optional[Dict]:
        """检测Clash相关进程"""
        clash_patterns = [
            "clash", "Clash", "ClashX", "Clash Pro", "ClashForWindows", 
            "clash-core", "clash-meta", "Clash Verge", "clash-verge"
        ]
        
        try:
            for process in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
                try:
                    pinfo = process.info
                    process_name = pinfo.get('name', '').lower()
                    exe_path = pinfo.get('exe', '').lower()
                    
                    # 检查进程名和可执行文件路径
                    for pattern in clash_patterns:
                        if (pattern.lower() in process_name or 
                            pattern.lower() in exe_path):
                            return {
                                "pid": pinfo['pid'],
                                "name": pinfo['name'],
                                "exe": pinfo.get('exe', ''),
                                "pattern_matched": pattern
                            }
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        except Exception as e:
            logger.error(f"检测Clash进程时出错: {e}")
        
        return None
    
    async def _scan_clash_ports(self) -> Dict[str, int]:
        """扫描Clash常用端口"""
        # Clash常用端口配置
        port_configs = {
            "http": [7890, 7891, 65327, 8080, 8888],      # HTTP代理端口
            "socks": [7891, 1080, 1081, 65328],           # SOCKS代理端口  
            "mixed": [7890, 65327],                       # 混合端口
            "api": [9090, 9091, 65329, 8090]              # API端口
        }
        
        detected_ports = {}
        
        for port_type, ports in port_configs.items():
            for port in ports:
                if await self._test_port_connection(port, port_type):
                    detected_ports[port_type] = port
                    logger.debug(f"检测到Clash {port_type}端口: {port}")
                    break
        
        return detected_ports
    
    async def _test_port_connection(self, port: int, port_type: str) -> bool:
        """测试端口连接"""
        try:
            # 对于API端口，尝试HTTP请求
            if port_type == "api":
                try:
                    response = requests.get(
                        f"http://127.0.0.1:{port}/version",
                        timeout=2
                    )
                    return response.status_code == 200
                except:
                    return False
            
            # 对于代理端口，尝试socket连接
            else:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', port))
                sock.close()
                return result == 0
                
        except Exception as e:
            logger.debug(f"测试端口 {port} 连接失败: {e}")
            return False
    
    async def _query_clash_api(self, clash_ports: Dict[str, int]) -> Optional[Dict]:
        """查询Clash API获取详细信息"""
        api_port = clash_ports.get("api")
        if not api_port:
            return None
        
        try:
            # 尝试获取版本信息
            response = requests.get(
                f"http://127.0.0.1:{api_port}/version",
                timeout=3
            )
            
            if response.status_code == 200:
                version_info = response.json()
                logger.debug(f"Clash版本信息: {version_info}")
                return {
                    "version": version_info.get("version", ""),
                    "premium": version_info.get("premium", False)
                }
                
        except Exception as e:
            logger.debug(f"查询Clash API失败: {e}")
        
        return None

    async def analyze_proxy_conflicts(self) -> List[ProxyConflictInfo]:
        """
        分析代理冲突情况

        Returns:
            List[ProxyConflictInfo]: 检测到的冲突列表
        """
        logger.info("🔍 开始分析代理冲突...")
        conflicts = []

        # 1. 检测Clash Pro冲突
        clash_info = await self.detect_clash_pro_detailed()
        if clash_info:
            conflict = await self._analyze_clash_conflict(clash_info)
            if conflict:
                conflicts.append(conflict)

        # 2. 检测系统代理冲突
        system_conflict = await self._analyze_system_proxy_conflict()
        if system_conflict:
            conflicts.append(system_conflict)

        # 3. 检测端口冲突
        port_conflict = await self._analyze_port_conflict()
        if port_conflict:
            conflicts.append(port_conflict)

        logger.info(f"✅ 冲突分析完成，发现 {len(conflicts)} 个冲突")
        return conflicts

    async def _analyze_clash_conflict(self, clash_info: ClashProxyInfo) -> Optional[ProxyConflictInfo]:
        """分析Clash代理冲突"""
        # 检查是否存在系统代理设置指向Clash
        system_proxy = await self._get_current_system_proxy()

        if system_proxy and system_proxy.get("enabled"):
            proxy_host = system_proxy.get("host", "")
            proxy_port = system_proxy.get("port", 0)

            # 检查系统代理是否指向Clash
            if (proxy_host in ["127.0.0.1", "localhost"] and
                proxy_port in [clash_info.http_port, clash_info.mixed_port]):

                return ProxyConflictInfo(
                    conflict_type=ProxyConflictType.SYSTEM_PROXY_CONFLICT,
                    conflicting_tool=clash_info.tool_name,
                    conflicting_port=proxy_port,
                    conflicting_host=proxy_host,
                    resolution_strategy="upstream_chain",
                    upstream_proxy_url=f"http://127.0.0.1:{proxy_port}",
                    confidence=0.9
                )

        return None

    async def _analyze_system_proxy_conflict(self) -> Optional[ProxyConflictInfo]:
        """分析系统代理冲突"""
        system_proxy = await self._get_current_system_proxy()

        if system_proxy and system_proxy.get("enabled"):
            proxy_port = system_proxy.get("port", 0)

            # 检查是否与VideoSense端口冲突
            if proxy_port == self.videosense_port:
                return ProxyConflictInfo(
                    conflict_type=ProxyConflictType.PORT_CONFLICT,
                    conflicting_tool="System Proxy",
                    conflicting_port=proxy_port,
                    resolution_strategy="change_port",
                    confidence=1.0
                )

        return None

    async def _analyze_port_conflict(self) -> Optional[ProxyConflictInfo]:
        """分析端口冲突"""
        # 检查VideoSense端口是否被占用
        if await self._is_port_in_use(self.videosense_port):
            return ProxyConflictInfo(
                conflict_type=ProxyConflictType.PORT_CONFLICT,
                conflicting_tool="Unknown Process",
                conflicting_port=self.videosense_port,
                resolution_strategy="find_alternative_port",
                confidence=0.8
            )

        return None

    async def _get_current_system_proxy(self) -> Optional[Dict]:
        """获取当前系统代理设置"""
        if self.current_system_proxy:
            return self.current_system_proxy

        try:
            if self.system_type == "Darwin":  # macOS
                result = subprocess.run([
                    "networksetup", "-getwebproxy", "Wi-Fi"
                ], capture_output=True, text=True, timeout=5)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    proxy_info = {}
                    for line in lines:
                        if "Enabled:" in line:
                            proxy_info["enabled"] = "Yes" in line
                        elif "Server:" in line:
                            proxy_info["host"] = line.split(": ")[1]
                        elif "Port:" in line:
                            proxy_info["port"] = int(line.split(": ")[1])

                    self.current_system_proxy = proxy_info
                    return proxy_info

            elif self.system_type == "Windows":
                # Windows注册表查询
                import winreg
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                                       r"Software\Microsoft\Windows\CurrentVersion\Internet Settings")
                    proxy_enable = winreg.QueryValueEx(key, "ProxyEnable")[0]

                    if proxy_enable:
                        proxy_server = winreg.QueryValueEx(key, "ProxyServer")[0]
                        if ":" in proxy_server:
                            host, port = proxy_server.split(":", 1)
                            proxy_info = {
                                "enabled": True,
                                "host": host,
                                "port": int(port)
                            }
                            self.current_system_proxy = proxy_info
                            return proxy_info

                    winreg.CloseKey(key)
                except Exception as e:
                    logger.debug(f"Windows代理查询失败: {e}")

        except Exception as e:
            logger.error(f"获取系统代理设置失败: {e}")

        return None

    async def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result == 0
        except:
            return False

    async def resolve_conflicts(self, conflicts: List[ProxyConflictInfo]) -> Dict[str, Any]:
        """
        解决代理冲突

        Args:
            conflicts: 检测到的冲突列表

        Returns:
            Dict[str, Any]: 解决方案配置
        """
        logger.info(f"🔧 开始解决 {len(conflicts)} 个代理冲突...")

        if not conflicts:
            return {
                "success": True,
                "strategy": "direct",
                "message": "无冲突，使用直连模式",
                "mitmproxy_config": {
                    "mode": "regular",
                    "listen_port": self.videosense_port,
                    "upstream_proxy": None
                }
            }

        # 选择最高优先级的冲突进行解决
        primary_conflict = max(conflicts, key=lambda c: c.confidence)

        if primary_conflict.resolution_strategy == "upstream_chain":
            return await self._resolve_with_upstream_chain(primary_conflict)
        elif primary_conflict.resolution_strategy == "change_port":
            return await self._resolve_with_port_change(primary_conflict)
        elif primary_conflict.resolution_strategy == "find_alternative_port":
            return await self._resolve_with_alternative_port(primary_conflict)
        else:
            return {
                "success": False,
                "strategy": "unknown",
                "message": f"未知的解决策略: {primary_conflict.resolution_strategy}"
            }

    async def _resolve_with_upstream_chain(self, conflict: ProxyConflictInfo) -> Dict[str, Any]:
        """使用上游代理链解决冲突"""
        logger.info(f"🔗 使用代理链模式解决冲突: {conflict.conflicting_tool}")

        # 验证上游代理可用性
        upstream_available = await self._test_upstream_proxy(conflict.upstream_proxy_url)
        if not upstream_available:
            return {
                "success": False,
                "strategy": "upstream_chain",
                "message": f"上游代理不可用: {conflict.upstream_proxy_url}"
            }

        return {
            "success": True,
            "strategy": "upstream_chain",
            "message": f"使用代理链模式，通过 {conflict.conflicting_tool} 转发流量",
            "mitmproxy_config": {
                "mode": "upstream",
                "listen_port": self.videosense_port,
                "upstream_proxy": conflict.upstream_proxy_url,
                "upstream_tool": conflict.conflicting_tool
            },
            "system_proxy_config": {
                "set_system_proxy": True,
                "proxy_host": "127.0.0.1",
                "proxy_port": self.videosense_port
            }
        }

    async def _resolve_with_port_change(self, conflict: ProxyConflictInfo) -> Dict[str, Any]:
        """通过更改端口解决冲突"""
        logger.info(f"🔄 通过更改端口解决冲突: {conflict.conflicting_port}")

        # 寻找可用端口
        alternative_port = await self._find_available_port(self.videosense_port + 1)
        if not alternative_port:
            return {
                "success": False,
                "strategy": "change_port",
                "message": "无法找到可用的替代端口"
            }

        return {
            "success": True,
            "strategy": "change_port",
            "message": f"更改VideoSense端口从 {self.videosense_port} 到 {alternative_port}",
            "mitmproxy_config": {
                "mode": "regular",
                "listen_port": alternative_port,
                "upstream_proxy": None
            },
            "system_proxy_config": {
                "set_system_proxy": True,
                "proxy_host": "127.0.0.1",
                "proxy_port": alternative_port
            }
        }

    async def _resolve_with_alternative_port(self, conflict: ProxyConflictInfo) -> Dict[str, Any]:
        """寻找替代端口解决冲突"""
        return await self._resolve_with_port_change(conflict)

    async def _test_upstream_proxy(self, upstream_url: str) -> bool:
        """测试上游代理可用性"""
        try:
            # 解析代理URL
            from urllib.parse import urlparse
            parsed = urlparse(upstream_url)
            host = parsed.hostname or "127.0.0.1"
            port = parsed.port or 8080

            # 测试连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, port))
            sock.close()

            return result == 0

        except Exception as e:
            logger.error(f"测试上游代理失败: {e}")
            return False

    async def _find_available_port(self, start_port: int, max_attempts: int = 100) -> Optional[int]:
        """寻找可用端口"""
        for port in range(start_port, start_port + max_attempts):
            if not await self._is_port_in_use(port):
                return port
        return None

    async def get_optimal_proxy_config(self) -> Dict[str, Any]:
        """
        获取最优代理配置

        Returns:
            Dict[str, Any]: 最优配置方案
        """
        logger.info("🎯 分析最优代理配置...")

        # 1. 分析冲突
        conflicts = await self.analyze_proxy_conflicts()

        # 2. 解决冲突
        resolution = await self.resolve_conflicts(conflicts)

        # 3. 添加额外信息
        resolution["conflicts_detected"] = [asdict(c) for c in conflicts]
        resolution["clash_info"] = asdict(self.detected_clash_info) if self.detected_clash_info else None
        resolution["timestamp"] = time.time()

        # 4. 缓存结果
        self.conflict_resolution_cache = resolution

        logger.info(f"✅ 最优配置分析完成: {resolution['strategy']}")
        return resolution
