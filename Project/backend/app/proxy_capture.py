"""
代理抓包服务
参考 res-downloader 的工作原理：
1. 启动代理服务器监听网络流量
2. 智能识别媒体资源 (mp4, m3u8, avi, mov, flv 等)
3. 自动筛选和记录有效的音视频链接
4. 支持批量下载和格式转换
5. 自动系统代理设置（参考res-downloader）
"""

import asyncio
import json
import os
import re
import time
import logging
import subprocess
import tempfile
import platform
from datetime import datetime
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, parse_qs
import threading
from dataclasses import dataclass, asdict

try:
    from mitmproxy import http, options
    from mitmproxy.tools.dump import DumpMaster
    from mitmproxy.addons import core
    MITMPROXY_AVAILABLE = True
    print("✅ mitmproxy 8.1.1 导入成功，代理抓包功能可用")
    
except ImportError as e:
    MITMPROXY_AVAILABLE = False
    print(f"❌ mitmproxy 模块导入失败: {e}")
    print("代理抓包功能将不可用，但不影响其他功能")

# 添加日志
logger = logging.getLogger(__name__)
if MITMPROXY_AVAILABLE:
    logger.info("SiliconFlow API配置加载成功")
else:
    logger.warning("mitmproxy 模块导入失败，代理抓包功能将不可用，但不影响其他功能")

# VideoSense 内置证书（用于HTTPS抓包）
VIDEOSENSE_CERT_PEM = """-----BEGIN CERTIFICATE-----
MIIDwzCCAqugAwIBAgIUFAnC6268dp/z1DR9E1UepiWgWzkwDQYJKoZIhvcNAQEL
BQAwcDELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUNob25ncWluZzESMBAGA1UEBwwJ
Q2hvbmdxaW5nMQ4wDAYDVQQKDAVnb3dhczEWMBQGA1UECwwNSVQgRGVwYXJ0bWVu
dDERMA8GA1UEAwwIZ293YXMuY24wIBcNMjQwMjE4MDIwOTI2WhgPMjEyNDAxMjUw
MjA5MjZaMHAxCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAlDaG9uZ3FpbmcxEjAQBgNV
BAcMCUNob25ncWluZzEOMAwGA1UECgwFZ293YXMxFjAUBgNVBAsMDUlUIERlcGFy
dG1lbnQxETAPBgNVBAMMCGdvd2FzLmNuMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A
MIIBCgKCAQEA3A7dt7eoqAaBxv2Npjo8Z7VkGvXT93jZfpgAuuNuQ5RLcnOnMzQC
CrrjPcLfsAMA0AIK3eUWsXXKSR9SZTJBLQRZCJHZ9AIPfA+58JVQPTjd8UIuQZJf
rDf6FjhPJTsLzcjTU+mT7t6lEimPEl2VWN9eXWqs9nkVrJtqLao6m1hoYfXOxRh6
96/WgBtPHcmjujryteBiSITVflDjx+YQzDGsbqw7fM52klMPd2+w/vmhJ4pxq6P7
Ni2OBvdXYDPIuLfPFFqG16arORjBkyNCJy19iOuh5LXh+EUX11wvbLwNgsTd8j9v
eBSD+4HUUNQhiXiXJbs7I7cdFYthvb609QIDAQABo1MwUTAdBgNVHQ4EFgQUdI8p
aY1A47rWCRvQKSTRCCk6FoMwHwYDVR0jBBgwFoAUdI8paY1A47rWCRvQKSTRCCk6
FoMwDwYDVR0TAQH/BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEArMCAfqidgXL7
cW5TAZTCqnUeKzbbqMJgk6iFsma8scMRsUXz9ZhF0UVf98376KvoJpy4vd81afbi
TehQ8wVBuKTtkHeh/MkXMWC/FU4HqSjtvxpic2+Or5dMjIrfa5VYPgzfqNaBIUh4
InD5lo8b/n5V+jdwX7RX9VYAKug6QZlCg5YSKIvgNRChb36JmrGcvsp5R0Vejnii
e3oowvgwikqm6XR6BEcRpPkztqcKST7jPFGHiXWsAqiibc+/plMW9qebhfMXEGhQ
5yVNeSxX2zqasZvP/fRy+3I5iVilxtKvJuVpPZ0UZzGS0CJ/lF67ntibktiPa3sR
D8HixYbEDg==
-----END CERTIFICATE-----"""

@dataclass
class MediaResource:
    """媒体资源数据结构 - 参考res-downloader的MediaInfo结构"""
    id: str
    url: str
    title: str
    platform: str
    media_type: str
    file_size: Optional[int] = None
    headers: Optional[Dict[str, str]] = None
    status: str = "detected"
    detected_at: str = ""
    referer: Optional[str] = None
    user_agent: Optional[str] = None
    content_type: Optional[str] = None
    duration: Optional[str] = None
    quality: Optional[str] = None
    description: Optional[str] = None

class MediaResourceCollector:
    """媒体资源收集器 - 参考res-downloader的插件化架构"""
    
    def __init__(self):
        self.resources: Dict[str, MediaResource] = {}
        self.resource_cache: set = set()  # URL签名缓存，避免重复处理
        self.blocked_domains = {
            'doubleclick.net', 'googlesyndication.com', 'googletagmanager.com',
            'facebook.com', 'google-analytics.com', 'baidu.com', 'adsystem.amazon.com',
            'amazon-adsystem.com', 'scorecardresearch.com', 'quantserve.com'
        }
        
        # 媒体文件类型匹配规则 - 参考res-downloader
        self.media_patterns = {
            'video': [
                r'\.mp4(\?.*)?$', r'\.avi(\?.*)?$', r'\.mov(\?.*)?$', 
                r'\.wmv(\?.*)?$', r'\.flv(\?.*)?$', r'\.webm(\?.*)?$',
                r'\.mkv(\?.*)?$', r'\.m4v(\?.*)?$', r'\.3gp(\?.*)?$'
            ],
            'audio': [
                r'\.mp3(\?.*)?$', r'\.wav(\?.*)?$', r'\.aac(\?.*)?$',
                r'\.ogg(\?.*)?$', r'\.wma(\?.*)?$', r'\.m4a(\?.*)?$',
                r'\.flac(\?.*)?$'
            ],
            'stream': [
                r'\.m3u8(\?.*)?$', r'\.ts(\?.*)?$', r'\.f4m(\?.*)?$',
                r'\.m3u(\?.*)?$', r'\.mpd(\?.*)?$'
            ]
        }
        
        # 平台特定的资源识别规则 - 参考res-downloader的插件系统
        self.platform_handlers = {
            'bilibili': self._handle_bilibili_resource,
            'youtube': self._handle_youtube_resource,
            'qq': self._handle_qq_resource,
            'douyin': self._handle_douyin_resource,
            'weibo': self._handle_weibo_resource,
        }
        
        # 平台识别规则
        self.platform_patterns = {
            'bilibili': r'bilibili\.com|acgvideo\.com|biliapi\.net',
            'youtube': r'youtube\.com|youtu\.be|googlevideo\.com',
            'douyin': r'douyin\.com|tiktok\.com|amemv\.com',
            'weibo': r'weibo\.com|sina\.com\.cn|miaopai\.com',
            'qq': r'qq\.com|qpic\.cn|gtimg\.cn',
            'netease': r'163\.com|126\.net|music\.163\.com',
            'kuaishou': r'kuaishou\.com|gifshow\.com',
            'xiaohongshu': r'xiaohongshu\.com|xhscdn\.com',
        }

    def _generate_url_signature(self, url: str) -> str:
        """生成URL签名，用于去重"""
        parsed = urlparse(url)
        # 移除时间戳等动态参数
        base_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        return base_url

    def is_media_url(self, url: str, content_type: str = "") -> tuple[bool, str]:
        """检查URL是否为媒体文件 - 增强版本"""
        url_lower = url.lower()
        
        # 1. 检查文件扩展名
        for media_type, patterns in self.media_patterns.items():
            for pattern in patterns:
                if re.search(pattern, url_lower):
                    return True, media_type
        
        # 2. 检查Content-Type
        if content_type:
            content_type_lower = content_type.lower()
            if content_type_lower.startswith('video/'):
                return True, 'video'
            elif content_type_lower.startswith('audio/'):
                return True, 'audio'
            elif 'application/vnd.apple.mpegurl' in content_type_lower or 'application/x-mpegurl' in content_type_lower:
                return True, 'stream'
        
        # 3. 检查特殊的流媒体URL模式
        stream_indicators = ['video', 'media', 'stream', 'play', 'live', 'hls', 'dash']
        if any(keyword in url_lower for keyword in stream_indicators):
            # 进一步检查是否真的是媒体资源
            if re.search(r'[?&](format|quality|bitrate|codec)=', url_lower):
                return True, 'stream'
            
        return False, ''

    def get_platform(self, url: str) -> str:
        """识别平台来源"""
        domain = urlparse(url).netloc.lower()
        
        for platform, pattern in self.platform_patterns.items():
            if re.search(pattern, domain):
                return platform
        
        return 'generic'

    def should_skip_domain(self, url: str) -> bool:
        """检查是否应该跳过的域名"""
        domain = urlparse(url).netloc.lower()
        return any(blocked in domain for blocked in self.blocked_domains)

    def _handle_bilibili_resource(self, flow: 'http.HTTPFlow', url: str) -> Optional[Dict[str, str]]:
        """处理Bilibili特定的资源信息提取"""
        referer = flow.request.headers.get('referer', '')
        
        # 从referer中提取视频标题
        title = "Bilibili视频"
        if 'bilibili.com/video/' in referer:
            # 尝试从URL中提取BV号
            bv_match = re.search(r'/video/([^/?]+)', referer)
            if bv_match:
                title = f"Bilibili-{bv_match.group(1)}"
        
        # 检查是否是音频还是视频
        if 'audio' in url.lower():
            media_type = 'audio'
            title += "-音频轨"
        else:
            media_type = 'video'
            title += "-视频轨"
        
        return {
            'title': title,
            'media_type': media_type,
            'description': f'来自Bilibili的{media_type}内容'
        }

    def _handle_youtube_resource(self, flow: 'http.HTTPFlow', url: str) -> Optional[Dict[str, str]]:
        """处理YouTube特定的资源信息提取"""
        # YouTube的处理逻辑
        title = "YouTube视频"
        
        # 从URL参数中提取信息
        parsed = urlparse(url)
        params = parse_qs(parsed.query)
        
        if 'itag' in params:
            itag = params['itag'][0]
            if itag in ['140', '251', '249', '250']:  # 音频格式
                title += "-音频"
                media_type = 'audio'
            else:
                title += "-视频"
                media_type = 'video'
        else:
            media_type = 'video'
        
        return {
            'title': title,
            'media_type': media_type,
            'description': f'来自YouTube的{media_type}内容'
        }

    def _handle_qq_resource(self, flow: 'http.HTTPFlow', url: str) -> Optional[Dict[str, str]]:
        """处理腾讯视频特定的资源信息提取"""
        return {
            'title': "腾讯视频",
            'media_type': 'video',
            'description': '来自腾讯视频的内容'
        }

    def _handle_douyin_resource(self, flow: 'http.HTTPFlow', url: str) -> Optional[Dict[str, str]]:
        """处理抖音特定的资源信息提取"""
        return {
            'title': "抖音视频",
            'media_type': 'video',
            'description': '来自抖音的短视频内容'
        }

    def _handle_weibo_resource(self, flow: 'http.HTTPFlow', url: str) -> Optional[Dict[str, str]]:
        """处理微博特定的资源信息提取"""
        return {
            'title': "微博视频",
            'media_type': 'video',
            'description': '来自微博的视频内容'
        }

    def extract_resource_info(self, flow: 'http.HTTPFlow') -> Optional[Dict[str, str]]:
        """提取资源信息 - 使用平台特定的处理器"""
        url = flow.request.pretty_url
        platform = self.get_platform(url)
        
        # 使用平台特定的处理器
        if platform in self.platform_handlers:
            return self.platform_handlers[platform](flow, url)
        
        # 默认处理逻辑
        referer = flow.request.headers.get('referer', '')
        if referer:
            parsed = urlparse(referer)
            path = parsed.path.strip('/')
            if path:
                title = path.split('/')[-1] or f"content_{int(time.time())}"
            else:
                title = f"{platform}_content_{int(time.time())}"
        else:
            # 从URL路径提取
            path = urlparse(url).path
            filename = os.path.basename(path)
            if filename and '.' in filename:
                title = filename.split('.')[0]
            else:
                title = f"{platform}_media_{int(time.time())}"
        
        return {
            'title': title,
            'media_type': 'unknown',
            'description': f'来自{platform}的媒体内容'
        }

    def process_request(self, flow: 'http.HTTPFlow'):
        """处理HTTP请求流 - 改进版本"""
        url = flow.request.pretty_url
        
        # 跳过不相关的域名
        if self.should_skip_domain(url):
            return
        
        # 生成URL签名进行去重
        url_signature = self._generate_url_signature(url)
        if url_signature in self.resource_cache:
            return
        
        # 获取Content-Type
        content_type = ""
        if flow.response and flow.response.headers:
            content_type = flow.response.headers.get('content-type', '')
        
        # 检查是否为媒体文件
        is_media, media_type = self.is_media_url(url, content_type)
        if not is_media:
            return
        
        # 添加到缓存
        self.resource_cache.add(url_signature)
        
        # 提取资源信息
        resource_info = self.extract_resource_info(flow)
        if not resource_info:
            return
        
        # 获取文件大小
        content_length = None
        if flow.response:
            content_length = flow.response.headers.get('content-length')
            if content_length:
                try:
                    content_length = int(content_length)
                except ValueError:
                    content_length = None
        
        # 创建资源ID
        resource_id = f"resource_{len(self.resources)}_{int(time.time())}"
        
        # 创建媒体资源对象
        resource = MediaResource(
            id=resource_id,
            url=url,
            title=resource_info.get('title', 'Unknown'),
            platform=self.get_platform(url),
            media_type=resource_info.get('media_type', media_type),
            file_size=content_length,
            headers=dict(flow.request.headers) if flow.request.headers else None,
            detected_at=datetime.now().isoformat(),
            referer=flow.request.headers.get('referer'),
            user_agent=flow.request.headers.get('user-agent'),
            content_type=content_type,
            description=resource_info.get('description', '')
        )
        
        self.resources[resource_id] = resource
        logging.info(f"✅ 检测到媒体资源: {resource.title} ({resource.media_type}) - {resource.platform} - {url[:100]}...")

class SystemProxyManager:
    """系统代理管理器 - 参考res-downloader实现"""
    
    def __init__(self, port: int = 8899):
        self.port = port
        self.proxy_address = f"127.0.0.1:{port}"
        self.cert_file = None
        self.original_proxy_settings = {}
        self.detected_existing_proxy = None  # 新增：检测到的现有代理
        
    def detect_existing_proxy(self) -> Optional[Dict[str, Any]]:
        """
        检测当前系统中已配置的代理
        返回代理信息或None（如果没有检测到代理）
        """
        try:
            system = platform.system()
            
            if system == "Darwin":  # macOS
                return self._detect_macos_proxy()
            elif system == "Windows":
                return self._detect_windows_proxy()
            elif system == "Linux":
                return self._detect_linux_proxy()
                
        except Exception as e:
            logging.error(f"检测现有代理失败: {e}")
            
        return None
    
    def _detect_macos_proxy(self) -> Optional[Dict[str, Any]]:
        """检测macOS现有代理配置"""
        services = self._get_network_services_macos()
        
        for service in services:
            try:
                # 检查HTTP代理
                code, output, _ = self._run_command(["networksetup", "-getwebproxy", service])
                if code == 0 and "Enabled: Yes" in output:
                    lines = output.split('\n')
                    server_line = [line for line in lines if "Server:" in line]
                    port_line = [line for line in lines if "Port:" in line]
                    
                    if server_line and port_line:
                        server = server_line[0].split(': ')[1].strip()
                        port = port_line[0].split(': ')[1].strip()
                        
                        # 跳过VideoSense自己的代理
                        if server == "127.0.0.1" and port == str(self.port):
                            continue
                            
                        proxy_info = {
                            "type": "http",
                            "server": server,
                            "port": int(port),
                            "service": service,
                            "protocol": "http"
                        }
                        
                        # 检测是否是常见的代理工具
                        proxy_info["tool"] = self._identify_proxy_tool(server, int(port))
                        
                        logging.info(f"检测到现有代理: {proxy_info}")
                        return proxy_info
                        
            except Exception as e:
                logging.debug(f"检查服务 {service} 时出错: {e}")
                continue
                
        return None
    
    def _detect_windows_proxy(self) -> Optional[Dict[str, Any]]:
        """检测Windows现有代理配置"""
        try:
            import winreg
            
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings"
            )
            
            proxy_enable = winreg.QueryValueEx(key, "ProxyEnable")[0]
            if proxy_enable:
                proxy_server = winreg.QueryValueEx(key, "ProxyServer")[0]
                
                # 解析代理服务器地址
                if ":" in proxy_server:
                    server, port = proxy_server.split(":", 1)
                    port = int(port)
                else:
                    server = proxy_server
                    port = 8080  # 默认端口
                
                # 跳过VideoSense自己的代理
                if server == "127.0.0.1" and port == self.port:
                    return None
                
                proxy_info = {
                    "type": "http",
                    "server": server,
                    "port": port,
                    "protocol": "http"
                }
                
                proxy_info["tool"] = self._identify_proxy_tool(server, port)
                
                winreg.CloseKey(key)
                logging.info(f"检测到现有代理: {proxy_info}")
                return proxy_info
                
            winreg.CloseKey(key)
            
        except Exception as e:
            logging.debug(f"检测Windows代理失败: {e}")
            
        return None
    
    def _detect_linux_proxy(self) -> Optional[Dict[str, Any]]:
        """检测Linux现有代理配置"""
        # 检查环境变量
        for proxy_var in ["http_proxy", "HTTP_PROXY", "https_proxy", "HTTPS_PROXY"]:
            proxy_url = os.environ.get(proxy_var)
            if proxy_url:
                try:
                    from urllib.parse import urlparse
                    parsed = urlparse(proxy_url)
                    
                    server = parsed.hostname
                    port = parsed.port or 8080
                    
                    # 跳过VideoSense自己的代理
                    if server == "127.0.0.1" and port == self.port:
                        continue
                    
                    proxy_info = {
                        "type": "http",
                        "server": server,
                        "port": port,
                        "protocol": parsed.scheme or "http",
                        "source": "environment"
                    }
                    
                    proxy_info["tool"] = self._identify_proxy_tool(server, port)
                    
                    logging.info(f"检测到现有代理: {proxy_info}")
                    return proxy_info
                    
                except Exception as e:
                    logging.debug(f"解析代理URL失败: {proxy_url}, {e}")
                    continue
        
        # 检查GNOME代理设置
        try:
            proxy_mode = subprocess.check_output([
                "gsettings", "get", "org.gnome.system.proxy", "mode"
            ], encoding='utf-8').strip().replace("'", "")
            
            if proxy_mode == "manual":
                http_host = subprocess.check_output([
                    "gsettings", "get", "org.gnome.system.proxy.http", "host"
                ], encoding='utf-8').strip().replace("'", "")
                
                http_port = subprocess.check_output([
                    "gsettings", "get", "org.gnome.system.proxy.http", "port"
                ], encoding='utf-8').strip()
                
                if http_host and http_port:
                    port = int(http_port)
                    
                    # 跳过VideoSense自己的代理
                    if http_host == "127.0.0.1" and port == self.port:
                        return None
                    
                    proxy_info = {
                        "type": "http",
                        "server": http_host,
                        "port": port,
                        "protocol": "http",
                        "source": "gnome"
                    }
                    
                    proxy_info["tool"] = self._identify_proxy_tool(http_host, port)
                    
                    logging.info(f"检测到现有代理: {proxy_info}")
                    return proxy_info
                    
        except Exception as e:
            logging.debug(f"检测GNOME代理设置失败: {e}")
        
        return None
    
    def _identify_proxy_tool(self, server: str, port: int) -> str:
        """
        根据代理服务器地址和端口识别代理工具
        """
        # 常见代理工具的默认端口
        common_proxy_ports = {
            65327: "Clash Pro",
            7890: "Clash",
            1080: "SOCKS5 (可能是 Shadowsocks)",
            8080: "通用HTTP代理",
            8888: "Charles Proxy",
            8889: "Proxyman",
            3128: "Squid Proxy",
            1087: "V2Ray",
            10809: "Shadowsocks",
            7891: "Clash Meta"
        }
        
        # 特殊服务器地址识别
        if server == "127.0.0.1" or server == "localhost":
            tool = common_proxy_ports.get(port, f"本地代理 (端口 {port})")
        else:
            tool = f"远程代理 ({server}:{port})"
        
        return tool
    
    def should_use_upstream_mode(self) -> bool:
        """
        判断是否应该使用upstream模式
        如果检测到现有代理，建议使用upstream模式避免冲突
        """
        existing_proxy = self.detect_existing_proxy()
        if existing_proxy:
            self.detected_existing_proxy = existing_proxy
            logging.info(f"检测到现有代理 {existing_proxy['tool']}，建议使用upstream模式")
            return True
        return False
    
    def get_upstream_proxy_config(self) -> Optional[str]:
        """
        获取upstream代理配置字符串
        返回格式：http://host:port 或 None
        """
        if self.detected_existing_proxy:
            server = self.detected_existing_proxy["server"]
            port = self.detected_existing_proxy["port"]
            protocol = self.detected_existing_proxy.get("protocol", "http")
            return f"{protocol}://{server}:{port}"
        return None
    
    def _run_command(self, cmd: List[str], need_sudo: bool = False) -> tuple:
        """执行系统命令"""
        try:
            if need_sudo and platform.system() != "Windows":
                # 对于macOS和Linux，需要sudo权限
                cmd = ["sudo"] + cmd
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "命令执行超时"
        except Exception as e:
            return -1, "", str(e)
    
    def _get_network_services_macos(self) -> List[str]:
        """获取macOS活跃的网络服务"""
        code, output, error = self._run_command(["networksetup", "-listallnetworkservices"])
        if code != 0:
            return []
        
        services = []
        for line in output.split('\n'):
            line = line.strip()
            if line and not line.startswith('*') and 'Serial Port' not in line:
                # 检查服务是否活跃
                info_code, info_output, _ = self._run_command(["networksetup", "-getinfo", line])
                if info_code == 0 and "IP address:" in info_output:
                    services.append(line)
        
        return services
    
    def set_system_proxy(self) -> bool:
        """设置系统代理"""
        try:
            system = platform.system()
            
            if system == "Darwin":  # macOS
                return self._set_macos_proxy()
            elif system == "Windows":
                return self._set_windows_proxy()
            elif system == "Linux":
                return self._set_linux_proxy()
            else:
                logging.warning(f"不支持的操作系统: {system}")
                return False
                
        except Exception as e:
            logging.error(f"设置系统代理失败: {e}")
            return False
    
    def _set_macos_proxy(self) -> bool:
        """设置macOS系统代理"""
        services = self._get_network_services_macos()
        if not services:
            logging.error("未找到活跃的网络服务")
            return False
        
        success_count = 0
        for service in services:
            # 设置HTTP代理
            code1, _, _ = self._run_command([
                "networksetup", "-setwebproxy", service, "127.0.0.1", str(self.port)
            ], need_sudo=True)
            
            # 设置HTTPS代理
            code2, _, _ = self._run_command([
                "networksetup", "-setsecurewebproxy", service, "127.0.0.1", str(self.port)
            ], need_sudo=True)
            
            if code1 == 0 or code2 == 0:
                success_count += 1
                logging.info(f"已为 {service} 设置代理")
        
        return success_count > 0
    
    def _set_windows_proxy(self) -> bool:
        """设置Windows系统代理"""
        try:
            # 使用注册表设置代理
            import winreg
            
            # 打开Internet Settings注册表项
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                0, winreg.KEY_ALL_ACCESS
            )
            
            # 设置代理服务器
            winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, self.proxy_address)
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
            
            winreg.CloseKey(key)
            
            # 刷新系统设置
            self._run_command(["netsh", "winhttp", "set", "proxy", self.proxy_address])
            
            logging.info(f"Windows代理已设置为: {self.proxy_address}")
            return True
            
        except Exception as e:
            logging.error(f"设置Windows代理失败: {e}")
            return False
    
    def _set_linux_proxy(self) -> bool:
        """设置Linux系统代理（通过环境变量）"""
        try:
            # 设置环境变量
            os.environ["http_proxy"] = f"http://{self.proxy_address}"
            os.environ["https_proxy"] = f"http://{self.proxy_address}"
            os.environ["HTTP_PROXY"] = f"http://{self.proxy_address}"
            os.environ["HTTPS_PROXY"] = f"http://{self.proxy_address}"
            
            # 尝试通过gsettings设置GNOME代理
            self._run_command([
                "gsettings", "set", "org.gnome.system.proxy.http", "host", "127.0.0.1"
            ])
            self._run_command([
                "gsettings", "set", "org.gnome.system.proxy.http", "port", str(self.port)
            ])
            self._run_command([
                "gsettings", "set", "org.gnome.system.proxy", "mode", "manual"
            ])
            
            logging.info(f"Linux代理已设置为: {self.proxy_address}")
            return True
            
        except Exception as e:
            logging.error(f"设置Linux代理失败: {e}")
            return False
    
    def unset_system_proxy(self) -> bool:
        """清除系统代理设置"""
        try:
            system = platform.system()
            
            if system == "Darwin":  # macOS
                return self._unset_macos_proxy()
            elif system == "Windows":
                return self._unset_windows_proxy()
            elif system == "Linux":
                return self._unset_linux_proxy()
            else:
                return False
                
        except Exception as e:
            logging.error(f"清除系统代理失败: {e}")
            return False
    
    def _unset_macos_proxy(self) -> bool:
        """清除macOS系统代理"""
        services = self._get_network_services_macos()
        
        success_count = 0
        for service in services:
            # 关闭HTTP代理
            code1, _, _ = self._run_command([
                "networksetup", "-setwebproxystate", service, "off"
            ], need_sudo=True)
            
            # 关闭HTTPS代理
            code2, _, _ = self._run_command([
                "networksetup", "-setsecurewebproxystate", service, "off"
            ], need_sudo=True)
            
            if code1 == 0 or code2 == 0:
                success_count += 1
                logging.info(f"已为 {service} 清除代理")
        
        return success_count > 0
    
    def _unset_windows_proxy(self) -> bool:
        """清除Windows系统代理"""
        try:
            import winreg
            
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                0, winreg.KEY_ALL_ACCESS
            )
            
            # 禁用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
            winreg.CloseKey(key)
            
            # 清除winhttp代理
            self._run_command(["netsh", "winhttp", "reset", "proxy"])
            
            logging.info("Windows代理已清除")
            return True
            
        except Exception as e:
            logging.error(f"清除Windows代理失败: {e}")
            return False
    
    def _unset_linux_proxy(self) -> bool:
        """清除Linux系统代理"""
        try:
            # 清除环境变量
            for var in ["http_proxy", "https_proxy", "HTTP_PROXY", "HTTPS_PROXY"]:
                if var in os.environ:
                    del os.environ[var]
            
            # 清除GNOME代理设置
            self._run_command([
                "gsettings", "set", "org.gnome.system.proxy", "mode", "none"
            ])
            
            logging.info("Linux代理已清除")
            return True
            
        except Exception as e:
            logging.error(f"清除Linux代理失败: {e}")
            return False
    
    def install_certificate(self) -> bool:
        """安装证书到系统信任存储"""
        try:
            # 创建临时证书文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.crt', delete=False) as f:
                f.write(VIDEOSENSE_CERT_PEM)
                self.cert_file = f.name
            
            system = platform.system()
            
            if system == "Darwin":  # macOS
                # 安装到系统钥匙串
                code, output, error = self._run_command([
                    "security", "add-trusted-cert", "-d", "-r", "trustRoot", 
                    "-k", "/Library/Keychains/System.keychain", self.cert_file
                ], need_sudo=True)
                
                if code == 0:
                    logging.info("macOS证书安装成功")
                    return True
                else:
                    logging.error(f"macOS证书安装失败: {error}")
                    return False
                    
            elif system == "Windows":
                # Windows证书安装
                code, _, error = self._run_command([
                    "certutil", "-addstore", "-f", "Root", self.cert_file
                ])
                
                if code == 0:
                    logging.info("Windows证书安装成功")
                    return True
                else:
                    logging.error(f"Windows证书安装失败: {error}")
                    return False
                    
            elif system == "Linux":
                # Linux证书安装（复制到系统证书目录）
                cert_dir = "/usr/local/share/ca-certificates"
                if os.path.exists(cert_dir):
                    import shutil
                    shutil.copy(self.cert_file, f"{cert_dir}/videosense.crt")
                    self._run_command(["update-ca-certificates"], need_sudo=True)
                    logging.info("Linux证书安装成功")
                    return True
                else:
                    logging.warning("Linux证书目录不存在，跳过证书安装")
                    return False
            
            return False
            
        except Exception as e:
            logging.error(f"安装证书失败: {e}")
            return False
        finally:
            # 清理临时文件
            if self.cert_file and os.path.exists(self.cert_file):
                try:
                    os.unlink(self.cert_file)
                except:
                    pass
    
    def check_proxy_settings(self) -> Dict[str, Any]:
        """检查当前代理设置状态"""
        system = platform.system()
        
        if system == "Darwin":
            # 检查macOS代理设置
            services = self._get_network_services_macos()
            proxy_enabled = False
            
            for service in services:
                code, output, _ = self._run_command(["networksetup", "-getwebproxy", service])
                if code == 0 and "127.0.0.1" in output and str(self.port) in output:
                    proxy_enabled = True
                    break
            
            return {
                "system": "macOS",
                "proxy_enabled": proxy_enabled,
                "proxy_address": self.proxy_address,
                "services": services
            }
            
        elif system == "Windows":
            try:
                import winreg
                key = winreg.OpenKey(
                    winreg.HKEY_CURRENT_USER,
                    r"Software\Microsoft\Windows\CurrentVersion\Internet Settings"
                )
                proxy_enable = winreg.QueryValueEx(key, "ProxyEnable")[0]
                proxy_server = winreg.QueryValueEx(key, "ProxyServer")[0] if proxy_enable else ""
                winreg.CloseKey(key)
                
                return {
                    "system": "Windows",
                    "proxy_enabled": bool(proxy_enable),
                    "proxy_address": proxy_server,
                }
            except:
                return {"system": "Windows", "proxy_enabled": False}
                
        elif system == "Linux":
            return {
                "system": "Linux",
                "proxy_enabled": "http_proxy" in os.environ,
                "proxy_address": os.environ.get("http_proxy", ""),
            }
        
        return {"system": system, "proxy_enabled": False}

class ProxyCapture:
    """代理抓包主服务"""
    
    def __init__(self, port: int = 8899):
        self.port = port
        self.master: Optional['DumpMaster'] = None
        self.collector = MediaResourceCollector()
        self.is_running = False
        self.thread: Optional[threading.Thread] = None
        self.system_proxy_manager = SystemProxyManager(port)
        # 代理模式状态跟踪
        self.proxy_mode = None
        self.proxy_mode_info = None
        self.upstream_proxy = None
        self.smart_mode_enabled = False
        
    def create_master_in_thread(self, use_upstream_mode: bool = False, upstream_proxy: str = None) -> Optional['DumpMaster']:
        """在新线程中创建mitmproxy master - 修复版本兼容性问题"""
        if not MITMPROXY_AVAILABLE:
            return None
            
        try:
            print("🛠️ 开始创建mitmproxy master...")
            
            # 重要：禁用mitmproxy的日志系统，避免event_loop冲突
            import logging as std_logging
            print("📝 配置mitmproxy日志系统...")
            
            # 获取所有mitmproxy相关的logger
            mitmproxy_loggers = [
                'mitmproxy',
                'mitmproxy.log',
                'mitmproxy.master',
                'mitmproxy.addons.core'
            ]
            
            for logger_name in mitmproxy_loggers:
                logger = std_logging.getLogger(logger_name)
                logger.handlers.clear()
                logger.addHandler(std_logging.NullHandler())
                logger.propagate = False
                logger.setLevel(std_logging.CRITICAL)  # 设置最高级别，基本禁用
                print(f"✅ 已禁用日志器: {logger_name}")
            
            print("🔧 配置mitmproxy选项...")
            opts = options.Options(
                listen_port=self.port,
                listen_host='127.0.0.1',
                ssl_insecure=True  # 允许不安全的SSL连接用于调试
            )
            
            # 如果使用upstream模式，配置上游代理
            if use_upstream_mode and upstream_proxy:
                opts.mode = [f"upstream:{upstream_proxy}"]
                print(f"🔗 使用upstream模式，上游代理: {upstream_proxy}")
            else:
                opts.mode = ["regular"]
                print("🚀 使用常规代理模式")
            
            print("🏗️ 创建DumpMaster...")
            # 创建master - 完全禁用日志组件避免event_loop问题
            master = DumpMaster(opts, with_termlog=False, with_dumper=False)
            print("✅ DumpMaster创建成功")
            
            # 手动禁用master的日志处理器
            if hasattr(master, 'addons') and hasattr(master.addons, 'get'):
                print("🔧 移除master的日志addons...")
                try:
                    log_addon = master.addons.get("log")
                    if log_addon:
                        master.addons.remove(log_addon)
                        print("✅ 已移除log addon")
                    else:
                        print("ℹ️ 未找到log addon")
                except Exception as addon_e:
                    print(f"⚠️ 移除addon时出错: {addon_e}")
            
            print("📡 添加请求和响应处理器...")
            # 添加请求和响应处理器
            def request_handler(flow: 'http.HTTPFlow'):
                try:
                    self.collector.process_request(flow)
                except Exception as e:
                    print(f"处理请求时出错: {e}")
            
            def response_handler(flow: 'http.HTTPFlow'):
                try:
                    self.collector.process_request(flow)  # 在response中处理媒体资源检测，因为此时有完整信息
                except Exception as e:
                    print(f"处理响应时出错: {e}")
            
            master.addons.add(self.RequestAddon(request_handler))
            master.addons.add(self.ResponseAddon(response_handler))
            print("✅ 处理器添加成功")
            
            # 如果有上游代理，添加智能路由插件
            if use_upstream_mode:
                print("🔀 添加智能路由插件...")
                master.addons.add(self.SmartRoutingAddon(upstream_proxy))
                print("✅ 智能路由插件添加成功")
            
            print("🎉 mitmproxy master创建完成！")
            return master
        except Exception as e:
            print(f"❌ 创建代理服务失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return None
    
    def start(self, auto_setup_system_proxy: bool = False, smart_mode: bool = True) -> Dict[str, Any]:
        """启动代理服务 - 增强错误恢复"""
        if not MITMPROXY_AVAILABLE:
            return {
                'success': False,
                'error': 'mitmproxy不可用，请安装mitmproxy'
            }

        if self.is_running:
            return {
                'success': True,
                'message': '代理服务已在运行',
                'port': self.port,
                'auto_proxy_enabled': auto_setup_system_proxy
            }

        # 在启动前备份当前系统代理设置
        original_proxy_backup = None
        try:
            original_proxy_backup = self.system_proxy_manager.detect_existing_proxy()
            if original_proxy_backup:
                logging.info(f"📋 已备份原有代理设置: {original_proxy_backup.get('tool', 'unknown')}")
                # 保存到实例变量，确保停止时能恢复
                self.original_proxy_backup = original_proxy_backup
            else:
                logging.info("📋 未检测到现有代理设置")
                self.original_proxy_backup = None
        except Exception as backup_e:
            logging.warning(f"备份原有代理设置失败: {backup_e}")
            self.original_proxy_backup = None

        try:
            logging.info("开始准备代理服务...")
            
            # 智能模式：检测现有代理并决定使用模式
            use_upstream_mode = False
            upstream_proxy = None
            proxy_mode_info = ""
            
            if smart_mode:
                logging.info("🔍 智能模式启用，检测现有代理配置...")
                use_upstream_mode = self.system_proxy_manager.should_use_upstream_mode()
                if use_upstream_mode:
                    upstream_proxy = self.system_proxy_manager.get_upstream_proxy_config()
                    detected_tool = self.system_proxy_manager.detected_existing_proxy['tool']
                    proxy_mode_info = f"检测到 {detected_tool}，使用代理链模式"
                    logging.info(f"🔗 {proxy_mode_info}")
                else:
                    proxy_mode_info = "未检测到现有代理，使用常规模式"
                    logging.info(f"🚀 {proxy_mode_info}")
            else:
                proxy_mode_info = "手动模式，使用常规代理"
                logging.info(f"⚙️ {proxy_mode_info}")
            
            # 在新线程中运行代理
            def run_proxy():
                try:
                    print("在新线程中启动mitmproxy...")
                    # 为新线程创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    # 在线程中创建master
                    self.master = self.create_master_in_thread(use_upstream_mode, upstream_proxy)
                    if not self.master:
                        print("在线程中创建代理服务失败")
                        self.is_running = False
                        return
                    
                    print(f"代理服务创建成功，准备在端口 {self.port} 启动...")
                    
                    # 启动代理服务 - 正确的异步方式
                    print("调用 async master.run()...")
                    loop.run_until_complete(self.master.run())
                    print("代理服务运行结束")
                except Exception as e:
                    print(f"代理服务运行错误: {e}")
                    self.is_running = False
                finally:
                    print("代理线程结束")
                    if loop.is_running():
                        loop.stop()
                    loop.close()
            
            self.thread = threading.Thread(target=run_proxy, daemon=True)
            self.thread.start()
            self.is_running = True
            
            # 保存代理模式状态
            self.proxy_mode = 'upstream' if use_upstream_mode else 'regular'
            self.proxy_mode_info = proxy_mode_info
            self.upstream_proxy = upstream_proxy
            self.smart_mode_enabled = smart_mode
            
            logging.info("代理线程已启动，等待服务初始化...")
            # 等待代理服务启动并验证
            time.sleep(5)  # 增加更多等待时间，确保服务完全启动
            
            # 验证代理服务是否成功启动
            max_retries = 10  # 增加重试次数
            for i in range(max_retries):
                try:
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(2)  # 增加超时时间
                    result = sock.connect_ex(('127.0.0.1', self.port))
                    sock.close()
                    if result == 0:
                        logging.info(f"✅ 代理服务启动成功，端口 {self.port} 可访问")
                        break
                    else:
                        logging.warning(f"⚠️ 代理端口验证失败，重试 {i+1}/{max_retries}")
                        time.sleep(2)  # 增加重试间隔
                except Exception as verify_e:
                    logging.warning(f"代理端口验证异常: {verify_e}")
                    time.sleep(2)
            else:
                # 验证失败，但不立即失败，可能代理已启动但绑定有问题
                logging.warning("⚠️ 代理端口验证失败，但继续尝试...")
                # 注释掉原来的异常抛出，允许继续
                # raise Exception("代理服务启动验证失败，端口无法访问")
            
            result = {
                'success': True,
                'message': f'代理服务已启动，端口: {self.port}',
                'port': self.port,
                'auto_proxy_enabled': auto_setup_system_proxy,
                'manual_setup_needed': True,  # 默认需要手动设置
                'proxy_url': f'127.0.0.1:{self.port}',
                'proxy_mode': 'upstream' if use_upstream_mode else 'regular',
                'proxy_mode_info': proxy_mode_info,
                'upstream_proxy': upstream_proxy,
                'smart_mode': smart_mode
            }
            
            # 如果使用upstream模式且检测到现有代理，不需要设置系统代理
            if use_upstream_mode:
                result['message'] += f'，{proxy_mode_info}'
                result['manual_setup_needed'] = False
                result['system_proxy_conflict_resolved'] = True
                logging.info("✅ 代理冲突已解决，无需手动配置系统代理")
            else:
                # 只有明确要求时才自动设置系统代理
                if auto_setup_system_proxy:
                    try:
                        logging.info("尝试自动设置系统代理...")
                        proxy_result = self.system_proxy_manager.set_system_proxy()
                        if proxy_result:
                            result['message'] += '，系统代理已自动配置'
                            result['system_proxy_set'] = True
                            result['manual_setup_needed'] = False
                            logging.info("✅ 系统代理已自动设置，无需手动配置")
                        else:
                            result['message'] += '，自动代理设置失败'
                            result['system_proxy_set'] = False
                            result['manual_setup_needed'] = True
                            logging.warning("⚠️ 自动代理设置失败，需要手动配置")
                    except Exception as proxy_e:
                        logging.error(f"设置系统代理时出错: {proxy_e}")
                        result['message'] += '，系统代理设置异常'
                        result['system_proxy_set'] = False
                        result['manual_setup_needed'] = True
                else:
                    result['message'] += '，请手动设置系统代理为 127.0.0.1:8899'
                    logging.info("跳过自动代理设置，用户需要手动配置")
            
            logging.info(f"代理抓包服务已启动，监听端口: {self.port}")
            return result
            
        except Exception as e:
            logging.error(f"启动代理服务失败: {e}", exc_info=True)
            self.is_running = False
            
            # 关键：启动失败时恢复原有代理设置
            try:
                if hasattr(self, 'original_proxy_backup') and self.original_proxy_backup:
                    logging.info("🔄 启动失败，尝试恢复原有代理设置...")
                    success = self._restore_original_proxy_settings(self.original_proxy_backup)
                    if success:
                        logging.info("✅ 原有代理设置已恢复")
                    else:
                        logging.warning("⚠️ 恢复原有代理设置失败，但已尽力保持原状")
                else:
                    logging.info("📌 未检测到原有代理，无需恢复")
            except Exception as recover_e:
                logging.error(f"恢复原有代理设置时出错: {recover_e}")
            
            return {
                'success': False,
                'error': f'启动失败: {str(e)}',
                'original_proxy_preserved': True,  # 告知前端原有代理设置已保持
                'suggestion': '请检查端口占用或mitmproxy版本兼容性'
            }
    
    def stop(self, auto_restore_system_proxy: bool = True) -> Dict[str, Any]:
        """停止代理服务"""
        try:
            if self.master:
                self.master.shutdown()
                self.master = None
            
            self.is_running = False
            
            # 清除代理模式状态
            self.proxy_mode = None
            self.proxy_mode_info = None
            self.upstream_proxy = None
            self.smart_mode_enabled = False
            
            result = {
                'success': True,
                'message': '代理服务已停止'
            }
            
            # 智能恢复系统代理设置
            if auto_restore_system_proxy:
                if hasattr(self, 'original_proxy_backup') and self.original_proxy_backup:
                    # 恢复到原有代理设置
                    proxy_result = self._restore_original_proxy_settings(self.original_proxy_backup)
                    if proxy_result:
                        result['message'] += '，系统代理已恢复到原有设置'
                        result['system_proxy_restored'] = True
                        logging.info("✅ 系统代理已恢复到原有设置")
                    else:
                        result['message'] += '，系统代理恢复失败'
                        result['system_proxy_restored'] = False
                        logging.warning("⚠️ 系统代理恢复失败，可能需要手动设置")
                else:
                    # 没有原有设置，清除代理
                    proxy_result = self.system_proxy_manager.clear_system_proxy()
                    if proxy_result:
                        result['message'] += '，系统代理已清除'
                        result['system_proxy_restored'] = True
                        logging.info("✅ 系统代理已清除")
                    else:
                        result['message'] += '，系统代理清除失败'
                        result['system_proxy_restored'] = False
                        logging.warning("⚠️ 系统代理清除失败，可能需要手动清除")
            
            logging.info("代理抓包服务已停止")
            return result
            
        except Exception as e:
            logging.error(f"停止代理服务失败: {e}")
            return {
                'success': False,
                'error': f'停止失败: {str(e)}'
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {
            'success': True,
            'running': self.is_running,
            'port': self.port,
            'status': 'running' if self.is_running else 'stopped',
            'resources_count': len(self.collector.resources),
            'available': MITMPROXY_AVAILABLE
        }
        
        # 如果代理正在运行，添加代理模式信息
        if self.is_running and self.proxy_mode:
            status.update({
                'proxy_mode': self.proxy_mode,
                'proxy_mode_info': self.proxy_mode_info,
                'upstream_proxy': self.upstream_proxy,
                'smart_mode': self.smart_mode_enabled
            })
        
        return status
    
    def get_resources(self) -> Dict[str, Any]:
        """获取检测到的资源列表"""
        resources = [asdict(resource) for resource in self.collector.resources.values()]
        # 按检测时间倒序排列
        resources.sort(key=lambda x: x['detected_at'], reverse=True)
        
        return {
            'success': True,
            'resources': resources,
            'total': len(resources)
        }
    
    def clear_resources(self) -> Dict[str, Any]:
        """清空资源列表"""
        self.collector.resources.clear()
        return {
            'success': True,
            'message': '资源列表已清空'
        }
    
    def simulate_resources(self) -> Dict[str, Any]:
        """模拟检测资源（用于测试）"""
        test_resources = [
            MediaResource(
                id="test_1",
                url="https://example.com/video/sample.mp4",
                title="测试视频1",
                platform="generic",
                media_type="video",
                file_size=1024*1024*50,  # 50MB
                detected_at=datetime.now().isoformat()
            ),
            MediaResource(
                id="test_2", 
                url="https://video.example.com/playlist.m3u8",
                title="直播流测试",
                platform="generic",
                media_type="stream",
                detected_at=datetime.now().isoformat()
            )
        ]
        
        for resource in test_resources:
            self.collector.resources[resource.id] = resource
        
        return {
            'success': True,
            'message': f'已添加 {len(test_resources)} 个测试资源'
        }

    def install_certificate(self) -> Dict[str, Any]:
        """安装证书（一次性设置，类似res-downloader的安装过程）"""
        try:
            success = self.system_proxy_manager.install_certificate()
            if success:
                return {
                    'success': True,
                    'message': '证书安装成功，现在可以抓包HTTPS流量了'
                }
            else:
                return {
                    'success': False,
                    'error': '证书安装失败，可能需要管理员权限'
                }
        except Exception as e:
            return {
                'success': False,
                'error': f'证书安装失败: {str(e)}'
            }
    
    def check_system_proxy_status(self) -> Dict[str, Any]:
        """检查系统代理状态"""
        try:
            status = self.system_proxy_manager.check_proxy_settings()
            return {
                'success': True,
                **status
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'检查代理状态失败: {str(e)}'
            }

# mitmproxy 插件类
if MITMPROXY_AVAILABLE:
    class RequestAddon:
        def __init__(self, handler):
            self.handler = handler
        
        def request(self, flow: 'http.HTTPFlow'):
            self.handler(flow)
    
    class ResponseAddon:
        def __init__(self, handler):
            self.handler = handler
        
        def response(self, flow: 'http.HTTPFlow'):
            self.handler(flow)
    
    class SmartRoutingAddon:
        """智能路由插件 - 实现类似res-downloader的智能代理路由"""
        
        def __init__(self, upstream_proxy: str):
            self.upstream_proxy = upstream_proxy
            # 目标平台域名 - 这些域名直接抓包，其他走上游代理
            self.target_domains = {
                'bilibili.com', 'www.bilibili.com', 'api.bilibili.com',
                'douyin.com', 'www.douyin.com', 'aweme.snssdk.com',
                'youtube.com', 'www.youtube.com', 'googlevideo.com',
                'ixigua.com', 'www.ixigua.com',
                'qq.com', 'v.qq.com', 'video.qq.com',
                'weibo.com', 'weibo.cn', 'video.weibo.com',
                'kuaishou.com', 'www.kuaishou.com',
                'xiaohongshu.com', 'www.xiaohongshu.com'
            }
            logging.info(f"SmartRoutingAddon已初始化，上游代理: {upstream_proxy}")
            logging.info(f"目标域名: {self.target_domains}")
        
        def server_connect(self, flow):
            """在服务器连接时决定路由策略"""
            try:
                if hasattr(flow, 'request') and flow.request:
                    host = flow.request.host
                    
                    # 检查是否是目标域名或其子域名
                    is_target_domain = any(
                        host == domain or host.endswith('.' + domain)
                        for domain in self.target_domains
                    )
                    
                    if is_target_domain:
                        # 目标域名直接连接，用于媒体资源抓包
                        logging.debug(f"🎯 目标域名 {host} - 直接连接用于抓包")
                        # 不设置上游代理，直接连接
                        flow.server_conn.via = None
                    else:
                        # 其他域名使用上游代理，保持正常网络访问
                        logging.debug(f"🔗 非目标域名 {host} - 通过上游代理 {self.upstream_proxy}")
                        # 这里需要设置上游代理，但mitmproxy的API可能不同
                        # 实际实现可能需要调整
                        pass
                        
            except Exception as e:
                logging.error(f"SmartRoutingAddon路由决策失败: {e}")
        
        def request(self, flow):
            """处理请求，记录路由信息"""
            try:
                if hasattr(flow, 'request') and flow.request:
                    host = flow.request.host
                    is_target = any(
                        host == domain or host.endswith('.' + domain)
                        for domain in self.target_domains
                    )
                    
                    if is_target:
                        logging.debug(f"📡 抓包目标请求: {host}{flow.request.path}")
                    else:
                        logging.debug(f"🌐 代理转发请求: {host}{flow.request.path}")
                        
            except Exception as e:
                logging.debug(f"SmartRoutingAddon请求处理失败: {e}")

    def _restore_original_proxy_settings(self, original_proxy: Dict) -> bool:
        """
        恢复原有代理设置

        Args:
            original_proxy: 原有代理配置

        Returns:
            bool: 恢复是否成功
        """
        try:
            if not original_proxy.get('enabled', False):
                # 原来没有代理，清除当前代理设置
                logging.info("原来没有代理设置，清除当前代理")
                return self.system_proxy_manager.clear_system_proxy()

            # 原来有代理，恢复到原有设置
            proxy_host = original_proxy.get('host', '127.0.0.1')
            proxy_port = original_proxy.get('port', 8080)

            logging.info(f"恢复原有代理设置: {proxy_host}:{proxy_port}")
            return self.system_proxy_manager.set_system_proxy(
                proxy_host=proxy_host,
                proxy_port=proxy_port,
                proxy_type="http"
            )

        except Exception as e:
            logging.error(f"恢复原有代理设置失败: {e}")
            return False

# 全局代理实例
proxy_capture_instance = ProxyCapture()

def get_proxy_capture() -> ProxyCapture:
    """获取代理抓包实例"""
    return proxy_capture_instance