"""
增强的VideoSense代理解决方案
解决与Clash Pro等翻墙工具的代理冲突问题
"""

import subprocess
import json
import logging
import time
from typing import Dict, Any, Optional
import platform

logger = logging.getLogger(__name__)

class EnhancedProxyManager:
    """增强的代理管理器，专门解决与翻墙工具的冲突"""
    
    def __init__(self, port: int = 8899):
        self.port = port
        self.original_proxy_settings = {}
        self.clash_pro_detected = False
        
    def detect_clash_pro(self) -> Dict[str, Any]:
        """专门检测Clash Pro配置"""
        try:
            # 检查Clash Pro常见端口
            common_clash_ports = [65327, 7890, 7891, 1080, 9090]
            
            for port in common_clash_ports:
                # 尝试连接测试
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', port))
                sock.close()
                
                if result == 0:  # 端口开放
                    # 进一步验证是否为代理服务
                    proxy_info = {
                        'tool': 'Clash Pro',
                        'type': 'http',
                        'server': '127.0.0.1',
                        'port': port,
                        'detected_method': 'port_scan'
                    }
                    logger.info(f"检测到Clash Pro代理服务在端口 {port}")
                    return {'success': True, 'proxy_info': proxy_info}
                    
        except Exception as e:
            logger.warning(f"Clash Pro检测失败: {e}")
            
        return {'success': False, 'proxy_info': None}
    
    def detect_system_proxy_settings(self) -> Dict[str, Any]:
        """检测系统代理设置"""
        try:
            if platform.system() == 'Darwin':  # macOS
                # 检查Wi-Fi和以太网的代理设置
                for service in ['Wi-Fi', 'Ethernet']:
                    result = subprocess.run(
                        ['networksetup', '-getwebproxy', service],
                        capture_output=True, text=True, timeout=5
                    )
                    
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        if len(lines) >= 3:
                            enabled = 'Yes' in lines[0]
                            if enabled:
                                server = lines[1].split(': ')[1] if ': ' in lines[1] else ''
                                port = lines[2].split(': ')[1] if ': ' in lines[2] else ''
                                
                                if server and port:
                                    proxy_info = {
                                        'tool': self._identify_proxy_tool(server, int(port)) if port.isdigit() else 'Unknown',
                                        'type': 'http',
                                        'server': server,
                                        'port': int(port) if port.isdigit() else 0,
                                        'service': service,
                                        'detected_method': 'system_settings'
                                    }
                                    return {'success': True, 'proxy_info': proxy_info}
                                    
        except Exception as e:
            logger.warning(f"系统代理检测失败: {e}")
            
        return {'success': False, 'proxy_info': None}
    
    def _identify_proxy_tool(self, server: str, port: int) -> str:
        """识别代理工具类型"""
        if server == '127.0.0.1' or server == 'localhost':
            # 根据端口判断可能的工具
            clash_ports = [65327, 7890, 7891]
            if port in clash_ports:
                return 'Clash Pro'
            elif port == 1080:
                return 'SOCKS Proxy'
            elif port in [8080, 8888, 9090]:
                return 'HTTP Proxy'
        return 'Unknown Proxy Tool'
    
    def comprehensive_proxy_detection(self) -> Dict[str, Any]:
        """综合代理检测"""
        result = {
            'clash_pro': None,
            'system_proxy': None,
            'recommendation': None
        }
        
        # 1. 检测Clash Pro
        clash_result = self.detect_clash_pro()
        if clash_result['success']:
            result['clash_pro'] = clash_result['proxy_info']
            
        # 2. 检测系统代理设置
        system_result = self.detect_system_proxy_settings()
        if system_result['success']:
            result['system_proxy'] = system_result['proxy_info']
        
        # 3. 生成推荐
        if result['clash_pro'] or result['system_proxy']:
            # 找到最佳代理配置
            proxy_info = result['clash_pro'] or result['system_proxy']
            upstream_url = f"http://{proxy_info['server']}:{proxy_info['port']}"
            
            result['recommendation'] = {
                'use_smart_mode': True,
                'use_upstream_mode': True,
                'upstream_proxy': upstream_url,
                'message': f"检测到 {proxy_info['tool']}，建议使用智能代理链模式",
                'instructions': [
                    "VideoSense将使用upstream模式连接到现有代理",
                    "浏览器 → VideoSense(8899) → Clash Pro → 互联网",
                    "这样可以避免代理冲突，保持网络连接正常"
                ]
            }
        else:
            result['recommendation'] = {
                'use_smart_mode': False,
                'use_upstream_mode': False,
                'upstream_proxy': None,
                'message': "未检测到现有代理，可使用常规模式",
                'instructions': [
                    "VideoSense将直接设置系统代理",
                    "如果使用翻墙工具，请先启用其系统代理设置"
                ]
            }
            
        return result
    
    def start_smart_proxy(self) -> Dict[str, Any]:
        """启动智能代理模式"""
        try:
            # 先进行综合检测
            detection_result = self.comprehensive_proxy_detection()
            
            # 根据检测结果决定启动模式
            if detection_result['recommendation']['use_smart_mode']:
                # 智能模式：使用upstream代理链
                upstream_proxy = detection_result['recommendation']['upstream_proxy']
                logger.info(f"🔗 启动智能代理链模式，上游代理: {upstream_proxy}")
                
                # 调用现有的代理启动API，但明确指定参数
                import requests
                response = requests.post(
                    'http://localhost:8000/proxy/start',
                    params={
                        'auto_setup_proxy': False,  # 不自动设置系统代理
                        'smart_mode': True
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    result['detection_details'] = detection_result
                    return result
                else:
                    return {'success': False, 'error': f'API调用失败: {response.status_code}'}
                    
            else:
                # 常规模式
                logger.info("📡 启动常规代理模式")
                import requests
                response = requests.post(
                    'http://localhost:8000/proxy/start',
                    params={
                        'auto_setup_proxy': True,
                        'smart_mode': False
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    result['detection_details'] = detection_result
                    return result
                else:
                    return {'success': False, 'error': f'API调用失败: {response.status_code}'}
                    
        except Exception as e:
            logger.error(f"智能代理启动失败: {e}")
            return {'success': False, 'error': str(e)}

def test_enhanced_proxy_detection():
    """测试增强的代理检测功能"""
    manager = EnhancedProxyManager()
    
    print("🔍 开始综合代理检测...")
    result = manager.comprehensive_proxy_detection()
    
    print("\n📊 检测结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result

if __name__ == '__main__':
    test_enhanced_proxy_detection() 