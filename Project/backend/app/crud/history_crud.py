"""
转录历史记录CRUD操作
提供增删改查等数据库操作
"""

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from ..models.history import TranscriptionHistory

# --- Pydantic 模型 ---
# 用于创建记录时的数据验证
class HistoryCreate(BaseModel):
    filename: str
    file_size: Optional[int] = None
    transcription_text: str
    language: Optional[str] = None
    provider_used: Optional[str] = None
    confidence: Optional[float] = None
    char_count: Optional[int] = None
    is_mock: bool = False

    class Config:
        orm_mode = True

# 用于API响应的数据模型
class HistoryResponse(HistoryCreate):
    id: int
    created_at: datetime

class HistoryCRUD:
    """转录历史记录CRUD操作类"""
    
    @staticmethod
    def create_history(
        db: Session,
        filename: str,
        transcription_text: str,
        language: str,
        provider_used: str,
        file_size: Optional[int] = None,
        duration: Optional[float] = None,
        confidence: Optional[float] = None,
        char_count: Optional[int] = None,
        is_mock: bool = False
    ) -> TranscriptionHistory:
        """
        创建新的转录历史记录
        """
        # 如果没有提供字符数，自动计算
        if char_count is None:
            char_count = len(transcription_text)
        
        history = TranscriptionHistory(
            filename=filename,
            file_size=file_size,
            duration=duration,
            transcription_text=transcription_text,
            language=language,
            provider_used=provider_used,
            confidence=confidence,
            char_count=char_count,
            is_mock=is_mock
        )
        
        db.add(history)
        db.commit()
        db.refresh(history)
        return history
    
    @staticmethod
    def get_history_by_id(db: Session, history_id: int) -> Optional[TranscriptionHistory]:
        """
        根据ID获取转录历史记录
        """
        return db.query(TranscriptionHistory).filter(TranscriptionHistory.id == history_id).first()
    
    @staticmethod
    def get_all_histories(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        order_by: str = "created_at",
        order_direction: str = "desc"
    ) -> List[TranscriptionHistory]:
        """
        获取所有转录历史记录（分页）
        
        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 限制记录数
            order_by: 排序字段
            order_direction: 排序方向 (asc/desc)
        """
        query = db.query(TranscriptionHistory)
        
        # 处理排序
        if hasattr(TranscriptionHistory, order_by):
            order_column = getattr(TranscriptionHistory, order_by)
            if order_direction.lower() == "desc":
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        else:
            # 默认按创建时间倒序
            query = query.order_by(desc(TranscriptionHistory.created_at))
        
        return query.offset(skip).limit(limit).all()
    
    @staticmethod
    def search_histories(
        db: Session,
        search_text: str = None,
        provider: str = None,
        language: str = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[TranscriptionHistory]:
        """
        搜索转录历史记录
        
        Args:
            db: 数据库会话
            search_text: 搜索文本（在文件名和转录内容中搜索）
            provider: 转录服务提供商过滤
            language: 语言过滤
            skip: 跳过记录数
            limit: 限制记录数
        """
        query = db.query(TranscriptionHistory)
        
        # 文本搜索
        if search_text:
            search_pattern = f"%{search_text}%"
            query = query.filter(
                or_(
                    TranscriptionHistory.filename.like(search_pattern),
                    TranscriptionHistory.transcription_text.like(search_pattern)
                )
            )
        
        # 提供商过滤
        if provider:
            query = query.filter(TranscriptionHistory.provider_used == provider)
        
        # 语言过滤
        if language:
            query = query.filter(TranscriptionHistory.language == language)
        
        return query.order_by(desc(TranscriptionHistory.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def update_history(
        db: Session,
        history_id: int,
        update_data: Dict[str, Any]
    ) -> Optional[TranscriptionHistory]:
        """
        更新转录历史记录
        """
        history = db.query(TranscriptionHistory).filter(TranscriptionHistory.id == history_id).first()
        if not history:
            return None
        
        for field, value in update_data.items():
            if hasattr(history, field):
                setattr(history, field, value)
        
        history.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(history)
        return history
    
    @staticmethod
    def delete_history(db: Session, history_id: int) -> bool:
        """
        删除转录历史记录
        """
        history = db.query(TranscriptionHistory).filter(TranscriptionHistory.id == history_id).first()
        if not history:
            return False
        
        db.delete(history)
        db.commit()
        return True
    
    @staticmethod
    def get_history_count(db: Session) -> int:
        """
        获取历史记录总数
        """
        return db.query(TranscriptionHistory).count()
    
    @staticmethod
    def get_statistics(db: Session) -> Dict[str, Any]:
        """
        获取转录历史统计信息
        """
        total_count = db.query(TranscriptionHistory).count()
        
        # 按提供商统计
        provider_stats = {}
        providers = db.query(TranscriptionHistory.provider_used).distinct().all()
        for (provider,) in providers:
            count = db.query(TranscriptionHistory).filter(TranscriptionHistory.provider_used == provider).count()
            provider_stats[provider] = count
        
        # 按语言统计
        language_stats = {}
        languages = db.query(TranscriptionHistory.language).distinct().all()
        for (language,) in languages:
            count = db.query(TranscriptionHistory).filter(TranscriptionHistory.language == language).count()
            language_stats[language] = count
        
        # 总字符数统计
        total_chars = db.query(TranscriptionHistory.char_count).filter(
            TranscriptionHistory.char_count.isnot(None)
        ).all()
        total_char_count = sum(char_count for (char_count,) in total_chars if char_count)
        
        return {
            "total_count": total_count,
            "provider_stats": provider_stats,
            "language_stats": language_stats,
            "total_char_count": total_char_count
        }

# 创建全局实例
history_crud = HistoryCRUD()

def create_history_record(db: Session, history: HistoryCreate) -> TranscriptionHistory:
    """
    创建一个新的历史记录。
    """
    db_history = TranscriptionHistory(
        filename=history.filename,
        file_size=history.file_size,
        transcription_text=history.transcription_text,
        language=history.language,
        provider_used=history.provider_used,
        confidence=history.confidence,
        char_count=len(history.transcription_text) if history.char_count is None else history.char_count,
        is_mock=history.is_mock
    )
    db.add(db_history)
    db.commit()
    db.refresh(db_history)
    return db_history

def get_history_record_by_id(db: Session, history_id: int) -> Optional[TranscriptionHistory]:
    """
    根据ID获取单个历史记录。
    """
    return db.query(TranscriptionHistory).filter(TranscriptionHistory.id == history_id).first()

def get_all_history_records(db: Session, page: int = 1, page_size: int = 20):
    """
    分页获取所有历史记录。
    返回记录列表和总数。
    """
    offset = (page - 1) * page_size
    
    query = db.query(TranscriptionHistory).order_by(TranscriptionHistory.created_at.desc())
    
    total_count = query.count()
    records = query.offset(offset).limit(page_size).all()
    
    return records, total_count

def delete_history_record_by_id(db: Session, history_id: int) -> bool:
    """
    根据ID删除一个历史记录。
    如果删除成功，返回 True；如果记录不存在，返回 False。
    """
    db_history = get_history_record_by_id(db, history_id)
    if db_history:
        db.delete(db_history)
        db.commit()
        return True
    return False 