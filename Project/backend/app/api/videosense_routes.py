"""
VideoSense 统一API路由
提供一键启动/停止等核心功能的HTTP接口
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging

from ..core.videosense_core import (
    get_videosense_core, 
    start_videosense,
    stop_videosense,
    get_videosense_status,
    VideoSenseConfig,
    ProxyMode
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/videosense", tags=["VideoSense Core"])


class VideoSenseConfigRequest(BaseModel):
    """VideoSense配置请求"""
    proxy_port: Optional[int] = 8899
    proxy_mode: Optional[str] = "auto"  # auto, direct, chain
    auto_start_proxy: Optional[bool] = True
    auto_install_cert: Optional[bool] = True
    backup_proxy_settings: Optional[bool] = True
    restore_on_exit: Optional[bool] = True


@router.get("/status")
async def get_status():
    """
    获取VideoSense运行状态
    
    Returns:
        Dict: 详细状态信息
    """
    try:
        result = get_videosense_status()
        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start")
async def start(config: Optional[VideoSenseConfigRequest] = None):
    """
    一键启动VideoSense
    
    Args:
        config: 可选的配置参数
        
    Returns:
        Dict: 启动结果
    """
    try:
        # 转换配置
        vs_config = None
        if config:
            proxy_mode = ProxyMode.AUTO
            if config.proxy_mode == "direct":
                proxy_mode = ProxyMode.DIRECT
            elif config.proxy_mode == "chain":
                proxy_mode = ProxyMode.CHAIN
            
            vs_config = VideoSenseConfig(
                proxy_port=config.proxy_port or 8899,
                proxy_mode=proxy_mode,
                auto_start_proxy=config.auto_start_proxy,
                auto_install_cert=config.auto_install_cert,
                backup_proxy_settings=config.backup_proxy_settings,
                restore_on_exit=config.restore_on_exit
            )
        
        result = await start_videosense(vs_config)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["message"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop():
    """
    停止VideoSense
    
    Returns:
        Dict: 停止结果
    """
    try:
        result = await stop_videosense()
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["message"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/restart")
async def restart(config: Optional[VideoSenseConfigRequest] = None):
    """
    重启VideoSense
    
    Args:
        config: 可选的配置参数
        
    Returns:
        Dict: 重启结果
    """
    try:
        core = get_videosense_core()
        
        # 如果提供了新配置，需要先停止再用新配置启动
        if config:
            # 停止当前实例
            await stop_videosense()
            
            # 用新配置启动
            return await start(config)
        else:
            # 直接重启
            result = await core.restart()
            
            if result["success"]:
                return result
            else:
                raise HTTPException(status_code=400, detail=result["message"])
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重启失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """
    健康检查
    
    Returns:
        Dict: 健康状态
    """
    try:
        core = get_videosense_core()
        status = core.get_status()
        
        return {
            "success": True,
            "healthy": status["success"],
            "status": status["data"]["status"] if status["success"] else "error",
            "timestamp": status["data"].get("startup_time") if status["success"] else None
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "healthy": False,
            "error": str(e)
        }


@router.get("/config")
async def get_config():
    """
    获取当前配置
    
    Returns:
        Dict: 配置信息
    """
    try:
        core = get_videosense_core()
        config = core.config
        
        return {
            "success": True,
            "data": {
                "proxy_port": config.proxy_port,
                "proxy_mode": config.proxy_mode.value,
                "auto_start_proxy": config.auto_start_proxy,
                "auto_install_cert": config.auto_install_cert,
                "backup_proxy_settings": config.backup_proxy_settings,
                "restore_on_exit": config.restore_on_exit
            }
        }
        
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config")
async def update_config(config: VideoSenseConfigRequest):
    """
    更新配置（需要重启才能生效）
    
    Args:
        config: 新的配置
        
    Returns:
        Dict: 更新结果
    """
    try:
        core = get_videosense_core()
        
        # 转换代理模式
        proxy_mode = ProxyMode.AUTO
        if config.proxy_mode == "direct":
            proxy_mode = ProxyMode.DIRECT
        elif config.proxy_mode == "chain":
            proxy_mode = ProxyMode.CHAIN
        
        # 更新配置
        core.config.proxy_port = config.proxy_port or core.config.proxy_port
        core.config.proxy_mode = proxy_mode
        core.config.auto_start_proxy = config.auto_start_proxy
        core.config.auto_install_cert = config.auto_install_cert
        core.config.backup_proxy_settings = config.backup_proxy_settings
        core.config.restore_on_exit = config.restore_on_exit
        
        return {
            "success": True,
            "message": "配置更新成功，重启后生效",
            "data": {
                "proxy_port": core.config.proxy_port,
                "proxy_mode": core.config.proxy_mode.value,
                "auto_start_proxy": core.config.auto_start_proxy,
                "auto_install_cert": core.config.auto_install_cert,
                "backup_proxy_settings": core.config.backup_proxy_settings,
                "restore_on_exit": core.config.restore_on_exit
            }
        }
        
    except Exception as e:
        logger.error(f"更新配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/initialize")
async def initialize():
    """
    初始化VideoSense环境（不启动服务）
    
    Returns:
        Dict: 初始化结果
    """
    try:
        core = get_videosense_core()
        result = await core.initialize()
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["message"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 兼容旧版本的端点（重定向到新的统一接口）
@router.get("/proxy/status", deprecated=True)
async def legacy_proxy_status():
    """兼容旧版本的代理状态接口"""
    return await get_status()


@router.post("/proxy/start", deprecated=True)
async def legacy_proxy_start():
    """兼容旧版本的代理启动接口"""
    return await start()


@router.post("/proxy/stop", deprecated=True)
async def legacy_proxy_stop():
    """兼容旧版本的代理停止接口"""
    return await stop() 