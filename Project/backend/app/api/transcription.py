import io
import tempfile
import os
import requests
from fastapi import APIRouter, UploadFile, File, HTTPException, WebSocket, WebSocketDisconnect, Form, Depends
from fastapi.responses import JSONResponse
import asyncio
from typing import Dict, List, Optional
import json
import wave
import numpy as np
import logging
import uuid
from sqlalchemy.orm import Session

# 导入数据库相关
from ..core.database import get_db
from ..crud.history_crud import (
    create_history_record, 
    get_all_history_records, 
    delete_history_record_by_id,
    HistoryCreate
)

# 安全导入API配置
try:
    from config.api_keys import SILICONFLOW_API_KEY, SILICONFLOW_BASE_URL, SILICONFLOW_MODEL
    API_AVAILABLE = True
except ImportError:
    # 开发环境备用方案
    SILICONFLOW_API_KEY = None
    SILICONFLOW_BASE_URL = None
    SILICONFLOW_MODEL = None
    API_AVAILABLE = False
    logging.warning("API配置未找到，使用Mock模式")

router = APIRouter()

# 全局变量
active_connections: List[WebSocket] = []
logger = logging.getLogger(__name__)

# 创建上传目录
UPLOAD_DIR = "uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

def transcribe_audio_file(file_path: str, language: str = "auto", provider: str = "siliconflow") -> Dict:
    """
    转录音频文件
    """
    try:
        if not API_AVAILABLE or not SILICONFLOW_API_KEY:
            # Mock模式
            return {
                "transcript": f"这是文件 {os.path.basename(file_path)} 的模拟转录结果。请配置 SiliconFlow API 密钥以获得真实转录。",
                "confidence": 0.85,
                "language": language,
                "provider": "mock"
            }
        
        # 读取音频文件
        with open(file_path, 'rb') as f:
            audio_data = f.read()
        
        # 调用SiliconFlow API
        url = f"{SILICONFLOW_BASE_URL}/audio/transcriptions"
        
        files = {
            'file': (os.path.basename(file_path), audio_data, 'audio/wav'),
            'model': (None, SILICONFLOW_MODEL)
        }
        
        headers = {
            'Authorization': f"Bearer {SILICONFLOW_API_KEY}"
        }
        
        response = requests.post(url, files=files, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            return {
                "transcript": result.get("text", ""),
                "confidence": 0.9,
                "language": language,
                "provider": "siliconflow"
            }
        else:
            logger.error(f"SiliconFlow API错误: {response.status_code} - {response.text}")
            # 失败时返回Mock结果
            return {
                "transcript": f"转录服务暂时不可用，这是文件 {os.path.basename(file_path)} 的模拟转录结果。",
                "confidence": 0.5,
                "language": language,
                "provider": "mock_fallback"
            }
            
    except Exception as e:
        logger.error(f"转录处理错误: {e}")
        # 错误时返回Mock结果
        return {
            "transcript": f"转录过程中出现错误，这是文件 {os.path.basename(file_path)} 的模拟转录结果。错误: {str(e)}",
            "confidence": 0.3,
            "language": language,
            "provider": "error_fallback"
        }

class SiliconFlowTranscriber:
    """SiliconFlow语音转文本客户端"""
    
    def __init__(self):
        self.config = None
        self._load_config()
    
    def _load_config(self):
        """加载API配置"""
        try:
            if API_AVAILABLE and SILICONFLOW_API_KEY:
                self.config = {
                    'api_key': SILICONFLOW_API_KEY,
                    'base_url': SILICONFLOW_BASE_URL,
                    'model': SILICONFLOW_MODEL
                }
                logger.info("SiliconFlow API配置加载成功")
            else:
                logger.warning("SiliconFlow API密钥未配置或无效，将使用Mock模式")
                self.config = None
        except Exception as e:
            logger.error(f"加载SiliconFlow配置失败: {e}")
            self.config = None
    
    async def transcribe(self, audio_data: bytes, filename: str = "audio.wav") -> Dict:
        """
        使用SiliconFlow API进行语音转文本
        """
        if not self.config:
            # Mock模式
            return await self._mock_transcribe(audio_data)
        
        try:
            url = f"{self.config['base_url']}/audio/transcriptions"
            
            # 准备multipart/form-data请求
            files = {
                'file': (filename, audio_data, 'audio/wav'),
                'model': (None, self.config['model'])
            }
            
            headers = {
                'Authorization': f"Bearer {self.config['api_key']}"
            }
            
            # 发送请求
            response = requests.post(url, files=files, headers=headers, timeout=30)
            
            # 处理响应状态码
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "text": result.get("text", ""),
                    "service": "siliconflow",
                    "model": self.config['model']
                }
            elif response.status_code == 400:
                raise HTTPException(status_code=400, detail="无效的音频文件或请求参数")
            elif response.status_code == 401:
                raise HTTPException(status_code=401, detail="API密钥无效或未授权")
            elif response.status_code == 404:
                raise HTTPException(status_code=404, detail="API端点未找到")
            elif response.status_code == 429:
                raise HTTPException(status_code=429, detail="请求过于频繁，请稍后再试")
            elif response.status_code == 503:
                raise HTTPException(status_code=503, detail="服务暂时不可用，请稍后再试")
            elif response.status_code == 504:
                raise HTTPException(status_code=504, detail="请求超时，请稍后再试")
            else:
                raise HTTPException(status_code=500, detail=f"API请求失败: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"SiliconFlow API请求错误: {e}")
            # 网络错误时返回Mock结果
            return await self._mock_transcribe(audio_data)
        except Exception as e:
            logger.error(f"转录处理错误: {e}")
            raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")
    
    async def _mock_transcribe(self, audio_data: bytes) -> Dict:
        """Mock转录结果（开发和测试用）"""
        return {
            "success": True,
            "text": "这是一段Mock转录文本。真实的SiliconFlow API需要配置有效的API密钥。",
            "service": "mock",
            "model": "mock_model",
            "note": "请配置真实的SiliconFlow API密钥以获得实际转录结果"
        }

# 初始化转录器
transcriber = SiliconFlowTranscriber()

# 实时转录端点
@router.post("/realtime")
async def transcribe_realtime(audio: UploadFile = File(...)):
    """
    实时音频转录端点
    接收音频文件并立即返回转录结果
    使用SiliconFlow API进行转录
    """
    try:
        # 读取音频数据
        audio_data = await audio.read()
        
        # 验证文件大小（限制10MB以确保实时性）
        max_size = 10 * 1024 * 1024  # 10MB
        if len(audio_data) > max_size:
            raise HTTPException(status_code=413, detail="实时转录文件大小不能超过10MB")
        
        # 使用SiliconFlow API进行转录
        result = await transcriber.transcribe(audio_data, audio.filename or "audio.wav")
        
        # 添加额外的元数据
        result.update({
            "file_size": len(audio_data),
            "filename": audio.filename,
            "endpoint": "realtime"
        })
        
        return result
                
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"实时转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

# WebSocket实时转录
@router.websocket("/ws")
async def websocket_transcribe(websocket: WebSocket):
    """
    WebSocket实时转录
    支持持续的音频流转录
    使用SiliconFlow API进行实时转录
    """
    await websocket.accept()
    active_connections.append(websocket)
    
    try:
        while True:
            # 接收音频数据
            data = await websocket.receive_bytes()
            
            # 处理音频数据
            try:
                # 验证数据大小（WebSocket实时流限制5MB）
                max_size = 5 * 1024 * 1024  # 5MB
                if len(data) > max_size:
                    error_response = {
                        "type": "error",
                        "message": "音频数据块过大，请减少每次发送的数据量",
                        "timestamp": asyncio.get_event_loop().time()
                    }
                    await websocket.send_text(json.dumps(error_response))
                    continue
                
                # 使用SiliconFlow API进行转录
                result = await transcriber.transcribe(data, "stream.wav")
                
                # 发送转录结果
                response = {
                    "type": "transcription",
                    "text": result.get("text", ""),
                    "service": result.get("service", "unknown"),
                    "model": result.get("model", "unknown"),
                    "success": result.get("success", False),
                    "timestamp": asyncio.get_event_loop().time(),
                    "data_size": len(data)
                }
                
                # 如果有额外信息（如Mock模式提示）
                if "note" in result:
                    response["note"] = result["note"]
                
                await websocket.send_text(json.dumps(response))
                        
            except Exception as e:
                logger.error(f"WebSocket转录错误: {e}")
                error_response = {
                    "type": "error",
                    "message": f"转录错误: {str(e)}",
                    "timestamp": asyncio.get_event_loop().time()
                }
                await websocket.send_text(json.dumps(error_response))
                
    except WebSocketDisconnect:
        active_connections.remove(websocket)
        logger.info("WebSocket客户端断开连接")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        if websocket in active_connections:
            active_connections.remove(websocket)

# 批量转录端点（支持大文件）
@router.post("/batch")
async def transcribe_batch(audio: UploadFile = File(...)):
    """
    批量转录端点
    适用于较大的音频文件
    使用SiliconFlow API进行批量转录
    """
    try:
        # 检查文件大小（限制50MB，SiliconFlow API限制）
        max_size = 50 * 1024 * 1024  # 50MB
        audio_data = await audio.read()
        
        if len(audio_data) > max_size:
            raise HTTPException(status_code=413, detail="文件过大，请选择小于50MB的音频文件")
        
        # 使用SiliconFlow API进行转录
        result = await transcriber.transcribe(audio_data, audio.filename or "batch.wav")
        
        # 扩展批量转录结果
        enhanced_result = {
            **result,
            "file_size": len(audio_data),
            "filename": audio.filename,
            "endpoint": "batch",
            "word_count": len(result.get("text", "").split()) if result.get("text") else 0,
            "processing_mode": "batch"
        }
        
        # 如果是实际API响应，添加更多分析
        if result.get("service") == "siliconflow":
            text = result.get("text", "")
            enhanced_result.update({
                "character_count": len(text),
                "estimated_duration": len(text) / 150,  # 假设150字符/分钟
                "language_detected": "auto-detected"  # SiliconFlow可能提供语言检测
            })
        
        return enhanced_result
                
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"批量转录错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量转录失败: {str(e)}")

# API状态检查
@router.get("/status")
async def get_api_status():
    """
    检查转录API状态和配置
    """
    status = {
        "service": "VideoSense Transcription API",
        "version": "1.0.0",
        "timestamp": asyncio.get_event_loop().time()
    }
    
    # 检查SiliconFlow API配置状态
    if transcriber.config:
        status.update({
            "siliconflow_status": "configured",
            "model": transcriber.config.get("model", "unknown"),
            "api_endpoint": transcriber.config.get("base_url", "unknown")
        })
    else:
        status.update({
            "siliconflow_status": "not_configured",
            "mode": "mock",
            "message": "请配置SiliconFlow API密钥以启用真实转录功能"
        })
    
    return status

# 支持的格式检查  
@router.get("/formats")
async def get_supported_formats():
    """
    获取支持的音频格式列表
    """
    return {
        "audio_formats": [
            "wav", "mp3", "m4a", "flac", "ogg", "wma", "aac"
        ],
        "video_formats": [
            "mp4", "avi", "mov", "mkv", "webm", "flv", "m4v"
        ],
        "streaming_formats": [
            "m3u8", "mpd", "rtmp", "hls"
        ],
        "max_file_size": "100MB",
        "supported_languages": [
            "zh", "en", "ja", "ko", "es", "fr", "de", "it", "pt", "ru"
        ]
    }

# 健康检查
@router.get("/health")
async def health_check():
    """
    转录服务健康检查
    """
    return {
        "status": "healthy",
        "service": "transcription",
        "siliconflow_configured": transcriber.config is not None,
        "active_connections": len(active_connections),
        "api_mode": "siliconflow" if transcriber.config else "mock"
    }

# 辅助函数
def get_average_confidence(segments: List[Dict]) -> float:
    """
    计算所有段落的平均置信度
    """
    if not segments:
        return 0.9  # 默认置信度
    
    confidences = [segment.get("avg_logprob", 0.0) for segment in segments]
    if not confidences:
        return 0.9
    
    # 将logprob转换为0-1的置信度
    avg_logprob = sum(confidences) / len(confidences)
    confidence = min(1.0, max(0.0, (avg_logprob + 1.0)))  # 简单转换
    return round(confidence, 3)

def get_audio_duration(segments: List[Dict]) -> float:
    """
    获取音频总时长
    """
    if not segments:
        return 0.0
    
    last_segment = segments[-1]
    return last_segment.get("end", 0.0)

# API配置验证端点
@router.post("/validate-config")
async def validate_api_config():
    """
    验证API配置是否正确
    发送测试请求到SiliconFlow API
    """
    if not transcriber.config:
        return {
            "valid": False,
            "message": "API配置未找到，请配置SiliconFlow API密钥",
            "suggestion": "复制 backend/config/api_keys.example.py 为 api_keys.py 并填入真实密钥"
        }
    
    try:
        # 创建测试音频数据（1秒的静音WAV）
        import struct
        sample_rate = 16000
        duration = 1  # 1秒
        samples = [0] * (sample_rate * duration)
        
        # 创建WAV格式的字节数据
        test_audio = b'RIFF'
        test_audio += struct.pack('<I', 36 + len(samples) * 2)
        test_audio += b'WAVE'
        test_audio += b'fmt '
        test_audio += struct.pack('<I', 16)  # fmt chunk size
        test_audio += struct.pack('<H', 1)   # audio format (PCM)
        test_audio += struct.pack('<H', 1)   # channels
        test_audio += struct.pack('<I', sample_rate)  # sample rate
        test_audio += struct.pack('<I', sample_rate * 2)  # byte rate
        test_audio += struct.pack('<H', 2)   # block align
        test_audio += struct.pack('<H', 16)  # bits per sample
        test_audio += b'data'
        test_audio += struct.pack('<I', len(samples) * 2)
        
        for sample in samples:
            test_audio += struct.pack('<h', sample)
        
        # 发送测试请求
        result = await transcriber.transcribe(test_audio, "test.wav")
        
        if result.get("success") and result.get("service") == "siliconflow":
            return {
                "valid": True,
                "message": "SiliconFlow API配置有效",
                "model": result.get("model"),
                "test_result": result.get("text", "")
            }
        else:
            return {
                "valid": False,
                "message": "API测试失败",
                "details": result
            }
            
    except Exception as e:
        return {
            "valid": False,
            "message": f"配置验证失败: {str(e)}",
            "suggestion": "请检查API密钥是否正确"
        }

# 启动事件
@router.on_event("startup")
async def startup_event():
    """
    API启动时的初始化
    """
    logger.info("VideoSense转录API启动")
    logger.info(f"SiliconFlow配置状态: {'已配置' if transcriber.config else '未配置'}")

@router.post("/transcribe")
async def transcribe_file(
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    language: Optional[str] = Form("auto"),
    provider: Optional[str] = Form("siliconflow"),
    db: Session = Depends(get_db)
):
    """
    文件转录端点
    """
    logger.debug("=== 转录API调用开始 ===")
    logger.debug(f"接收到文件: {file.filename}")
    logger.debug(f"文件大小: {file.size}")
    logger.debug(f"标题: {title}")
    logger.debug(f"语言: {language}")
    logger.debug(f"提供商: {provider}")
    
    try:
        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(UPLOAD_DIR, unique_filename)
        
        logger.debug(f"保存文件到: {file_path}")
        
        # 保存上传的文件
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.debug(f"文件保存成功，大小: {len(content)} 字节")
        
        # 计算文件大小（字节）
        file_size = len(content)
        logger.debug(f"文件大小: {file_size} 字节")
        
        # 执行转录
        logger.debug("开始转录处理...")
        result = transcribe_audio_file(
            file_path=file_path,
            language=language,
            provider=provider
        )
        logger.debug(f"转录结果: {result}")
        
        # 使用文件名作为标题（如果未提供）
        if not title:
            title = os.path.splitext(file.filename)[0]
        
        logger.debug(f"使用标题: {title}")
        
        # 保存到历史记录
        logger.debug("保存到历史记录...")
        
        history_data = HistoryCreate(
            filename=file.filename,
            file_size=file_size,
            transcription_text=result.get('transcript', ''),
            language=language,
            provider_used=provider,
            char_count=len(result.get('transcript', '')),
            is_mock=provider in ['mock', 'mock_fallback', 'error_fallback']
        )
        
        history_record = create_history_record(db, history_data)
        logger.debug(f"历史记录创建成功，ID: {history_record.id}")
        
        # 清理临时文件
        try:
            os.remove(file_path)
            logger.debug("临时文件删除成功")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {e}")
        
        response_data = {
            "success": True,
            "message": "转录完成",
            "history_id": history_record.id,
            "data": {
                "id": history_record.id,
                "filename": history_record.filename,
                "transcript": result.get('transcript', ''),
                "language": history_record.language,
                "provider": history_record.provider_used,
                "char_count": history_record.char_count,
                "created_at": history_record.created_at.isoformat()
            }
        }
        
        logger.debug("=== 转录API调用成功完成 ===")
        logger.debug(f"响应数据: {response_data}")
        
        return response_data
        
    except Exception as e:
        logger.error("=== 转录API调用失败 ===")
        logger.error(f"错误详情: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        
        # 清理文件
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.debug("错误处理：临时文件已删除")
            except:
                pass
        
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

@router.get("/history")
async def get_history(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    language: Optional[str] = None,
    provider: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取历史记录端点
    """
    logger.debug("=== 历史记录查询API调用开始 ===")
    logger.debug(f"查询参数 - 页码: {page}, 每页数量: {page_size}")
    logger.debug(f"搜索关键词: {search}")
    logger.debug(f"语言过滤: {language}")
    logger.debug(f"提供商过滤: {provider}")
    
    try:
        records, total_count = get_all_history_records(db, page=page, page_size=page_size)
        
        logger.debug(f"查询结果 - 总记录数: {total_count}, 当前页记录数: {len(records)}")
        
        response_data = {
            "success": True,
            "data": {
                "records": records,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            }
        }
        
        logger.debug("=== 历史记录查询API调用成功完成 ===")
        logger.debug(f"响应数据结构: 记录数={len(records)}, 总数={total_count}")
        
        return response_data
        
    except Exception as e:
        logger.error("=== 历史记录查询API调用失败 ===")
        logger.error(f"错误详情: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")

@router.delete("/history/{record_id}")
async def delete_history(record_id: int, db: Session = Depends(get_db)):
    """
    删除历史记录端点
    """
    logger.debug("=== 删除历史记录API调用开始 ===")
    logger.debug(f"要删除的记录ID: {record_id}")
    
    try:
        success = delete_history_record_by_id(db, record_id)
        
        if success:
            response_data = {
                "success": True,
                "message": "历史记录删除成功"
            }
            logger.debug("=== 删除历史记录API调用成功完成 ===")
            return response_data
        else:
            logger.warning(f"未找到记录ID: {record_id}")
            raise HTTPException(status_code=404, detail="未找到要删除的记录")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("=== 删除历史记录API调用失败 ===")
        logger.error(f"错误详情: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        
        raise HTTPException(status_code=500, detail=f"删除记录失败: {str(e)}") 