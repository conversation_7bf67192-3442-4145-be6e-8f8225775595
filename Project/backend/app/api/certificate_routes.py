"""
VideoSense 证书管理 API 路由
提供证书生成、安装、检查等功能的 RESTful API
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Optional
import logging
from pathlib import Path

from ..core.certificate_manager import CertificateManager
from ..core.system_proxy_manager import SystemProxyManager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/certificate", tags=["证书管理"])

# 全局证书管理器实例
cert_manager = CertificateManager()


@router.get("/status", summary="获取证书状态")
async def get_certificate_status() -> Dict:
    """
    获取当前证书状态和详细信息
    
    Returns:
        Dict: 证书状态信息
    """
    try:
        status = cert_manager.check_certificate_validity()
        info = cert_manager.get_certificate_info()
        
        return {
            "success": True,
            "data": {
                "status": status,
                "info": info
            }
        }
    except Exception as e:
        logger.error(f"获取证书状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取证书状态失败: {str(e)}")


@router.post("/setup", summary="一键设置证书")
async def setup_certificates(
    background_tasks: BackgroundTasks,
    force_regenerate: Optional[bool] = False
) -> Dict:
    """
    一键设置证书（生成+安装）
    
    Args:
        force_regenerate: 是否强制重新生成证书
        
    Returns:
        Dict: 设置结果
    """
    try:
        # 在后台任务中执行证书设置（因为可能需要用户权限）
        success = cert_manager.setup_certificates(force_regenerate=force_regenerate)
        
        if success:
            return {
                "success": True,
                "message": "证书设置成功",
                "data": cert_manager.get_certificate_info()
            }
        else:
            raise HTTPException(status_code=500, detail="证书设置失败")
            
    except Exception as e:
        logger.error(f"证书设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"证书设置失败: {str(e)}")


@router.post("/generate", summary="生成证书")
async def generate_certificates() -> Dict:
    """
    生成CA证书和服务器证书
    
    Returns:
        Dict: 生成结果
    """
    try:
        # 生成CA证书
        ca_key_path, ca_cert_path = cert_manager.generate_ca_certificate()
        
        # 生成服务器证书
        server_key_path, server_cert_path = cert_manager.generate_server_certificate()
        
        return {
            "success": True,
            "message": "证书生成成功",
            "data": {
                "ca_cert_path": ca_cert_path,
                "server_cert_path": server_cert_path,
                "server_key_path": server_key_path
            }
        }
    except Exception as e:
        logger.error(f"证书生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"证书生成失败: {str(e)}")


@router.post("/install", summary="安装CA证书")
async def install_ca_certificate() -> Dict:
    """
    将CA证书安装到系统信任库
    
    Returns:
        Dict: 安装结果
    """
    try:
        success = cert_manager.install_ca_certificate()
        
        if success:
            return {
                "success": True,
                "message": "CA证书安装成功"
            }
        else:
            raise HTTPException(status_code=500, detail="CA证书安装失败")
            
    except Exception as e:
        logger.error(f"CA证书安装失败: {e}")
        raise HTTPException(status_code=500, detail=f"CA证书安装失败: {str(e)}")


@router.delete("/uninstall", summary="卸载CA证书")
async def uninstall_ca_certificate() -> Dict:
    """
    从系统信任库卸载CA证书
    
    Returns:
        Dict: 卸载结果
    """
    try:
        success = cert_manager.uninstall_ca_certificate()
        
        if success:
            return {
                "success": True,
                "message": "CA证书卸载成功"
            }
        else:
            raise HTTPException(status_code=500, detail="CA证书卸载失败")
            
    except Exception as e:
        logger.error(f"CA证书卸载失败: {e}")
        raise HTTPException(status_code=500, detail=f"CA证书卸载失败: {str(e)}")


@router.get("/paths", summary="获取证书路径")
async def get_certificate_paths() -> Dict:
    """
    获取证书文件路径信息
    
    Returns:
        Dict: 证书路径信息
    """
    try:
        info = cert_manager.get_certificate_info()
        
        return {
            "success": True,
            "data": {
                "cert_dir": info["cert_dir"],
                "ca_cert_path": info["ca_cert_path"],
                "server_cert_path": info["server_cert_path"],
                "platform": info["platform"]
            }
        }
    except Exception as e:
        logger.error(f"获取证书路径失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取证书路径失败: {str(e)}")


@router.get("/ca-content", summary="获取CA证书内容")
async def get_ca_certificate_content() -> Dict:
    """
    获取CA证书文件内容（用于手动安装）
    
    Returns:
        Dict: CA证书内容
    """
    try:
        ca_cert_path = cert_manager.ca_cert_path
        
        if not ca_cert_path.exists():
            raise HTTPException(status_code=404, detail="CA证书文件不存在")
        
        with open(ca_cert_path, 'r', encoding='utf-8') as f:
            cert_content = f.read()
        
        return {
            "success": True,
            "data": {
                "cert_content": cert_content,
                "cert_path": str(ca_cert_path),
                "filename": "videosense-ca-cert.pem"
            }
        }
    except Exception as e:
        logger.error(f"获取CA证书内容失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取CA证书内容失败: {str(e)}")


@router.post("/validate", summary="验证证书")
async def validate_certificates() -> Dict:
    """
    验证证书的有效性和配置
    
    Returns:
        Dict: 验证结果
    """
    try:
        status = cert_manager.check_certificate_validity()
        
        # 检查证书文件权限
        permissions_ok = True
        permission_details = {}
        
        for cert_type, path_attr in [
            ("ca_cert", "ca_cert_path"),
            ("server_cert", "server_cert_path"),
            ("server_key", "server_key_path")
        ]:
            cert_path = getattr(cert_manager, path_attr)
            if cert_path.exists():
                try:
                    # 检查文件是否可读
                    with open(cert_path, 'rb') as f:
                        f.read(1)
                    permission_details[cert_type] = "可读"
                except Exception as e:
                    permissions_ok = False
                    permission_details[cert_type] = f"权限错误: {e}"
            else:
                permission_details[cert_type] = "文件不存在"
        
        return {
            "success": True,
            "data": {
                "validity": status,
                "permissions": {
                    "all_ok": permissions_ok,
                    "details": permission_details
                }
            }
        }
    except Exception as e:
        logger.error(f"证书验证失败: {e}")
        raise HTTPException(status_code=500, detail=f"证书验证失败: {str(e)}")


@router.post("/test-connection", summary="测试HTTPS连接")
async def test_https_connection() -> Dict:
    """
    测试使用生成的证书的HTTPS连接
    
    Returns:
        Dict: 连接测试结果
    """
    try:
        import ssl
        import socket
        from urllib.parse import urlparse
        
        # 检查证书是否存在
        if not cert_manager.server_cert_path.exists() or not cert_manager.server_key_path.exists():
            raise HTTPException(status_code=404, detail="服务器证书不存在")
        
        # 创建SSL上下文
        context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
        context.load_cert_chain(
            str(cert_manager.server_cert_path),
            str(cert_manager.server_key_path)
        )
        
        # 测试本地连接
        test_host = "127.0.0.1"
        test_port = 8899  # VideoSense代理端口
        
        try:
            with socket.create_connection((test_host, test_port), timeout=5) as sock:
                with context.wrap_socket(sock, server_hostname=test_host) as ssock:
                    ssl_info = {
                        "cipher": ssock.cipher(),
                        "version": ssock.version(),
                        "peer_cert": ssock.getpeercert()
                    }
        except Exception as conn_error:
            ssl_info = {"error": f"连接测试失败: {conn_error}"}
        
        return {
            "success": True,
            "data": {
                "certificate_files_exist": True,
                "ssl_context_created": True,
                "connection_test": ssl_info
            }
        }
    except Exception as e:
        logger.error(f"HTTPS连接测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"HTTPS连接测试失败: {str(e)}")


# 集成系统代理检查
@router.get("/proxy-integration", summary="检查代理集成状态")
async def check_proxy_integration() -> Dict:
    """
    检查证书与系统代理的集成状态
    
    Returns:
        Dict: 集成状态信息
    """
    try:
        # 检查证书状态
        cert_status = cert_manager.check_certificate_validity()
        
        # 检查系统代理状态
        proxy_manager = SystemProxyManager()
        proxy_settings = proxy_manager.get_current_proxy_settings()
        detected_proxies = proxy_manager.detect_proxy_software()
        
        # 分析集成状态
        integration_status = {
            "certificate_ready": cert_status.get("ca_valid", False) and cert_status.get("server_valid", False),
            "proxy_configured": False,
            "conflicts_detected": len(detected_proxies) > 0,
            "recommendation": "direct"
        }
        
        # 检查是否已配置VideoSense代理
        if proxy_settings.get("platform") == "macos":
            services = proxy_settings.get("services", {})
            for service, settings in services.items():
                http_proxy = settings.get("http", {})
                if http_proxy.get("enabled") and http_proxy.get("port") == "8899":
                    integration_status["proxy_configured"] = True
                    break
        
        # 提供建议
        if integration_status["conflicts_detected"]:
            integration_status["recommendation"] = "smart_chain"
        elif integration_status["certificate_ready"]:
            integration_status["recommendation"] = "ready_to_start"
        
        return {
            "success": True,
            "data": {
                "certificate_status": cert_status,
                "proxy_settings": proxy_settings,
                "detected_proxies": detected_proxies,
                "integration": integration_status
            }
        }
    except Exception as e:
        logger.error(f"代理集成检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理集成检查失败: {str(e)}") 