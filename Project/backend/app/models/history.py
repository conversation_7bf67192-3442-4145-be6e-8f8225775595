"""
转录历史记录数据模型
存储用户的转录历史信息
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean
from sqlalchemy.sql import func
from datetime import datetime
from ..core.database import Base

class TranscriptionHistory(Base):
    """
    转录历史记录 ORM 模型
    """
    __tablename__ = "transcription_history"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, comment="记录ID")
    
    # 文件信息
    filename = Column(String, nullable=False, comment="原始文件名")
    file_size = Column(Integer, comment="文件大小 (bytes)")
    duration = Column(Float, comment="音视频时长 (seconds)")
    
    # 转录结果
    transcription_text = Column(Text, nullable=False, comment="转录后的文本内容")
    language = Column(String, comment="识别出的语言")
    
    # 元数据
    provider_used = Column(String, comment="使用的转录服务提供商")
    confidence = Column(Float, comment="平均置信度")
    char_count = Column(Integer, comment="字符数")
    is_mock = Column(Boolean, default=False, comment="是否为模拟数据")
    
    # 时间戳 - 使用Python默认值而不是数据库server_default
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    
    def __repr__(self):
        return f"<TranscriptionHistory(id={self.id}, filename='{self.filename}')>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "filename": self.filename,
            "file_size": self.file_size,
            "duration": self.duration,
            "transcription_text": self.transcription_text,
            "language": self.language,
            "provider_used": self.provider_used,
            "confidence": self.confidence,
            "char_count": self.char_count,
            "is_mock": self.is_mock,
            "created_at": self.created_at.isoformat() if self.created_at else None
        } 