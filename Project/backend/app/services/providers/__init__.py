"""
转录服务提供商模块

此模块包含各种转录API的具体实现。
每个提供商都应继承TranscriptionProvider抽象类。

使用示例:
    from app.services.providers.openai_provider import OpenAIProvider
    from app.services.providers.baidu_provider import BaiduProvider
    
    # 注册提供商
    service = get_transcription_service()
    service.register_provider("openai", OpenAIProvider(api_key="your_key"))
    service.register_provider("baidu", BaiduProvider(api_key="your_key"))

支持的提供商:
- MockProvider: 开发测试用的模拟提供商 (已实现)
- OpenAIProvider: OpenAI Whisper API (待实现)
- BaiduProvider: 百度语音识别API (待实现)  
- AliCloudProvider: 阿里云语音识别API (待实现)
- LocalWhisperProvider: 本地Whisper模型 (待实现)
"""

# 导入已实现的提供商
from ..transcription import MockTranscriptionProvider

# 导出提供商类
__all__ = [
    'MockTranscriptionProvider',
    # 'OpenAIProvider',      # TODO: 实现后取消注释
    # 'BaiduProvider',       # TODO: 实现后取消注释
    # 'AliCloudProvider',    # TODO: 实现后取消注释
    # 'LocalWhisperProvider', # TODO: 实现后取消注释
]

# 提供商注册信息
AVAILABLE_PROVIDERS = {
    'mock': {
        'class': MockTranscriptionProvider,
        'name': 'Mock Provider (Development)',
        'description': '开发测试用的模拟转录服务',
        'languages': ['auto', 'zh', 'en', 'ja', 'ko'],
        'requires_api_key': False,
        'cost': 'free',
        'implemented': True
    },
    # TODO: 添加其他提供商的注册信息
    # 'openai': {
    #     'class': 'OpenAIProvider',
    #     'name': 'OpenAI Whisper',
    #     'description': 'OpenAI的高精度语音识别API',
    #     'languages': ['auto', 'zh', 'en', 'ja', 'ko', 'fr', 'de', 'es'],
    #     'requires_api_key': True,
    #     'cost': 'paid',
    #     'implemented': False
    # },
    # 'baidu': {
    #     'class': 'BaiduProvider', 
    #     'name': '百度语音识别',
    #     'description': '中文支持优秀的本土化API',
    #     'languages': ['zh', 'en'],
    #     'requires_api_key': True,
    #     'cost': 'paid',
    #     'implemented': False
    # }
}

def get_provider_info(provider_name: str = None):
    """获取提供商信息"""
    if provider_name:
        return AVAILABLE_PROVIDERS.get(provider_name)
    return AVAILABLE_PROVIDERS 