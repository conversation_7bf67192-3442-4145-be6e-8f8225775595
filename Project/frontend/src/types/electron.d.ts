// Electron preload API 类型定义

interface PermissionAPI {
  requestAuthorization(): Promise<{
    success: boolean
    cancelled?: boolean
    message?: string
    error?: string
  }>
  
  setSystemProxy(enable: boolean, host?: string, port?: number): Promise<{
    success: boolean
    message?: string
    error?: string
    networkService?: string
    requiresAuthorization?: boolean
  }>
  
  checkProxyStatus(): Promise<{
    webProxyEnabled: boolean
    httpsProxyEnabled: boolean
    isVideoSenseProxy: boolean
    canAutoManage: boolean
    networkService?: string
    platform: string
    error?: string
  }>
  
  getPermissionState(): Promise<{
    permissionGranted: boolean
    certificateInstalled: boolean
    platform: string
    platformSupported: boolean
  }>
  
  resetPermissions(): Promise<{
    success: boolean
    message: string
  }>

  // 新增证书管理功能
  installCertificate(): Promise<{
    success: boolean
    message?: string
    error?: string
    cancelled?: boolean
  }>

  checkCertificateStatus(): Promise<{
    installed: boolean
    platform: string
    error?: string
  }>
}

declare global {
  interface Window {
    permissionAPI: PermissionAPI
  }
}

export {} 