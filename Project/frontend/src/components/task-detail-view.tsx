"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ArrowLeft, Pause, XCircle, AlertTriangle, CheckCircle, Clock, Languages, Globe } from "lucide-react"

interface TaskDetailViewProps {
  taskId: number
  onBack: () => void
}

const translations = {
  zh: {
    taskDetail: "任务详情",
    pause: "暂停",
    cancel: "取消",
    source: "来源",
    duration: "时长",
    fileSize: "文件大小",
    format: "格式",
    overallProgress: "整体进度",
    processingSteps: "处理步骤",
    mediaDetection: "媒体检测",
    download: "下载",
    audioExtraction: "音频提取",
    aiTranscription: "AI转录",
    postProcessing: "后处理",
    completed: "已完成",
    processing: "处理中",
    pending: "等待中",
    failed: "失败",
    currentStatus: "当前正在执行AI转录，预计还需约15分钟完成。",
    processingLog: "处理日志",
    taskStarted: "任务开始",
    mediaDetectionComplete: "媒体检测完成",
    downloadStarted: "下载开始",
    downloadComplete: "下载完成",
    audioExtractionStarted: "音频提取开始",
    audioExtractionComplete: "音频提取完成",
    aiTranscriptionStarted: "AI转录开始",
    transcriptionInProgress: "转录进行中...",
  },
  en: {
    taskDetail: "Task Details",
    pause: "Pause",
    cancel: "Cancel",
    source: "Source",
    duration: "Duration",
    fileSize: "File Size",
    format: "Format",
    overallProgress: "Overall Progress",
    processingSteps: "Processing Steps",
    mediaDetection: "Media Detection",
    download: "Download",
    audioExtraction: "Audio Extraction",
    aiTranscription: "AI Transcription",
    postProcessing: "Post Processing",
    completed: "Completed",
    processing: "Processing",
    pending: "Pending",
    failed: "Failed",
    currentStatus: "Currently executing AI transcription, estimated 15 minutes remaining.",
    processingLog: "Processing Log",
    taskStarted: "Task started",
    mediaDetectionComplete: "Media detection complete",
    downloadStarted: "Download started",
    downloadComplete: "Download complete",
    audioExtractionStarted: "Audio extraction started",
    audioExtractionComplete: "Audio extraction complete",
    aiTranscriptionStarted: "AI transcription started",
    transcriptionInProgress: "Transcription in progress...",
  },
}

export default function TaskDetailView({ taskId, onBack }: TaskDetailViewProps) {
  const [language, setLanguage] = useState<"zh" | "en">("zh")
  const t = translations[language]

  const [task] = useState({
    id: taskId,
    title: "会议录像 - 项目进展讨论",
    status: "transcribing",
    progress: 65,
    duration: "1:23:15",
    createdAt: "2025-06-19 15:45",
    source: "YouTube",
    fileSize: "245 MB",
    format: "MP4",
    steps: [
      { name: t.mediaDetection, status: "completed", progress: 100 },
      { name: t.download, status: "completed", progress: 100 },
      { name: t.audioExtraction, status: "completed", progress: 100 },
      { name: t.aiTranscription, status: "processing", progress: 65 },
      { name: t.postProcessing, status: "pending", progress: 0 },
    ],
  })

  const getStepIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case "processing":
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />
      case "pending":
        return <Clock className="w-4 h-4 text-gray-400" />
      default:
        return <AlertTriangle className="w-4 h-4 text-red-500" />
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      completed: t.completed,
      processing: t.processing,
      pending: t.pending,
      failed: t.failed,
    }
    return statusMap[status] || status
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <h1 className="text-xl font-semibold text-gray-900">{t.taskDetail}</h1>
          </div>
          <div className="flex items-center gap-2">
            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Languages className="w-4 h-4 mr-2" />
                  {language === "zh" ? "中文" : "EN"}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setLanguage("zh")}>
                  <Globe className="w-4 h-4 mr-2" />
                  中文
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLanguage("en")}>
                  <Globe className="w-4 h-4 mr-2" />
                  English
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            {task.status === "transcribing" && (
              <Button variant="outline" size="sm">
                <Pause className="w-4 h-4 mr-2" />
                {t.pause}
              </Button>
            )}
            {task.status === "downloading" && (
              <Button variant="outline" size="sm">
                <XCircle className="w-4 h-4 mr-2" />
                {t.cancel}
              </Button>
            )}
          </div>
        </div>
      </header>

      <main className="p-6 max-w-4xl mx-auto space-y-6">
        {/* Task Overview */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>{task.title}</CardTitle>
              <Badge variant={task.status === "transcribing" ? "default" : "secondary"}>
                {language === "zh" ? "转录中" : "Transcribing"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500">{t.source}</span>
                <p className="font-medium">{task.source}</p>
              </div>
              <div>
                <span className="text-gray-500">{t.duration}</span>
                <p className="font-medium">{task.duration}</p>
              </div>
              <div>
                <span className="text-gray-500">{t.fileSize}</span>
                <p className="font-medium">{task.fileSize}</p>
              </div>
              <div>
                <span className="text-gray-500">{t.format}</span>
                <p className="font-medium">{task.format}</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{t.overallProgress}</span>
                <span>{task.progress}%</span>
              </div>
              <Progress value={task.progress} className="h-3" />
            </div>
          </CardContent>
        </Card>

        {/* Processing Steps */}
        <Card>
          <CardHeader>
            <CardTitle>{t.processingSteps}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {task.steps.map((step, index) => (
                <div key={index} className="flex items-center gap-4">
                  {getStepIcon(step.status)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium">{step.name}</span>
                      <span className="text-sm text-gray-500">{step.progress}%</span>
                    </div>
                    <Progress value={step.progress} className="h-2" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Current Status */}
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertDescription>{t.currentStatus}</AlertDescription>
        </Alert>

        {/* Logs */}
        <Card>
          <CardHeader>
            <CardTitle>{t.processingLog}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm font-mono bg-gray-50 p-4 rounded-lg max-h-64 overflow-y-auto">
              <div className="text-gray-600">[15:45:23] {t.taskStarted}</div>
              <div className="text-gray-600">[15:45:24] {t.mediaDetectionComplete}</div>
              <div className="text-gray-600">[15:45:25] {t.downloadStarted}</div>
              <div className="text-gray-600">[15:47:12] {t.downloadComplete} (245 MB)</div>
              <div className="text-gray-600">[15:47:13] {t.audioExtractionStarted}</div>
              <div className="text-gray-600">[15:47:45] {t.audioExtractionComplete}</div>
              <div className="text-blue-600">[15:47:46] {t.aiTranscriptionStarted}</div>
              <div className="text-blue-600">[15:52:30] {t.transcriptionInProgress} (65%)</div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
