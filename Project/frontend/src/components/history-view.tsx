"use client"

import { useState, useEffect } from "react"
import { historyApi, type HistoryRecord, type HistorySearchParams } from "@/lib/api"
import { useToast } from "@/components/toast-provider"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Search,
  Trash2,
  Eye,
  Copy,
  ChevronLeft,
  ChevronRight,
  FileText,
} from "lucide-react"

interface HistoryViewProps {
  language: "zh" | "en"
}

const translations = {
  zh: {
    historyManagement: "历史记录管理",
    searchPlaceholder: "搜索文件名或转录内容...",
    filterByLanguage: "按语言筛选",
    filterByProvider: "按服务商筛选",
    allLanguages: "所有语言",
    allProviders: "所有服务商",
    totalRecords: "总记录数",
    viewDetails: "查看详情",
    deleteRecord: "删除记录",
    copyText: "复制文本",
    confirmDelete: "确认删除",
    deleteWarning: "确定要删除这条记录吗？此操作无法撤销。",
    cancel: "取消",
    delete: "删除",
    close: "关闭",
    transcriptionDetails: "转录详情",
    fileInfo: "文件信息",
    fileName: "文件名",
    fileSize: "文件大小",
    detectedLanguage: "检测语言",
    provider: "服务商",
    wordCount: "字数",
    createdAt: "创建时间",
    transcriptionContent: "转录内容",
    noRecords: "暂无历史记录",
    noRecordsDesc: "开始转录音视频文件后，历史记录将在这里显示",
    loading: "加载中...",
    prevPage: "上一页",
    nextPage: "下一页",
    copiedToClipboard: "已复制到剪贴板",
    deleteSuccess: "删除成功",
    deleteFailed: "删除失败",
    bytes: "字节",
    kb: "KB",
    mb: "MB",
    chinese: "中文",
    english: "英文",
    loadFailed: "加载失败",
  },
  en: {
    historyManagement: "History Management",
    searchPlaceholder: "Search filename or transcription content...",
    filterByLanguage: "Filter by language",
    filterByProvider: "Filter by provider",
    allLanguages: "All languages",
    allProviders: "All providers",
    totalRecords: "Total records",
    viewDetails: "View details",
    deleteRecord: "Delete record",
    copyText: "Copy text",
    confirmDelete: "Confirm deletion",
    deleteWarning: "Are you sure you want to delete this record? This action cannot be undone.",
    cancel: "Cancel",
    delete: "Delete",
    close: "Close",
    transcriptionDetails: "Transcription Details",
    fileInfo: "File Information",
    fileName: "File name",
    fileSize: "File size",
    detectedLanguage: "Detected language",
    provider: "Provider",
    wordCount: "Word count",
    createdAt: "Created at",
    transcriptionContent: "Transcription Content",
    noRecords: "No history records",
    noRecordsDesc: "History records will appear here after you start transcribing audio/video files",
    loading: "Loading...",
    prevPage: "Previous",
    nextPage: "Next",
    copiedToClipboard: "Copied to clipboard",
    deleteSuccess: "Delete successful",
    deleteFailed: "Delete failed",
    bytes: "bytes",
    kb: "KB",
    mb: "MB",
    chinese: "Chinese",
    english: "English",
    loadFailed: "Load failed",
  },
}

export default function HistoryView({ language }: HistoryViewProps) {
  const t = translations[language]
  const toast = useToast()

  // 状态管理
  const [records, setRecords] = useState<HistoryRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [languageFilter, setLanguageFilter] = useState("all")
  const [providerFilter, setProviderFilter] = useState("all")
  const [selectedRecords, setSelectedRecords] = useState<number[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [totalRecords, setTotalRecords] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  const [selectedRecord, setSelectedRecord] = useState<HistoryRecord | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [recordToDelete, setRecordToDelete] = useState<number | null>(null)
  const [batchDeleteDialogOpen, setBatchDeleteDialogOpen] = useState(false)
  const [deleting, setDeleting] = useState(false)

  // 加载历史记录
  const loadRecords = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const params: HistorySearchParams = {
        page: currentPage,
        page_size: pageSize,
      }
      
      if (searchTerm) {
        params.search = searchTerm
      }
      if (languageFilter !== "all") {
        params.language = languageFilter
      }
      if (providerFilter !== "all") {
        params.provider = providerFilter
      }
      
      console.log('Loading history records with params:', params)
      const response = await historyApi.getHistory(params)
      console.log('History API response:', response)
      
      if (response.success && response.data) {
        setRecords(response.data || [])
        setTotalRecords(response.pagination?.total_items || 0)
        setTotalPages(response.pagination?.total_pages || 0)
      } else {
        throw new Error('获取历史记录失败')
      }
    } catch (error) {
      console.error('Failed to load history records:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      setError(errorMessage)
      toast.error(t.loadFailed, errorMessage)
      // 设置默认值防止渲染错误
      setRecords([])
      setTotalRecords(0)
      setTotalPages(0)
    } finally {
      setLoading(false)
    }
  }

  // 初始加载和依赖更新
  useEffect(() => {
    console.log('HistoryView useEffect triggered, loading records...')
    loadRecords()
  }, [currentPage, searchTerm, languageFilter, providerFilter])

  // 组件挂载时重置状态
  useEffect(() => {
    console.log('HistoryView mounted, resetting state...')
    setLoading(true)
    setError(null)
    setRecords([])
    setCurrentPage(1)
    setSearchTerm("")
    setLanguageFilter("all")
    setProviderFilter("all")
    setSelectedRecords([])
    
    // 延迟加载以确保状态重置完成
    const timer = setTimeout(() => {
      loadRecords()
    }, 100)
    
    return () => clearTimeout(timer)
  }, [])

  // 格式化文件大小
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "0 " + t.bytes
    if (bytes < 1024) return bytes + " " + t.bytes
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " " + t.kb
    return (bytes / 1024 / 1024).toFixed(1) + " " + t.mb
  }

  // 格式化日期
  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleString()
  }

  // 获取语言显示名称
  const getLanguageDisplay = (lang?: string) => {
    switch (lang) {
      case "zh": return t.chinese
      case "en": return t.english
      default: return lang || "Unknown"
    }
  }

  // 选择记录
  const handleSelectRecord = (recordId: number, checked: boolean) => {
    if (checked) {
      setSelectedRecords(prev => [...prev, recordId])
    } else {
      setSelectedRecords(prev => prev.filter(id => id !== recordId))
    }
  }

  // 删除记录
  const handleDeleteRecord = async (recordId: number) => {
    try {
      console.log('Deleting record:', recordId)
      const response = await historyApi.deleteHistory(recordId)
      console.log('Delete response:', response)
      
      if (response.success) {
        toast.success(t.deleteSuccess)
        // 从列表中移除已删除的记录
        setRecords(prev => prev.filter(record => record.id !== recordId))
        setTotalRecords(prev => prev - 1)
        setDeleteDialogOpen(false)
        setRecordToDelete(null)
      } else {
        throw new Error('删除失败')
      }
    } catch (error) {
      console.error('Failed to delete record:', error)
      const errorMessage = error instanceof Error ? error.message : '删除失败'
      toast.error(t.deleteFailed, errorMessage)
    }
  }

  // 批量删除功能
  const handleBatchDelete = async () => {
    if (selectedRecords.length === 0) return
    
    try {
      setDeleting(true)
      console.log('Batch deleting records:', selectedRecords)
      
      // 并行删除所有选中的记录
      const deletePromises = selectedRecords.map(recordId => 
        historyApi.deleteHistory(recordId)
      )
      
      const results = await Promise.all(deletePromises)
      console.log('Batch delete results:', results)
      
      // 统计成功和失败的数量
      const successCount = results.filter(result => result.success).length
      const failedCount = results.length - successCount
      
      if (successCount > 0) {
        // 从列表中移除已删除的记录
        setRecords(prev => prev.filter(record => !selectedRecords.includes(record.id)))
        setTotalRecords(prev => prev - successCount)
        setSelectedRecords([])
        
        if (failedCount === 0) {
          toast.success(`成功删除 ${successCount} 条记录`)
        } else {
          toast.warning(`成功删除 ${successCount} 条记录，${failedCount} 条删除失败`)
        }
      } else {
        toast.error(`批量删除失败，共 ${failedCount} 条记录删除失败`)
      }
      
      setBatchDeleteDialogOpen(false)
    } catch (error) {
      console.error('Batch delete failed:', error)
      toast.error('批量删除过程中发生错误')
    } finally {
      setDeleting(false)
    }
  }

  // 复制文本
  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success(t.copiedToClipboard)
  }

  // 打开详情对话框
  const openDetailsDialog = (record: HistoryRecord) => {
    setSelectedRecord(record)
    setDetailsDialogOpen(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-500">{t.loading}</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="text-lg font-medium mb-2 text-red-600 dark:text-red-400">加载失败</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={() => loadRecords()} variant="outline">
            重新加载
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">{t.historyManagement}</h2>
        <p className="text-muted-foreground">
          {t.totalRecords}: {totalRecords}
        </p>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t.searchPlaceholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <Select value={languageFilter} onValueChange={setLanguageFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t.filterByLanguage} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t.allLanguages}</SelectItem>
            <SelectItem value="zh">{t.chinese}</SelectItem>
            <SelectItem value="en">{t.english}</SelectItem>
          </SelectContent>
        </Select>

        <Select value={providerFilter} onValueChange={setProviderFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t.filterByProvider} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t.allProviders}</SelectItem>
            <SelectItem value="groq">Groq</SelectItem>
            <SelectItem value="openai">OpenAI</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 批量操作栏 */}
      {selectedRecords.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                已选择 {selectedRecords.length} 项
              </span>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedRecords([])}
                >
                  清除选择
                </Button>
                <Button 
                  variant="destructive" 
                  size="sm"
                  disabled={deleting}
                  onClick={() => {
                    setBatchDeleteDialogOpen(true)
                  }}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  {deleting ? '删除中...' : '批量删除'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 记录列表 */}
      {records.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">{t.noRecords}</h3>
            <p className="text-muted-foreground text-center">
              {t.noRecordsDesc}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {records.map((record) => (
            <Card key={record.id}>
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <Checkbox
                      checked={selectedRecords.includes(record.id)}
                      onCheckedChange={(checked) => 
                        handleSelectRecord(record.id, checked as boolean)
                      }
                    />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium truncate">{record.title}</h3>
                        <Badge variant="secondary">
                          {getLanguageDisplay(record.language)}
                        </Badge>
                        <Badge variant="outline">
                          {record.provider}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                        {record.content}
                      </p>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{formatFileSize(record.file_size)}</span>
                        <span>{record.word_count} 字</span>
                        <span>{formatDate(record.created_at)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openDetailsDialog(record)}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopyText(record.content)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setRecordToDelete(record.id)
                        setDeleteDialogOpen(true)
                      }}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            第 {currentPage} 页，共 {totalPages} 页
          </p>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              {t.prevPage}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              {t.nextPage}
              <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t.confirmDelete}</DialogTitle>
            <DialogDescription>
              {t.deleteWarning}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              {t.cancel}
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => recordToDelete && handleDeleteRecord(recordToDelete)}
            >
              {t.delete}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 批量删除确认对话框 */}
      <Dialog open={batchDeleteDialogOpen} onOpenChange={setBatchDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认批量删除</DialogTitle>
            <DialogDescription>
              确定要删除选中的所有记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setBatchDeleteDialogOpen(false)}>
              {t.cancel}
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => handleBatchDelete()}
            >
              {t.delete}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 详情对话框 */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>{t.transcriptionDetails}</DialogTitle>
          </DialogHeader>
          
          {selectedRecord && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 文件信息 */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{t.fileInfo}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium">{t.fileName}</label>
                      <p className="text-sm text-muted-foreground">{selectedRecord.title}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">{t.fileSize}</label>
                      <p className="text-sm text-muted-foreground">
                        {formatFileSize(selectedRecord.file_size)}
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">{t.detectedLanguage}</label>
                      <p className="text-sm text-muted-foreground">
                        {getLanguageDisplay(selectedRecord.language)}
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">{t.provider}</label>
                      <p className="text-sm text-muted-foreground">{selectedRecord.provider}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">{t.wordCount}</label>
                      <p className="text-sm text-muted-foreground">{selectedRecord.word_count}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">{t.createdAt}</label>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(selectedRecord.created_at)}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* 转录内容 */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{t.transcriptionContent}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-96 overflow-y-auto">
                      <p className="text-sm leading-relaxed whitespace-pre-wrap">
                        {selectedRecord.content}
                      </p>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopyText(selectedRecord.content)}
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        {t.copyText}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setDetailsDialogOpen(false)}>
              {t.close}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 