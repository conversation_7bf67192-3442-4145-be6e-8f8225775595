"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  CheckCircle2,
  AlertCircle,
  Zap,
  ArrowRight,
  Wifi,
  Shield,
  Globe,
  Settings,
  Info,
  PlayCircle,
  StopCircle
} from "lucide-react"
import { toast } from "sonner"

interface DetectionResult {
  clash_pro?: {
    tool: string
    port: number
    server: string
  }
  system_proxy?: {
    tool: string
    port: number
    server: string
  }
  recommendation: {
    use_smart_mode: boolean
    use_upstream_mode: boolean
    upstream_proxy?: string
    message: string
    instructions: string[]
  }
}

interface ProxyStatus {
  running: boolean
  port: number
  proxy_mode?: string
  upstream_proxy?: string
  smart_mode?: boolean
}

export default function SmartProxyLaunch() {
  const [detection, setDetection] = useState<DetectionResult | null>(null)
  const [proxyStatus, setProxyStatus] = useState<ProxyStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [showHelp, setShowHelp] = useState(false)

  // 检测代理配置（使用新的统一API）
  const detectProxy = async () => {
    try {
      const response = await fetch('/api/videosense/status')
      const data = await response.json()
      
      if (data.success) {
        // 转换新API响应为旧格式以兼容现有UI
        const status = data.data
        const detectedProxies = status.detected_proxies || []
        const systemProxy = status.system_proxy || {}
        
        // 查找Clash Pro
        const clashPro = detectedProxies.find((p: any) => p.name === 'clash_pro' && p.port > 0)
        
        setDetection({
          clash_pro: clashPro ? {
            tool: clashPro.name,
            port: clashPro.port,
            server: clashPro.host || '127.0.0.1'
          } : undefined,
          system_proxy: systemProxy.enabled ? {
            tool: systemProxy.type || 'unknown',
            port: systemProxy.port || 0,
            server: systemProxy.host || '127.0.0.1'
          } : undefined,
          recommendation: {
            use_smart_mode: detectedProxies.length > 0,
            use_upstream_mode: detectedProxies.length > 0,
            upstream_proxy: clashPro ? `http://${clashPro.host}:${clashPro.port}` : undefined,
            message: detectedProxies.length > 0 
              ? '🎯 检测到翻墙工具，建议使用智能代理链模式' 
              : '💡 未检测到代理冲突，可直接启动',
            instructions: detectedProxies.length > 0 
              ? [
                  '自动配置代理链：浏览器 → VideoSense → Clash Pro → 互联网',
                  '保持您的网络连接正常，同时启用视频抓取',
                  '一键启动，无需手动配置'
                ]
              : [
                  '直接启动VideoSense代理服务器',
                  '设置浏览器代理指向 127.0.0.1:8899',
                  '开始视频抓取'
                ]
          }
        })
      } else {
        toast.error('状态检测失败')
      }
    } catch (error) {
      console.error('代理检测错误:', error)
      toast.error('代理检测错误')
    }
  }

  // 获取代理状态（使用新的统一API）
  const getProxyStatus = async () => {
    try {
      const response = await fetch('/api/videosense/status')
      const data = await response.json()
      
      if (data.success) {
        const status = data.data
        setProxyStatus({
          running: status.status === 'running',
          port: status.config?.proxy_port || 8899,
          proxy_mode: status.config?.proxy_mode === 'chain' ? 'upstream' : 'direct',
          upstream_proxy: status.detected_proxies?.find((p: any) => p.port > 0)?.host,
          smart_mode: status.config?.proxy_mode === 'auto' || status.config?.proxy_mode === 'chain'
        })
      }
    } catch (error) {
      console.error('获取代理状态失败:', error)
    }
  }

  // 智能启动代理（使用新的统一API）
  const startSmartProxy = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/videosense/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          proxy_mode: 'auto',  // 自动模式：智能选择直连或代理链
          auto_start_proxy: true,
          auto_install_cert: false  // 避免弹出系统对话框
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        const proxyMode = data.data?.proxy_mode || 'direct'
        const upstreamProxy = data.data?.upstream_proxy
        
        if (proxyMode === 'chain' && upstreamProxy) {
          toast.success('🔗 智能代理链启动成功！VideoSense → Clash Pro')
        } else {
          toast.success('📡 VideoSense代理启动成功！')
        }
        
        await getProxyStatus()
      } else {
        toast.error(`启动失败: ${data.message || '未知错误'}`)
      }
    } catch (error) {
      console.error('智能启动失败:', error)
      toast.error('智能启动失败')
    } finally {
      setLoading(false)
    }
  }

  // 停止代理（使用新的统一API）
  const stopProxy = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/videosense/stop', {
        method: 'POST'
      })
      
      const data = await response.json()
      
      if (data.success) {
        toast.success('✅ VideoSense已停止，系统代理已恢复')
        await getProxyStatus()
      } else {
        toast.error(`停止失败: ${data.message || '未知错误'}`)
      }
    } catch (error) {
      console.error('停止失败:', error)
      toast.error('停止失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    detectProxy()
    getProxyStatus()
  }, [])

  const renderDetectionStatus = () => {
    if (!detection) return null

    const { clash_pro, system_proxy, recommendation } = detection

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wifi className="h-5 w-5" />
            代理环境检测
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Clash Pro 检测结果 */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span>Clash Pro 检测</span>
            </div>
            {clash_pro ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                ✅ 已检测到 (端口 {clash_pro.port})
              </Badge>
            ) : (
              <Badge variant="secondary">未检测到</Badge>
            )}
          </div>

          {/* 系统代理检测结果 */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              <span>系统代理设置</span>
            </div>
            {system_proxy ? (
              <Badge variant="default" className="bg-blue-100 text-blue-800">
                ✅ {system_proxy.tool} ({system_proxy.port})
              </Badge>
            ) : (
              <Badge variant="secondary">未启用</Badge>
            )}
          </div>

          {/* 推荐方案 */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>{recommendation.message}</strong>
              <ul className="mt-2 space-y-1">
                {recommendation.instructions.map((instruction, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm">
                    <ArrowRight className="h-3 w-3" />
                    {instruction}
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>

          {/* 代理链流程图 */}
          {recommendation.use_smart_mode && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium mb-2">智能代理链模式</h4>
              <div className="flex items-center gap-2 text-sm">
                <Badge variant="outline">浏览器</Badge>
                <ArrowRight className="h-3 w-3" />
                <Badge variant="outline">VideoSense:8899</Badge>
                <ArrowRight className="h-3 w-3" />
                <Badge variant="outline">Clash Pro:{clash_pro?.port}</Badge>
                <ArrowRight className="h-3 w-3" />
                <Badge variant="outline">互联网</Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  const renderProxyControls = () => {
    const isRunning = proxyStatus?.running

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            智能代理控制
          </CardTitle>
          <CardDescription>
            一键启动智能代理，自动处理与翻墙工具的代理冲突
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 当前状态 */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span>代理服务状态</span>
            {isRunning ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle2 className="h-3 w-3 mr-1" />
                运行中 (端口 {proxyStatus.port})
              </Badge>
            ) : (
              <Badge variant="secondary">
                <StopCircle className="h-3 w-3 mr-1" />
                已停止
              </Badge>
            )}
          </div>

          {/* 代理模式信息 */}
          {isRunning && proxyStatus?.proxy_mode && (
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>代理模式</span>
              <Badge variant={proxyStatus.proxy_mode === 'upstream' ? 'default' : 'secondary'}>
                {proxyStatus.proxy_mode === 'upstream' ? '🔗 智能代理链' : '📡 常规模式'}
              </Badge>
            </div>
          )}

          {/* 上游代理信息 */}
          {isRunning && proxyStatus?.upstream_proxy && (
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>上游代理</span>
              <code className="text-sm bg-white px-2 py-1 rounded">
                {proxyStatus.upstream_proxy}
              </code>
            </div>
          )}

          {/* 控制按钮 */}
          <div className="flex gap-3">
            {!isRunning ? (
              <Button 
                onClick={startSmartProxy} 
                disabled={loading}
                className="flex-1"
              >
                <PlayCircle className="h-4 w-4 mr-2" />
                {loading ? '启动中...' : '智能启动代理'}
              </Button>
            ) : (
              <Button 
                onClick={stopProxy} 
                disabled={loading}
                variant="outline"
                className="flex-1"
              >
                <StopCircle className="h-4 w-4 mr-2" />
                {loading ? '停止中...' : '停止代理'}
              </Button>
            )}
            
            <Button 
              onClick={() => setShowHelp(true)} 
              variant="outline"
              size="icon"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>

          {/* 使用提示 */}
          {isRunning && (
            <Alert>
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>
                <strong>代理已启动！</strong>现在您可以：
                <br />
                1. 在浏览器中播放音视频内容
                <br />
                2. VideoSense将自动检测并记录媒体资源
                <br />
                3. 您的网络连接将通过Clash Pro保持正常
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {renderDetectionStatus()}
      {renderProxyControls()}

      {/* 帮助对话框 */}
      <Dialog open={showHelp} onOpenChange={setShowHelp}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>智能代理使用说明</DialogTitle>
            <DialogDescription>
              解决VideoSense与翻墙工具的代理冲突问题
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">工作原理</h4>
              <p className="text-sm text-gray-600">
                智能代理会自动检测您的翻墙工具（如Clash Pro），并使用代理链模式避免冲突。
                您的网络请求会经过：浏览器 → VideoSense → Clash Pro → 互联网
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">使用步骤</h4>
              <ol className="list-decimal list-inside text-sm text-gray-600 space-y-1">
                <li>确保Clash Pro等翻墙工具正常运行</li>
                <li>点击"智能启动代理"按钮</li>
                <li>系统会自动检测并配置代理链</li>
                <li>在浏览器中播放音视频即可自动检测</li>
              </ol>
            </div>

            <div>
              <h4 className="font-medium mb-2">注意事项</h4>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>首次使用可能需要安装证书</li>
                <li>确保防火墙允许端口8899</li>
                <li>如有问题，请重启翻墙工具后再试</li>
              </ul>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => setShowHelp(false)}>
              了解了
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 