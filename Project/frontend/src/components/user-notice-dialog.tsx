/**
 * 用户须知对话框
 * 首次使用播放即录功能时的法律风险规避提示
 * 参考PRD设计，确保用户理解责任和风险
 */
"use client"

import React, { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  AlertTriangle, 
  Shield, 
  FileText, 
  Trash2,
  CheckCircle2 
} from "lucide-react"

interface UserNoticeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAccept: () => void
  language?: 'zh' | 'en'
}

const texts = {
  zh: {
    title: "重要提示",
    subtitle: "首次使用播放即录功能前，请仔细阅读以下内容",
    introduction: "为了帮您更好地学习和研究，VideoSense的播放即录功能会在您的电脑上对正在播放的内容进行临时处理。",
    beforeUse: "在使用前，请您理解并确认以下几点：",
    points: [
      {
        icon: Shield,
        title: "您是内容的主人",
        description: "您需要确保拥有处理这些内容的合法权利，或者您的使用行为符合您所在地区的\"合理使用\"或\"信息解析\"等法律规定。"
      },
      {
        icon: FileText,
        title: "我们是工具，您是作者",
        description: "VideoSense仅作为您的个人工具，所有操作均由您发起。您需要为您的使用行为承担全部责任。"
      },
      {
        icon: Trash2,
        title: "用后即焚，保护隐私",
        description: "处理完成后，所有临时文件都将被立即删除，我们不会保存您的任何视频内容。"
      }
    ],
    moreInfo: "更详细的信息，请查阅我们的《服务条款》。",
    agreement: "我已阅读、理解并同意以上条款，愿意承担相关责任",
    buttons: {
      cancel: "取消",
      agreeAndContinue: "同意并继续"
    },
    warning: "请仔细阅读并勾选同意条款后再继续。"
  },
  en: {
    title: "Important Notice",
    subtitle: "Before using the live capture feature for the first time, please read the following carefully",
    introduction: "To help you learn and research better, VideoSense's 'live capture' feature will temporarily process content being played on your computer.",
    beforeUse: "Before use, please understand and confirm the following:",
    points: [
      {
        icon: Shield,
        title: "You Own the Content",
        description: "You need to ensure you have legal rights to process these contents, or your usage complies with 'fair use' or 'information analysis' laws in your region."
      },
      {
        icon: FileText,
        title: "We Are Tools, You Are the Author",
        description: "VideoSense serves only as your personal tool, and all operations are initiated by you. You are fully responsible for your usage behavior."
      },
      {
        icon: Trash2,
        title: "Delete After Use, Protect Privacy",
        description: "After processing, all temporary files will be immediately deleted, and we do not save any of your video content."
      }
    ],
    moreInfo: "For more detailed information, please refer to our Terms of Service.",
    agreement: "I have read, understood and agree to the above terms, and am willing to take responsibility",
    buttons: {
      cancel: "Cancel",
      agreeAndContinue: "Agree and Continue"
    },
    warning: "Please read carefully and check the agreement before continuing."
  }
}

export default function UserNoticeDialog({ 
  open, 
  onOpenChange, 
  onAccept, 
  language = 'zh' 
}: UserNoticeDialogProps) {
  const [agreed, setAgreed] = useState(false)
  const t = texts[language]

  const handleAccept = () => {
    if (agreed) {
      onAccept()
      onOpenChange(false)
    }
  }

  const handleCancel = () => {
    setAgreed(false)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            {t.title}
          </DialogTitle>
          <DialogDescription className="text-sm">
            {t.subtitle}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* 介绍 */}
          <p className="text-sm leading-relaxed">
            {t.introduction}
          </p>

          {/* 使用前确认 */}
          <div className="space-y-3">
            <p className="font-medium text-sm">
              {t.beforeUse}
            </p>

            {/* 要点列表 */}
            <div className="space-y-4">
              {t.points.map((point, index) => {
                const IconComponent = point.icon
                return (
                  <div key={index} className="flex gap-3 p-3 bg-muted/50 rounded-lg">
                    <div className="flex-shrink-0 mt-0.5">
                      <IconComponent className="h-5 w-5 text-blue-500" />
                    </div>
                    <div className="space-y-1">
                      <h4 className="font-medium text-sm">
                        {point.title}
                      </h4>
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {point.description}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* 更多信息 */}
          <Alert>
            <FileText className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {t.moreInfo}
            </AlertDescription>
          </Alert>

          {/* 同意条款 */}
          <div className="border rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-start gap-3">
              <Checkbox
                id="agree"
                checked={agreed}
                onCheckedChange={(checked) => setAgreed(checked as boolean)}
                className="mt-0.5"
              />
              <label 
                htmlFor="agree" 
                className="text-sm font-medium leading-relaxed cursor-pointer"
              >
                {t.agreement}
              </label>
            </div>
          </div>

          {/* 提醒 */}
          {!agreed && (
            <Alert className="border-orange-200 bg-orange-50 dark:bg-orange-900/20">
              <AlertTriangle className="h-4 w-4 text-orange-500" />
              <AlertDescription className="text-xs">
                {t.warning}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            {t.buttons.cancel}
          </Button>
          <Button 
            onClick={handleAccept} 
            disabled={!agreed}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {agreed && <CheckCircle2 className="h-4 w-4 mr-2" />}
            {t.buttons.agreeAndContinue}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 