/**
 * 播放即录 - 基于代理抓包的媒体资源检测器
 * 参考 res-downloader 的工作原理：代理服务器 + 资源嗅探 + 自动下载
 * 智能检测网络流量中的音视频资源，支持 m3u8、mp4 等格式
 */
"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Play, 
  Globe,
  Settings,
  FileText,
  Zap,
  RefreshCw,
  Download,
  Trash2,
  PowerOff,
  Power,
  Wifi,
  ExternalLink,
  HelpCircle,
  CheckSquare,
  Copy,
  Info,
  CheckCircle2,
  AlertCircle
} from "lucide-react"
import UserNoticeDialog from "./user-notice-dialog"

interface SimpleLiveCaptureProps {
  language?: 'zh' | 'en'
}

interface MediaResource {
  id: string
  url: string
  title: string
  platform: string
  media_type: string
  file_size?: number
  status: string
  detected_at: string
}

interface ProxyStatus {
  running: boolean
  port: number
  status: string
  resources_count: number
  available: boolean
}

const texts = {
  zh: {
    title: "播放即录",
    description: "智能检测网络中的音视频资源，自动抓取下载链接",
    subtitle: "像 res-downloader 一样的网络抓包工具",
    
    features: {
      intelligent: {
        title: "智能检测",
        description: "自动识别网络流量中的媒体资源"
      },
      realtime: {
        title: "实时抓取", 
        description: "即时捕获 mp4、m3u8 等资源链接"
      },
      download: {
        title: "直接下载",
        description: "获取真实源地址，支持批量下载"
      }
    },

    proxy: {
      title: "代理抓包服务",
      status: "服务状态",
      running: "运行中",
      stopped: "已停止",
      unavailable: "不可用",
      startService: "启动服务",
      stopService: "停止服务",
      port: "代理端口"
    },

    setup: {
      title: "浏览器配置指南",
      description: "设置浏览器代理来抓取网络中的媒体资源",
      steps: [
        "启动代理抓包服务",
        "配置浏览器代理: 127.0.0.1:8899", 
        "访问视频网站并播放内容",
        "查看检测到的资源列表"
      ],
      chrome: "Chrome: 设置 → 高级 → 系统 → 打开代理设置",
      firefox: "Firefox: 设置 → 网络设置 → 手动代理配置",
      safari: "Safari: 偏好设置 → 高级 → 代理",
      testConnection: "测试代理连接",
      connectionWorking: "代理配置正常",
      connectionFailed: "代理配置异常",
      copyAddress: "复制代理地址"
    },

    resources: {
      title: "检测到的媒体资源",
      empty: "暂无检测到资源",
      emptyHint: "请确保代理配置正确，然后在浏览器中播放视频内容",
      refresh: "刷新列表",
      clear: "清空列表",
      download: "下载资源",
      transcribe: "转录音频",
      copyUrl: "复制链接"
    },

    buttons: {
      startCapture: "🚀 启动抓包",
      stopCapture: "⏹️停止抓包", 
      viewNotice: "查看使用须知",
      viewGuide: "配置指南"
    },

    alerts: {
      ready: "代理服务运行正常！请配置浏览器代理后开始播放视频。",
      needSetup: "需要启动代理抓包服务才能检测网络资源。",
      firstTime: "首次使用前，请阅读使用须知并确认您的权利和责任。",
      serviceUnavailable: "代理服务不可用。请检查后端服务是否正常运行。",
      foundResources: "检测到新的媒体资源！",
      downloading: "正在下载资源...",
      downloadComplete: "下载完成！",
      configGuide: "需要配置浏览器代理才能检测网络资源。点击查看详细配置指南。"
    }
  },
  
  en: {
    title: "Live Capture",
    description: "Intelligently detect media resources in network traffic and capture download links",
    subtitle: "Network packet capture tool like res-downloader",
    
    features: {
      intelligent: {
        title: "Smart Detection",
        description: "Auto identify media resources in network traffic"
      },
      realtime: {
        title: "Real-time Capture", 
        description: "Instantly capture mp4, m3u8 and other resource links"
      },
      download: {
        title: "Direct Download",
        description: "Get real source URLs with batch download support"
      }
    },

    proxy: {
      title: "Proxy Capture Service",
      status: "Service Status",
      running: "Running",
      stopped: "Stopped",
      unavailable: "Unavailable",
      startService: "Start Service",
      stopService: "Stop Service",
      port: "Proxy Port"
    },

    setup: {
      title: "Browser Configuration Guide",
      description: "Configure browser proxy to capture media resources from network",
      steps: [
        "Start proxy capture service",
        "Configure browser proxy: 127.0.0.1:8899", 
        "Visit video websites and play content",
        "Check detected resource list"
      ],
      chrome: "Chrome: Settings → Advanced → System → Open proxy settings",
      firefox: "Firefox: Settings → Network Settings → Manual proxy configuration",
      safari: "Safari: Preferences → Advanced → Proxies",
      testConnection: "Test Proxy Connection",
      connectionWorking: "Proxy configuration working",
      connectionFailed: "Proxy configuration failed",
      copyAddress: "Copy Proxy Address"
    },

    resources: {
      title: "Detected Media Resources",
      empty: "No resources detected yet",
      emptyHint: "Please ensure proxy is configured correctly, then play videos in browser",
      refresh: "Refresh List",
      clear: "Clear List",
      download: "Download Resource", 
      transcribe: "Transcribe Audio",
      copyUrl: "Copy URL"
    },

    buttons: {
      startCapture: "🚀 Start Capture",
      stopCapture: "⏹️ Stop Capture",
      viewNotice: "View Usage Notice",
      viewGuide: "Setup Guide"
    },

    alerts: {
      ready: "Proxy service running! Please configure browser proxy and start playing videos.",
      needSetup: "Need to start proxy capture service to detect network resources.",
      firstTime: "Please read usage notice and confirm your rights and responsibilities before first use.",
      serviceUnavailable: "Proxy service unavailable. Please check if backend service is running.",
      foundResources: "New media resources detected!",
      downloading: "Downloading resource...",
      downloadComplete: "Download completed!",
      configGuide: "Browser proxy configuration required to detect network resources. Click for detailed setup guide."
    }
  }
}

export default function SimpleLiveCapture({ language = 'zh' }: SimpleLiveCaptureProps) {
  const [proxyStatus, setProxyStatus] = useState<ProxyStatus>({
    running: false,
    port: 8899,
    status: 'stopped',
    resources_count: 0,
    available: true
  })
  const [resources, setResources] = useState<MediaResource[]>([])
  const [showUserNotice, setShowUserNotice] = useState(false)
  const [hasAcceptedNotice, setHasAcceptedNotice] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showSetupGuide, setShowSetupGuide] = useState(false)
  const [connectionTest, setConnectionTest] = useState<'idle' | 'testing' | 'success' | 'failed'>('idle')

  const t = texts[language]

  // API调用函数
  const apiCall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    try {
      const response = await fetch(`http://localhost:8000${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error(`API调用失败: ${endpoint}`, error)
      throw error
    }
  }, [])

  // 检查代理状态
  const checkProxyStatus = useCallback(async () => {
    try {
      const result = await apiCall('/proxy/status')
      if (result.success) {
        setProxyStatus({
          running: result.running,
          port: result.port,
          status: result.status,
          resources_count: result.resources_count,
          available: result.available !== false
        })
      }
    } catch (error) {
      console.error('获取代理状态失败:', error)
      setProxyStatus(prev => ({ 
        ...prev, 
        running: false, 
        status: 'error',
        available: false 
      }))
    }
  }, [apiCall])

  // 获取资源列表
  const fetchResources = useCallback(async () => {
    try {
      const result = await apiCall('/proxy/resources')
      if (result.success) {
        setResources(result.resources || [])
      }
    } catch (error) {
      console.error('获取资源列表失败:', error)
    }
  }, [apiCall])

  // 启动代理服务
  const startProxy = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await apiCall('/proxy/start', { method: 'POST' })
      if (result.success) {
        await checkProxyStatus()
      }
    } catch (error) {
      console.error('启动代理失败:', error)
    } finally {
      setIsLoading(false)
    }
  }, [apiCall, checkProxyStatus])

  // 停止代理服务
  const stopProxy = useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await apiCall('/proxy/stop', { method: 'POST' })
      if (result.success) {
        await checkProxyStatus()
      }
    } catch (error) {
      console.error('停止代理失败:', error)
    } finally {
      setIsLoading(false)
    }
  }, [apiCall, checkProxyStatus])

  // 检查是否接受过用户须知
  const checkUserNoticeAcceptance = () => {
    const accepted = localStorage.getItem('videosense-notice-accepted')
    setHasAcceptedNotice(accepted === 'true')
  }

  // 处理用户须知接受
  const handleNoticeAccepted = () => {
    localStorage.setItem('videosense-notice-accepted', 'true')
    setHasAcceptedNotice(true)
  }

  // 开始抓包
  const handleStartCapture = () => {
    if (!hasAcceptedNotice) {
      setShowUserNotice(true)
      return
    }

    if (!proxyStatus.running) {
      startProxy()
    }
  }

  // 测试代理连接
  const testProxyConnection = async () => {
    setConnectionTest('testing')
    try {
      const response = await fetch('/api/test-proxy-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ proxy: `127.0.0.1:${proxyStatus.port}` })
      })
      
      if (response.ok) {
        setConnectionTest('success')
      } else {
        setConnectionTest('failed')
      }
    } catch (error) {
      setConnectionTest('failed')
    }

    // 3秒后重置状态
    setTimeout(() => setConnectionTest('idle'), 3000)
  }

  // 复制代理地址
  const copyProxyAddress = () => {
    navigator.clipboard.writeText(`127.0.0.1:${proxyStatus.port}`)
  }

  // 复制资源URL
  const copyResourceUrl = (url: string) => {
    navigator.clipboard.writeText(url)
  }

  // 下载资源
  const downloadResource = async (resource: MediaResource) => {
    try {
      const result = await apiCall('/proxy/download', {
        method: 'POST',
        body: JSON.stringify({
          url: resource.url,
          title: resource.title,
          type: resource.media_type
        })
      })
      
      if (result.success) {
        console.log('下载开始:', resource.title)
      }
    } catch (error) {
      console.error('下载失败:', error)
    }
  }

  // 转录音频资源
  const transcribeResource = async (resource: MediaResource) => {
    try {
      const result = await apiCall('/transcription/start', {
        method: 'POST',
        body: JSON.stringify({
          url: resource.url,
          title: resource.title,
          language: 'auto'
        })
      })
      
      if (result.success) {
        console.log('转录开始:', resource.title)
      }
    } catch (error) {
      console.error('转录失败:', error)
    }
  }

  // 清空资源列表
  const clearResources = async () => {
    try {
      await apiCall('/proxy/clear', { method: 'POST' })
      setResources([])
    } catch (error) {
      console.error('清空列表失败:', error)
    }
  }

  // 格式化文件大小
  const formatFileSize = (size?: number) => {
    if (!size) return '-'
    if (size < 1024) return `${size} B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
    if (size < 1024 * 1024 * 1024) return `${(size / 1024 / 1024).toFixed(1)} MB`
    return `${(size / 1024 / 1024 / 1024).toFixed(1)} GB`
  }

  useEffect(() => {
    checkProxyStatus()
    fetchResources()
    checkUserNoticeAcceptance()

    // 定期刷新状态
    const interval = setInterval(() => {
      checkProxyStatus()
      if (proxyStatus.running) {
        fetchResources()
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [checkProxyStatus, fetchResources, proxyStatus.running])

  return (
    <div className="space-y-6">
      {/* 功能介绍 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5 text-blue-600" />
            {t.title}
          </CardTitle>
          <CardDescription>
            {t.description}
            <br />
            <span className="text-blue-600 font-medium">{t.subtitle}</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200">
              <Zap className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm text-blue-900 dark:text-blue-100">{t.features.intelligent.title}</h4>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">{t.features.intelligent.description}</p>
              </div>
            </div>
            <div className="flex items-start gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200">
              <Globe className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm text-green-900 dark:text-green-100">{t.features.realtime.title}</h4>
                <p className="text-xs text-green-700 dark:text-green-300 mt-1">{t.features.realtime.description}</p>
              </div>
            </div>
            <div className="flex items-start gap-3 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200">
              <Download className="h-5 w-5 text-purple-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm text-purple-900 dark:text-purple-100">{t.features.download.title}</h4>
                <p className="text-xs text-purple-700 dark:text-purple-300 mt-1">{t.features.download.description}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 代理服务控制 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-gray-600" />
            {t.proxy.title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 服务状态 */}
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-900 rounded-lg border">
            <div className="flex items-center gap-3">
              {proxyStatus.running ? (
                <Power className="h-5 w-5 text-green-500" />
              ) : (
                <PowerOff className="h-5 w-5 text-gray-500" />
              )}
              <div>
                <span className="font-medium">{t.proxy.status}:</span>
                <Badge variant={proxyStatus.running ? "default" : "secondary"} className="ml-2">
                  {proxyStatus.available === false ? t.proxy.unavailable : 
                   proxyStatus.running ? t.proxy.running : t.proxy.stopped}
                </Badge>
                {proxyStatus.running && (
                  <div className="text-sm text-muted-foreground mt-1">
                    {t.proxy.port}: {proxyStatus.port} | 资源: {resources.length} 个
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {proxyStatus.running ? (
                <Button
                  variant="outline"
                  onClick={stopProxy}
                  disabled={isLoading}
                >
                  <PowerOff className="h-4 w-4 mr-2" />
                  {t.proxy.stopService}
                </Button>
              ) : (
                <Button
                  onClick={handleStartCapture}
                  disabled={isLoading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? (
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  {t.buttons.startCapture}
                </Button>
              )}
            </div>
          </div>

          {/* 配置指南 */}
          {proxyStatus.running && (
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200">
              <div className="flex items-start gap-3">
                <HelpCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-sm mb-2">{t.setup.title}</h4>
                  <p className="text-xs text-muted-foreground mb-3">{t.setup.description}</p>
                  
                  <div className="space-y-2 mb-3">
                    {t.setup.steps.map((step, index) => (
                      <div key={index} className="flex items-center gap-2 text-xs">
                        <CheckSquare className="h-3 w-3 text-blue-500" />
                        <span>{step}</span>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center gap-2 mb-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyProxyAddress}
                      className="text-xs"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      {t.setup.copyAddress}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={testProxyConnection}
                      disabled={connectionTest === 'testing'}
                      className="text-xs"
                    >
                      {connectionTest === 'testing' ? (
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                      ) : connectionTest === 'success' ? (
                        <CheckCircle2 className="h-3 w-3 mr-1 text-green-500" />
                      ) : connectionTest === 'failed' ? (
                        <AlertCircle className="h-3 w-3 mr-1 text-red-500" />
                      ) : (
                        <Wifi className="h-3 w-3 mr-1" />
                      )}
                      {t.setup.testConnection}
                    </Button>
                  </div>

                  <div className="text-xs text-muted-foreground space-y-1">
                    <div>💡 <strong>Chrome:</strong> {t.setup.chrome}</div>
                    <div>🦊 <strong>Firefox:</strong> {t.setup.firefox}</div>
                    <div>🍎 <strong>Safari:</strong> {t.setup.safari}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 状态提示 */}
          {proxyStatus.running && resources.length === 0 && (
            <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-900/20">
              <Info className="h-4 w-4 text-blue-500" />
              <AlertDescription className="text-blue-800 dark:text-blue-200">
                {t.alerts.ready}
              </AlertDescription>
            </Alert>
          )}

          {!proxyStatus.running && hasAcceptedNotice && proxyStatus.available && (
            <Alert className="border-orange-200 bg-orange-50 dark:bg-orange-900/20">
              <AlertCircle className="h-4 w-4 text-orange-500" />
              <AlertDescription className="text-orange-800 dark:text-orange-200">
                {t.alerts.needSetup}
              </AlertDescription>
            </Alert>
          )}

          {!hasAcceptedNotice && (
            <Alert className="border-red-200 bg-red-50 dark:bg-red-900/20">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-800 dark:text-red-200">
                {t.alerts.firstTime}
              </AlertDescription>
            </Alert>
          )}

          {!proxyStatus.available && (
            <Alert className="border-red-200 bg-red-50 dark:bg-red-900/20">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-800 dark:text-red-200">
                {t.alerts.serviceUnavailable}
              </AlertDescription>
            </Alert>
          )}

          {resources.length > 0 && (
            <Alert className="border-green-200 bg-green-50 dark:bg-green-900/20">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <AlertDescription className="text-green-800 dark:text-green-200">
                {t.alerts.foundResources}
              </AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center gap-3 pt-2">
            <Button
              variant="outline"
              onClick={() => setShowUserNotice(true)}
            >
              <FileText className="h-4 w-4 mr-2" />
              {t.buttons.viewNotice}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 检测到的资源列表 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-green-600" />
              {t.resources.title}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchResources}
                disabled={!proxyStatus.running}
              >
                <RefreshCw className="h-4 w-4" />
                {t.resources.refresh}
              </Button>
              {resources.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearResources}
                >
                  <Trash2 className="h-4 w-4" />
                  {t.resources.clear}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {resources.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <Globe className="h-16 w-16 mx-auto mb-4 opacity-30" />
              <p className="text-lg font-medium mb-2">{t.resources.empty}</p>
              {proxyStatus.running && (
                <p className="text-sm">{t.resources.emptyHint}</p>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {resources.map((resource) => (
                <div 
                  key={resource.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors"
                >
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm mb-1 truncate">{resource.title}</h4>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>平台: {resource.platform}</span>
                      <span>类型: {resource.media_type}</span>
                      <span>大小: {formatFileSize(resource.file_size)}</span>
                      <span>时间: {new Date(resource.detected_at).toLocaleTimeString()}</span>
                    </div>
                    <div className="text-xs text-blue-600 mt-1 truncate">
                      {resource.url}
                    </div>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => copyResourceUrl(resource.url)}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      {t.resources.copyUrl}
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => downloadResource(resource)}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      {t.resources.download}
                    </Button>
                    <Button 
                      size="sm"
                      onClick={() => transcribeResource(resource)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <FileText className="h-4 w-4 mr-1" />
                      {t.resources.transcribe}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 用户须知对话框 */}
      <UserNoticeDialog
        open={showUserNotice}
        onOpenChange={setShowUserNotice}
        onAccept={handleNoticeAccepted}
        language={language}
      />
    </div>
  )
} 