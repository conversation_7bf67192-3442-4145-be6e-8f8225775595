"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Play, 
  Square, 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX, 
  Monitor, 
  Smartphone, 
  Globe, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Headphones,
  Radio,
  Tv,
  RefreshCw,
  Settings,
  Info,
  Loader2,
  Music,
  Video,
  Users
} from 'lucide-react'
import { toast } from 'sonner'

// 扩展窗口对象类型定义
declare global {
  interface Window {
    electronAPI?: {
      mediaCapture: {
        detectSources: () => Promise<MediaSource[]>
        requestPermissions: () => Promise<PermissionStatus>
        startCapture: (sourceId: string, options?: any) => Promise<CaptureResult>
        stopCapture: () => Promise<CaptureResult>
        getStatus: () => Promise<CaptureStatus>
        onStatusChange: (callback: (status: CaptureStatus) => void) => void
        onSourcesChange: (callback: (sources: MediaSource[]) => void) => void
        removeAllListeners: () => void
      }
    }
    mediaAPI?: {
      getUserMedia: (constraints: MediaStreamConstraints) => Promise<MediaStream>
      getDisplayMedia: (constraints: MediaStreamConstraints & { video?: boolean; audio?: boolean }) => Promise<MediaStream>
      enumerateDevices: () => Promise<MediaDeviceInfo[]>
      audioUtils: {
        createAudioContext: () => AudioContext
        createMediaRecorder: (stream: MediaStream, options?: any) => MediaRecorder
        getSupportedMimeTypes: () => string[]
      }
    }
  }
}

// 媒体源类型定义
interface MediaSource {
  id: string
  name: string
  type: 'browser' | 'music' | 'meeting' | 'screen' | 'application' | 'system'
  hasAudio: boolean
  thumbnail?: string | null
  appIcon?: string | null
  display_id: string
  status: 'detected' | 'playing' | 'capturing' | 'paused'
  url?: string
  duration?: string
  platform?: string
}

// 权限状态类型
interface PermissionStatus {
  screen: 'granted' | 'denied' | 'restricted' | 'unknown'
  microphone: 'granted' | 'denied' | 'restricted' | 'unknown'
  camera: 'granted' | 'denied' | 'restricted' | 'unknown'
}

// 捕捉结果类型
interface CaptureResult {
  success: boolean
  sourceId?: string
  sourceName?: string
  startTime?: number
  message: string
}

// 捕捉状态类型
interface CaptureStatus {
  isCapturing: boolean
  activeSources: MediaSource[]
}

// 转录状态类型
type TranscriptionStatus = 'idle' | 'detecting' | 'requesting-permission' | 'capturing' | 'processing' | 'completed' | 'error'

// 语言配置
const translations = {
  zh: {
    title: "播放即录",
    description: "检测系统正在播放的媒体内容，支持多格式转录",
    detectingSources: "正在检测媒体源...",
    noSourcesFound: "未检测到活跃的媒体源",
    refreshSources: "刷新检测",
    selectedSources: "已选择的媒体源",
    startRecording: "开始转录",
    stopRecording: "停止转录",
    recording: "转录中",
    processing: "处理中",
    authorizationRequired: "需要授权",
    authorizationDescription: "首次使用需要获得媒体访问权限",
    requestPermission: "请求权限",
    permissionGranted: "权限已授予",
    permissionDenied: "权限被拒绝",
    microphonePermission: "麦克风权限",
    screenPermission: "屏幕录制权限",
    cameraPermission: "摄像头权限",
    systemAudio: "系统音频",
    browserAudio: "浏览器音频", 
    applicationAudio: "应用音频",
    meetingAudio: "会议音频",
    musicAudio: "音乐播放器",
    screenCapture: "屏幕捕捉",
    permissionWarning: "部分功能需要相应权限才能正常使用",
    captureStarted: "媒体捕捉已开始",
    captureStopped: "媒体捕捉已停止",
    captureError: "捕捉过程出现错误",
    detectError: "检测媒体源时出现错误",
    noElectronAPI: "当前环境不支持媒体检测功能",
    platformSupport: "平台支持",
    formatSupport: "格式支持",
    commonFormats: "常见格式: MP4, MP3, M3U8, WAV, MOV等",
    streamingSupport: "流媒体: YouTube, Bilibili, Spotify等平台",
    settings: "设置",
    about: "关于"
  },
  en: {
    title: "Live Capture",
    description: "Detect media content being played on system, support multi-format transcription",
    detectingSources: "Detecting media sources...",
    noSourcesFound: "No active media sources detected",
    refreshSources: "Refresh Detection",
    selectedSources: "Selected Media Sources",
    startRecording: "Start Transcription",
    stopRecording: "Stop Transcription", 
    recording: "Recording",
    processing: "Processing",
    authorizationRequired: "Authorization Required",
    authorizationDescription: "First-time use requires media access permissions",
    requestPermission: "Request Permission",
    permissionGranted: "Permission Granted",
    permissionDenied: "Permission Denied",
    microphonePermission: "Microphone Permission",
    screenPermission: "Screen Recording Permission",
    cameraPermission: "Camera Permission",
    systemAudio: "System Audio",
    browserAudio: "Browser Audio",
    applicationAudio: "Application Audio", 
    meetingAudio: "Meeting Audio",
    musicAudio: "Music Player",
    screenCapture: "Screen Capture",
    permissionWarning: "Some features require corresponding permissions to work properly",
    captureStarted: "Media capture started",
    captureStopped: "Media capture stopped",
    captureError: "Error occurred during capture",
    detectError: "Error detecting media sources",
    noElectronAPI: "Media detection not supported in current environment",
    platformSupport: "Platform Support",
    formatSupport: "Format Support", 
    commonFormats: "Common formats: MP4, MP3, M3U8, WAV, MOV, etc.",
    streamingSupport: "Streaming: YouTube, Bilibili, Spotify, etc.",
    settings: "Settings",
    about: "About"
  }
}

export default function LiveCapture({ language = 'zh' }: { language?: 'zh' | 'en' }) {
  const t = translations[language]
  
  // 状态管理
  const [mediaSourcesList, setMediaSourcesList] = useState<MediaSource[]>([])
  const [selectedSources, setSelectedSources] = useState<string[]>([])
  const [transcriptionStatus, setTranscriptionStatus] = useState<TranscriptionStatus>('idle')
  const [permissions, setPermissions] = useState<PermissionStatus>({
    screen: 'unknown',
    microphone: 'unknown', 
    camera: 'unknown'
  })
  const [isDetecting, setIsDetecting] = useState(false)
  const [captureProgress, setCaptureProgress] = useState(0)
  const [permissionDialog, setPermissionDialog] = useState(false)
  const [captureStartTime, setCaptureStartTime] = useState<number | null>(null)

  // Refs
  const captureTimerRef = useRef<NodeJS.Timeout | null>(null)

  // 检查Electron API可用性
  const isElectronAvailable = typeof window !== 'undefined' && window.electronAPI?.mediaCapture

  // 获取媒体源图标
  const getSourceIcon = (source: MediaSource) => {
    switch (source.type) {
      case 'browser':
        return <Globe className="w-4 h-4" />
      case 'music':
        return <Music className="w-4 h-4" />
      case 'meeting':
        return <Users className="w-4 h-4" />
      case 'screen':
        return <Monitor className="w-4 h-4" />
      case 'application':
        return <Video className="w-4 h-4" />
      default:
        return <Headphones className="w-4 h-4" />
    }
  }

  // 获取媒体源状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'playing':
        return 'bg-green-500'
      case 'capturing':
        return 'bg-blue-500'
      case 'paused':
        return 'bg-yellow-500'
      default:
        return 'bg-gray-500'
    }
  }

  // 获取权限状态图标
  const getPermissionIcon = (status: string) => {
    switch (status) {
      case 'granted':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'denied':
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  // 检测媒体源
  const detectMediaSources = useCallback(async () => {
    setIsDetecting(true)
    try {
      if (window.electronAPI?.mediaCapture) {
        // 使用Electron API检测真实媒体源
        const sources = await window.electronAPI.mediaCapture.detectSources()
        setMediaSourcesList(sources)
        
        if (sources.length === 0) {
          toast.info(t.noSourcesFound)
        } else {
          toast.success(`检测到 ${sources.length} 个媒体源`)
        }
      } else {
        // 浏览器环境提示
        toast.warning(t.noElectronAPI)
        setMediaSourcesList([])
      }
    } catch (error) {
      console.error('检测媒体源失败:', error)
      toast.error(t.detectError)
      setMediaSourcesList([])
    } finally {
      setIsDetecting(false)
    }
  }, [t])

  // 请求权限
  const requestPermissions = useCallback(async () => {
    if (!window.electronAPI?.mediaCapture) {
      toast.warning(t.noElectronAPI)
      return
    }

    try {
      setTranscriptionStatus('requesting-permission')
      toast.info(language === 'zh' ? '正在请求权限...' : 'Requesting permissions...')
      
      const permissionResult = await window.electronAPI.mediaCapture.requestPermissions()
      console.log('Permission result:', permissionResult)
      setPermissions(permissionResult)
      
      // 检查关键权限（屏幕录制或麦克风）
      const hasScreenPermission = permissionResult.screen === 'granted'
      const hasMicrophonePermission = permissionResult.microphone === 'granted'
      const hasEssentialPermissions = hasScreenPermission || hasMicrophonePermission
      
      if (hasEssentialPermissions) {
        toast.success(language === 'zh' ? '权限已获得！可以开始检测媒体源' : 'Permissions granted! Ready to detect media sources')
        setPermissionDialog(false)
        // 权限获取后自动检测媒体源
        await detectMediaSources()
      } else {
        // 显示具体的权限状态
        const pendingPermissions = Object.entries(permissionResult)
          .filter(([key, status]) => status !== 'granted')
          .map(([key, status]) => `${key}: ${status}`)
        
        toast.warning(t.permissionDenied + '\n详细状态: ' + pendingPermissions.join(', '))
        
        // 提供手动设置的建议
        toast.info(language === 'zh' ? 
          '请在"系统偏好设置 > 安全性与隐私 > 隐私"中手动授予权限，然后重新检测' : 
          'Please grant permissions manually in System Preferences > Security & Privacy > Privacy, then retry detection'
        )
      }
    } catch (error) {
      console.error('请求权限失败:', error)
      toast.error('权限请求失败: ' + (error as Error).message)
    } finally {
      setTranscriptionStatus('idle')
    }
  }, [t, detectMediaSources, language])

  // 开始捕捉
  const startCapture = useCallback(async () => {
    if (!isElectronAvailable || selectedSources.length === 0) {
      return
    }

    setTranscriptionStatus('capturing')
    setCaptureStartTime(Date.now())

    try {
      // 开始捕捉第一个选中的源（后续可扩展为多源同时捕捉）
      const firstSourceId = selectedSources[0]
      const result = await window.electronAPI!.mediaCapture.startCapture(firstSourceId)
      
      if (result.success) {
        toast.success(t.captureStarted)
        
        // 开始进度计时
        captureTimerRef.current = setInterval(() => {
          setCaptureProgress(prev => Math.min(prev + 1, 100))
        }, 1000)
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('Capture start error:', error)
      toast.error(t.captureError + ': ' + (error as Error).message)
      setTranscriptionStatus('idle')
      setCaptureStartTime(null)
    }
  }, [isElectronAvailable, selectedSources, t])

  // 停止捕捉
  const stopCapture = useCallback(async () => {
    if (!isElectronAvailable) {
      return
    }

    try {
      const result = await window.electronAPI!.mediaCapture.stopCapture()
      
      if (result.success) {
        toast.success(t.captureStopped)
      }
    } catch (error) {
      console.error('Capture stop error:', error)
      toast.error(t.captureError + ': ' + (error as Error).message)
    } finally {
      setTranscriptionStatus('idle')
      setCaptureProgress(0)
      setCaptureStartTime(null)
      
      if (captureTimerRef.current) {
        clearInterval(captureTimerRef.current)
        captureTimerRef.current = null
      }
    }
  }, [isElectronAvailable, t])

  // 组件挂载时检查权限并检测媒体源
  useEffect(() => {
    const initializeCapture = async () => {
      if (window.electronAPI?.mediaCapture) {
        try {
          // 先尝试检测媒体源，如果失败再显示权限对话框
          await detectMediaSources()
          // 如果检测成功但没有媒体源，仍然显示权限状态
          if (mediaSourcesList.length === 0) {
            setPermissionDialog(true)
          }
        } catch (error) {
          console.error('初始化失败:', error)
          setPermissionDialog(true)
        }
      }
    }

    initializeCapture()
    
    // 清理函数
    return () => {
      if (window.electronAPI?.mediaCapture?.removeAllListeners) {
        window.electronAPI.mediaCapture.removeAllListeners()
      }
    }
  }, [detectMediaSources, mediaSourcesList.length])

  // 处理源选择
  const handleSourceSelection = (sourceId: string, checked: boolean) => {
    setSelectedSources(prev => 
      checked 
        ? [...prev, sourceId]
        : prev.filter(id => id !== sourceId)
    )
  }

  // 渲染媒体源卡片
  const renderMediaSourceCard = (source: MediaSource) => (
    <Card key={source.id} className={`transition-all duration-200 ${
      selectedSources.includes(source.id) ? 'ring-2 ring-blue-500' : ''
    }`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <Checkbox
            checked={selectedSources.includes(source.id)}
            onCheckedChange={(checked) => handleSourceSelection(source.id, !!checked)}
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              {getSourceIcon(source)}
              <span className="font-medium truncate">{source.name}</span>
              <div className={`w-2 h-2 rounded-full ${getStatusColor(source.status)}`} />
            </div>
            
            <div className="text-sm text-gray-500 space-y-1">
              <div>类型: {t[source.type as keyof typeof t] || source.type}</div>
              {source.duration && <div>时长: {source.duration}</div>}
              {source.platform && <div>平台: {source.platform}</div>}
            </div>
          </div>
          
          {source.thumbnail && (
            <img 
              src={source.thumbnail} 
              alt="预览" 
              className="w-12 h-12 rounded object-cover"
            />
          )}
        </div>
      </CardContent>
    </Card>
  )

  if (!isElectronAvailable) {
    return (
      <div className="space-y-6">
        <Alert>
          <Info className="w-4 h-4" />
          <AlertDescription>
            {t.noElectronAPI}。此功能需要在VideoSense桌面应用中使用。
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 标题和描述 */}
      <div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t.title}</h2>
        <p className="text-gray-600">{t.description}</p>
      </div>

      {/* 权限状态卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            权限状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              {getPermissionIcon(permissions.screen)}
              <span className="text-sm">{t.screenPermission}</span>
            </div>
            <div className="flex items-center gap-2">
              {getPermissionIcon(permissions.microphone)}
              <span className="text-sm">{t.microphonePermission}</span>
            </div>
            <div className="flex items-center gap-2">
              {getPermissionIcon(permissions.camera)}
              <span className="text-sm">{t.cameraPermission}</span>
            </div>
          </div>
          
          {Object.values(permissions).some(status => status !== 'granted') && (
            <div className="mt-4">
              <Button variant="outline" onClick={() => setPermissionDialog(true)}>
                {t.requestPermission}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 媒体源检测卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Play className="w-5 h-5" />
              检测到的媒体源 ({mediaSourcesList.length})
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={detectMediaSources}
              disabled={isDetecting}
            >
              {isDetecting ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              {t.refreshSources}
            </Button>
          </CardTitle>
          <CardDescription>
            选择要转录的媒体源，支持多种格式和平台
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isDetecting ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin mr-2" />
              <span>{t.detectingSources}</span>
            </div>
          ) : mediaSourcesList.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Monitor className="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p>{t.noSourcesFound}</p>
              <p className="text-sm mt-1">请开始播放媒体内容后重新检测</p>
            </div>
          ) : (
            <div className="space-y-3">
              {mediaSourcesList.map(renderMediaSourceCard)}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 控制面板 */}
      {selectedSources.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">
                  已选择 {selectedSources.length} 个媒体源
                </p>
                <p className="text-sm text-gray-500">
                  准备开始转录选中的媒体内容
                </p>
              </div>
              
              <div className="flex gap-2">
                {transcriptionStatus === 'capturing' ? (
                  <Button onClick={stopCapture} variant="destructive">
                    <Square className="w-4 h-4 mr-2" />
                    {t.stopRecording}
                  </Button>
                ) : (
                  <Button 
                    onClick={startCapture}
                    disabled={transcriptionStatus !== 'idle'}
                  >
                    <Play className="w-4 h-4 mr-2" />
                    {t.startRecording}
                  </Button>
                )}
              </div>
            </div>
            
            {/* 捕捉进度 */}
            {transcriptionStatus === 'capturing' && (
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t.recording}</span>
                  <span>{captureStartTime ? Math.floor((Date.now() - captureStartTime) / 1000) : 0}s</span>
                </div>
                <Progress value={captureProgress} className="h-2" />
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 格式支持信息 */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-blue-900 mb-2">{t.formatSupport}</h4>
              <p className="text-sm text-blue-700">{t.commonFormats}</p>
            </div>
            <div>
              <h4 className="font-medium text-blue-900 mb-2">{t.platformSupport}</h4>
              <p className="text-sm text-blue-700">{t.streamingSupport}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 权限请求对话框 */}
      <Dialog open={permissionDialog} onOpenChange={setPermissionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t.authorizationRequired}</DialogTitle>
            <DialogDescription>
              {t.authorizationDescription}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <Alert>
              <AlertTriangle className="w-4 h-4" />
              <AlertDescription>
                为了检测和捕捉系统中的媒体内容，VideoSense需要获得屏幕录制和音频访问权限。所有处理均在本地进行，不会上传任何媒体内容。
              </AlertDescription>
            </Alert>
            
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setPermissionDialog(false)}>
                取消
              </Button>
              <Button onClick={requestPermissions}>
                {t.requestPermission}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}