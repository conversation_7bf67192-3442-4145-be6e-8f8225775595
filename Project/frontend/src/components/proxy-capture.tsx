"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Play,
  Download,
  Globe,
  RefreshCw,
  Trash2,
  HelpCircle,
  CheckCircle2,
  AlertCircle,
  Power,
  PowerOff,
  ExternalLink,
  Clock,
  Zap,
  FileVideo,
  Square,
  RotateCcw,
  Settings,
  Info,
  ChevronDown,
  Loader2,
  Wifi,
  Shield,
  AlertTriangle,
  CheckCircle,
  FileText,
  Copy,
} from "lucide-react"
import { toast } from "sonner"

// 语言配置
const translations = {
  zh: {
    title: "智能抓包",
    description: "通过网络代理自动检测和捕获音视频资源",
    startProxy: "启动代理",
    stopProxy: "停止代理",
    proxyStatus: "代理状态",
    running: "运行中",
    stopped: "已停止",
    starting: "启动中",
    port: "端口",
    detectedResources: "检测到的资源",
    noResources: "暂无检测到的资源",
    refreshResources: "刷新资源",
    clearResources: "清空列表",
    downloadResource: "下载资源",
    transcribeResource: "转录资源",
    downloadAndTranscribe: "下载并转录",
    platform: "平台",
    mediaType: "类型",
    fileSize: "大小",
    detectedAt: "检测时间",
    status: "状态",
    detected: "已检测",
    downloading: "下载中",
    completed: "已完成",
    failed: "失败",
    helpTitle: "使用说明",
    helpContent: `代理抓包使用步骤：

1. 点击"启动代理"按钮启动代理服务
2. 将系统网络代理设置为 127.0.0.1:8899
3. 在浏览器中打开并播放音视频内容
4. 返回VideoSense查看自动检测到的资源
5. 选择需要的资源进行下载和转录

支持的平台：
• YouTube、Bilibili、抖音、快手、小红书
• Spotify、QQ音乐、网易云音乐、酷狗音乐
• Twitch、Netflix等

注意事项：
• 如果检测不到资源，请检查代理设置
• 确保播放的内容包含音频轨道
• 某些平台可能需要登录才能检测到资源`,
    proxyStarted: "代理服务已启动",
    proxyStopped: "代理服务已停止",
    proxyStartFailed: "代理服务启动失败",
    proxyStopFailed: "代理服务停止失败",
    resourceDownloadStarted: "开始下载资源",
    resourceTranscribeStarted: "开始转录资源",
    systemProxyHint: "请将系统代理设置为",
    autoProxySetup: "系统将尝试自动配置代理",
    manualProxySetup: "如果自动配置失败，请手动设置",
    proxyInstructions: "代理设置说明",
    macOSProxy: "macOS: 系统偏好设置 → 网络 → 高级 → 代理",
    windowsProxy: "Windows: 设置 → 网络和Internet → 代理",
    browserUsage: "设置代理后，在浏览器中播放音视频即可自动检测",
  },
  en: {
    title: "Smart Capture",
    description: "Automatically detect and capture audio/video resources through network proxy",
    startProxy: "Start Proxy",
    stopProxy: "Stop Proxy",
    proxyStatus: "Proxy Status",
    running: "Running",
    stopped: "Stopped",
    starting: "Starting",
    port: "Port",
    detectedResources: "Detected Resources",
    noResources: "No resources detected",
    refreshResources: "Refresh Resources",
    clearResources: "Clear List",
    downloadResource: "Download",
    transcribeResource: "Transcribe",
    downloadAndTranscribe: "Download & Transcribe",
    platform: "Platform",
    mediaType: "Type",
    fileSize: "Size",
    detectedAt: "Detected",
    status: "Status",
    detected: "Detected",
    downloading: "Downloading",
    completed: "Completed",
    failed: "Failed",
    helpTitle: "Usage Instructions",
    helpContent: `Proxy capture usage steps:

1. Click "Start Proxy" to start the proxy service
2. Set system network proxy to 127.0.0.1:8899
3. Open and play audio/video content in browser
4. Return to VideoSense to view auto-detected resources
5. Select resources for download and transcription

Supported platforms:
• YouTube, Bilibili, TikTok, Kuaishou, Xiaohongshu
• Spotify, QQ Music, NetEase Music, Kugou Music
• Twitch, Netflix, etc.

Notes:
• If no resources detected, check proxy settings
• Ensure content contains audio tracks
• Some platforms may require login to detect resources`,
    proxyStarted: "Proxy service started",
    proxyStopped: "Proxy service stopped",
    proxyStartFailed: "Failed to start proxy service",
    proxyStopFailed: "Failed to stop proxy service",
    resourceDownloadStarted: "Started downloading resource",
    resourceTranscribeStarted: "Started transcribing resource",
    systemProxyHint: "Please set system proxy to",
    autoProxySetup: "System will try to auto-configure proxy",
    manualProxySetup: "If auto-config fails, please set manually",
    proxyInstructions: "Proxy Setup Instructions",
    macOSProxy: "macOS: System Preferences → Network → Advanced → Proxies",
    windowsProxy: "Windows: Settings → Network & Internet → Proxy",
    browserUsage: "After setting proxy, play audio/video in browser to auto-detect",
  },
}

interface MediaResource {
  id: string
  url: string
  title: string
  platform: string
  media_type: string
  file_size?: number
  duration?: string
  quality?: string
  format?: string
  status: string
  detected_at: string
  content_type?: string
  description?: string
  referer?: string
}

interface ProxyStatus {
  running: boolean
  port: number
  status: string
  resources_count: number
  available: boolean
  proxy_mode?: string
  proxy_mode_info?: string
  upstream_proxy?: string
  smart_mode?: boolean
}

export default function ProxyCapture({ language = 'zh' }: { language?: 'zh' | 'en' }) {
  const t = translations[language]
  
  // 状态管理
  const [proxyStatus, setProxyStatus] = useState<ProxyStatus>({
    running: false,
    port: 8899,
    status: 'stopped',
    resources_count: 0,
    available: true
  })
  const [resources, setResources] = useState<MediaResource[]>([])
  const [selectedResources, setSelectedResources] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(false)
  const [showHelp, setShowHelp] = useState(false)
  const [showProxyInstructions, setShowProxyInstructions] = useState(false)
  const [permissionStatus, setPermissionStatus] = useState({
    screen: 'unknown',
    microphone: 'unknown'
  })
  const [isCapturing, setIsCapturing] = useState(false)
  const [backendStatus, setBackendStatus] = useState<'connecting' | 'connected' | 'error'>('connecting')
  const [error, setError] = useState<string | null>(null)
  
  // 权限管理状态
  const [systemPermission, setSystemPermission] = useState({
    granted: false,
    checked: false,
    canAutoManage: false
  })
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const [authDialogContent, setAuthDialogContent] = useState({
    title: '',
    message: '',
    description: '',
    onConfirm: null as (() => void) | null
  })

  // API 调用函数
  const apiCall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    try {
      console.log(`[API] 调用: ${endpoint}`, options)
      
      // 创建AbortController来实现超时
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 增加到15秒超时
      
      const response = await fetch(`http://localhost:8000${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: controller.signal,
        ...options,
      })
      
      clearTimeout(timeoutId)
      console.log(`[API] 响应状态: ${response.status}`)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error(`[API] 错误响应: ${response.status} - ${errorText}`)
        throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`)
      }
      
      const result = await response.json()
      console.log(`[API] 响应数据:`, result)
      return result
    } catch (error) {
      console.error(`[API] 调用失败: ${endpoint}`, error)
      
      // 更详细的错误信息
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      if (error instanceof TypeError && (errorMessage.includes('fetch') || errorMessage.includes('Failed to fetch'))) {
        const detailedError = `🔌 无法连接到后端服务 (${endpoint})。请确保后端服务正在运行在 http://localhost:8000`
        console.error('[连接错误]', detailedError)
        toast.error(detailedError, {
          description: '检查后端服务是否启动，或尝试重新启动应用',
          duration: 8000
        })
        throw new Error('后端服务连接失败')
      } else if (errorMessage.includes('aborted') || errorMessage.includes('timeout')) {
        const timeoutError = `⏱️ 请求超时 (${endpoint})。服务可能正忙，请稍后重试`
        console.error('[超时错误]', timeoutError)
        toast.error(timeoutError, {
          duration: 6000
        })
        throw new Error('请求超时')
      } else if (errorMessage.includes('NetworkError')) {
        const networkError = `🌐 网络错误 (${endpoint})。请检查网络连接`
        console.error('[网络错误]', networkError)
        toast.error(networkError, {
          duration: 6000
        })
        throw new Error('网络连接失败')
      }
      
      // 显示原始错误
      const genericError = `❌ 请求失败: ${errorMessage}`
      console.error('[通用错误]', genericError)
      toast.error(genericError, {
        description: `端点: ${endpoint}`,
        duration: 8000
      })
      
      throw error
    }
  }, [])

  // 获取代理状态
  const checkProxyStatus = useCallback(async () => {
    try {
      const result = await apiCall('/proxy/status')
      if (result.success) {
        setProxyStatus({
          running: result.running,
          port: result.port,
          status: result.status,
          resources_count: result.resources_count,
          available: result.available !== false // 如果没有available字段，默认为true
        })
        console.log('[代理状态]', result)
      } else {
        console.warn('[代理状态] API返回失败:', result)
        toast.warning(`代理状态检查失败: ${result.error || '未知错误'}`)
      }
    } catch (error) {
      console.error('获取代理状态失败:', error)
      // 连接失败时设置为不可用
      setProxyStatus(prev => ({ 
        ...prev, 
        running: false, 
        status: 'error',
        available: false 
      }))
      
      // 不在这里显示toast，避免过多提示
    }
  }, [apiCall])

  // 获取检测到的资源
  const fetchResources = useCallback(async () => {
    try {
      console.log('[资源获取] 开始获取资源列表...')
      const result = await apiCall('/proxy/resources')
      console.log('[资源获取] 结果:', result)
      
      if (result.success) {
        setResources(result.resources || [])
        setProxyStatus(prev => ({ ...prev, resources_count: result.total || result.count || 0 }))
        
        if (result.resources?.length > 0) {
          toast.success(`🎯 发现 ${result.resources.length} 个资源`)
        }
      } else {
        console.warn('[资源获取] API返回失败:', result)
        toast.warning(`获取资源失败: ${result.message || '未知错误'}`)
      }
    } catch (error) {
      console.error('获取资源列表失败:', error)
      // 只在明确的错误情况下显示toast
      const errorMessage = error instanceof Error ? error.message : String(error)
      if (!errorMessage.includes('后端服务连接失败')) {
        toast.error('获取资源列表失败')
      }
    }
  }, [apiCall])

  // 检查后端连接状态
  const checkBackendStatus = useCallback(async () => {
    try {
      console.log('🔍 检查后端连接状态...')
      const response = await fetch('http://localhost:8000/health', { 
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5秒超时
      })
      
      if (response.ok) {
        console.log('✅ 后端连接正常')
        setBackendStatus('connected')
        setError(null)
        return true
      } else {
        throw new Error(`后端响应错误: ${response.status}`)
      }
    } catch (error: any) {
      console.error('❌ 后端连接失败:', error)
      setBackendStatus('error')
      
      if (error.name === 'AbortError') {
        setError('后端连接超时，请检查后端服务是否启动')
      } else if (error.code === 'ECONNREFUSED') {
        setError('无法连接到后端服务，请确保后端已启动在端口8000')
      } else {
        setError(`后端连接异常: ${error.message}`)
      }
      
      toast.error("后端连接失败", {
        description: error.message || "请检查后端服务状态",
      })
      return false
    }
  }, [])

  // 检查系统权限状态
  const checkSystemPermission = useCallback(async () => {
    try {
      // 检查是否在Electron环境中
      if (typeof window !== 'undefined' && window.permissionAPI) {
        const permissionState = await window.permissionAPI.getPermissionState()
        const proxyStatus = await window.permissionAPI.checkProxyStatus()
        
        setSystemPermission({
          granted: permissionState.permissionGranted,
          checked: true,
          canAutoManage: proxyStatus.canAutoManage
        })
        
        console.log('🔐 系统权限状态:', {
          granted: permissionState.permissionGranted,
          canAutoManage: proxyStatus.canAutoManage
        })
        
        return permissionState.permissionGranted
      } else {
        // 非Electron环境，跳过权限检查
        setSystemPermission({
          granted: false,
          checked: true,
          canAutoManage: false
        })
        return false
      }
    } catch (error) {
      console.error('检查系统权限失败:', error)
      setSystemPermission({
        granted: false,
        checked: true,
        canAutoManage: false
      })
      return false
    }
  }, [])

  // 请求系统授权
  const requestSystemAuthorization = useCallback(async () => {
    try {
      if (typeof window !== 'undefined' && window.permissionAPI) {
        setIsLoading(true)
        
        const result = await window.permissionAPI.requestAuthorization()
        
        if (result.success) {
          setSystemPermission(prev => ({
            ...prev,
            granted: true,
            canAutoManage: true
          }))
          
          toast.success(result.message || '🎉 授权成功！', {
            description: '现在可以自动管理系统代理设置了',
            duration: 5000
          })
          
          return true
        } else if (result.cancelled) {
          setAuthDialogContent({
            title: '手动设置代理',
            message: '您选择了手动设置，我们将提供详细的设置指导。',
            description: '虽然需要手动操作，但VideoSense会尽力简化这个过程。',
            onConfirm: () => {
              setShowAuthDialog(false)
              setShowProxyInstructions(true)
            }
          })
          setShowAuthDialog(true)
          return false
        } else {
          toast.warning(result.message || '授权被取消', {
            description: '将使用手动代理设置方式',
            duration: 4000
          })
          return false
        }
      }
      return false
    } catch (error) {
      console.error('请求系统授权失败:', error)
      toast.error('授权过程中出现错误', {
        description: '将使用手动代理设置方式',
        duration: 4000
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 组件加载时检查后端状态和权限
  useEffect(() => {
    const initializeComponent = async () => {
      await checkBackendStatus()
      await checkSystemPermission()
    }
    
    initializeComponent()
    
    // 设置定期检查（30秒一次）
    const interval = setInterval(checkBackendStatus, 30000)
    return () => clearInterval(interval)
  }, [checkBackendStatus, checkSystemPermission])

  // 启动代理服务（集成权限管理）
  const startProxy = useCallback(async () => {
    try {
      setIsLoading(true)
      console.log('[代理启动] 开始启动代理服务...')
      console.log('[代理启动] 当前状态:', proxyStatus)
      console.log('[代理启动] 权限状态:', systemPermission)
      
      // 先检查后端连接
      const backendOk = await checkBackendStatus()
      if (!backendOk) {
        throw new Error('后端服务不可用，无法启动抓包')
      }
      
      // 检查系统权限状态
      const hasPermission = systemPermission.granted
      
      // 如果没有权限且在Electron环境中，提示用户授权
      if (!hasPermission && typeof window !== 'undefined' && window.permissionAPI) {
        setAuthDialogContent({
          title: '🚀 一键抓包需要权限',
          message: '为了提供最佳的用户体验，VideoSense希望获得一次性权限来自动管理系统代理设置。',
          description: '授权后您就可以享受"一键开启，自动停止"的无感抓包体验，就像专业的网络分析工具一样简单。',
          onConfirm: async () => {
            setShowAuthDialog(false)
            const authResult = await requestSystemAuthorization()
            if (authResult) {
              // 授权成功，继续启动流程
              await startProxyWithPermission(true)
            } else {
              // 授权失败或取消，使用手动模式
              await startProxyWithPermission(false)
            }
          }
        })
        setShowAuthDialog(true)
        return
      }
      
      // 直接启动（已有权限或非Electron环境）
      await startProxyWithPermission(hasPermission)
      
    } catch (error) {
      console.error('[代理启动] 启动流程异常:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      toast.error(`❌ 启动准备失败: ${errorMessage}`)
      setIsLoading(false)
    }
  }, [proxyStatus, systemPermission, checkBackendStatus, requestSystemAuthorization])

  // 带权限控制的代理启动逻辑
  const startProxyWithPermission = useCallback(async (autoManageProxy: boolean) => {
    try {
      console.log(`[代理启动] 启动代理服务，自动管理代理: ${autoManageProxy}`)
      
      // 首先检测现有代理
      let useSmartMode = true
      try {
        const detectionResult = await apiCall('/proxy/system/detect-existing-proxy')
        if (detectionResult.success && detectionResult.existing_proxy_found) {
          const proxyTool = detectionResult.proxy_info.tool
          console.log(`[智能检测] 发现现有代理: ${proxyTool}`)
          toast.info(`🔍 检测到现有代理: ${proxyTool}`, {
            description: '将使用智能模式避免冲突',
            duration: 4000
          })
        } else {
          console.log('[智能检测] 未发现现有代理，使用常规模式')
        }
      } catch (detectError) {
        console.warn('[智能检测] 代理检测失败，使用常规模式:', detectError)
        useSmartMode = false
      }
      
      // 启动后端代理服务（使用智能模式）
      const result = await apiCall('/proxy/start', {
        method: 'POST',
        body: JSON.stringify({ 
          auto_setup_proxy: false, // 由前端控制代理设置
          smart_mode: useSmartMode // 启用智能模式
        })
      })
      
      if (result && result.success) {
        console.log('[代理启动] 后端代理服务启动成功')
        setProxyStatus(prev => ({ 
          ...prev, 
          running: true, 
          status: 'running',
          available: true,
          proxy_mode: result.proxy_mode,
          proxy_mode_info: result.proxy_mode_info,
          upstream_proxy: result.upstream_proxy,
          smart_mode: result.smart_mode
        }))
        
        // 显示代理模式信息
        if (result.proxy_mode === 'upstream') {
          toast.success('🔗 智能代理链已启动！', {
            description: result.proxy_mode_info || '已通过代理链模式启动，与现有代理和谐共存',
            duration: 6000
          })
        } else {
          console.log('[代理启动] 使用常规代理模式')
        }
        
        // 如果有权限，尝试自动设置系统代理
        if (autoManageProxy && typeof window !== 'undefined' && window.permissionAPI) {
          try {
            const proxyResult = await window.permissionAPI.setSystemProxy(true, '127.0.0.1', 8899)
            
            if (proxyResult.success) {
              toast.success('🎉 抓包已启动！', {
                description: `✅ 系统代理已自动设置为 127.0.0.1:8899，现在可以开始浏览网页进行抓包了`,
                duration: 5000
              })
            } else if (proxyResult.requiresAuthorization) {
              // 权限不足，提示重新授权
              toast.warning('需要重新授权', {
                description: '系统代理设置需要权限，请重新授权或手动设置',
                duration: 6000
              })
              setShowProxyInstructions(true)
            } else {
              throw new Error(proxyResult.error || '自动设置代理失败')
            }
          } catch (proxyError) {
            console.error('[代理设置] 自动设置失败:', proxyError)
            toast.warning('⚠️ 自动设置代理失败', {
              description: '代理服务已启动，但需要手动设置系统代理',
              duration: 6000
            })
            setShowProxyInstructions(true)
          }
        } else {
          // 手动模式提示
          toast.success('🎯 代理服务已启动', {
            description: '请手动设置系统代理为 127.0.0.1:8899',
            duration: 8000
          })
          setShowProxyInstructions(true)
        }
        
        // 启动成功后检查状态和获取资源
        setTimeout(() => {
          checkProxyStatus()
          fetchResources()
        }, 1000)
        
      } else {
        const errorMsg = result?.error || result?.message || '启动失败：未知错误'
        console.error('[代理启动] 后端启动失败:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      console.error('[代理启动] 完整错误:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      // 设置代理状态为错误
      setProxyStatus(prev => ({ 
        ...prev, 
        running: false, 
        status: 'error',
        available: true 
      }))
      
      // 分类错误处理
      if (errorMessage.includes('后端服务连接失败') || errorMessage.includes('无法连接到后端服务')) {
        toast.error('🔌 后端服务连接失败', {
          description: '请确保后端服务正在运行在 http://localhost:8000，可以尝试重新启动应用',
          duration: 10000
        })
      } else if (errorMessage.includes('mitmproxy不可用') || errorMessage.includes('mitmproxy')) {
        toast.error('📦 mitmproxy 依赖问题', {
          description: '请检查 mitmproxy 是否正确安装：pip install mitmproxy',
          duration: 8000
        })
      } else if (errorMessage.includes('权限') || errorMessage.includes('Permission')) {
        toast.error('🔒 权限不足', {
          description: '启动代理可能需要管理员权限，请尝试以管理员身份运行应用',
          duration: 8000
        })
      } else if (errorMessage.includes('Address already in use') || errorMessage.includes('端口')) {
        toast.error('🔌 端口占用', {
          description: '代理端口8899被占用，请检查是否有其他程序在使用该端口',
          duration: 8000
        })
      } else {
        toast.error(`❌ 代理启动失败`, {
          description: `错误详情: ${errorMessage}`,
          duration: 10000
        })
      }
    } finally {
      setIsLoading(false)
      console.log('[代理启动] 启动流程结束')
    }
  }, [apiCall, fetchResources, checkProxyStatus])

  // 停止代理服务（集成权限管理）
  const stopProxy = useCallback(async () => {
    try {
      setIsLoading(true)
      console.log('[代理停止] 开始停止代理服务...')
      
      // 如果有权限且在Electron环境中，自动清除系统代理
      if (systemPermission.granted && typeof window !== 'undefined' && window.permissionAPI) {
        try {
          console.log('[代理停止] 尝试自动清除系统代理...')
          const proxyResult = await window.permissionAPI.setSystemProxy(false)
          
          if (proxyResult.success) {
            console.log('[代理停止] 系统代理已自动清除')
          } else {
            console.warn('[代理停止] 自动清除代理失败:', proxyResult.error)
          }
        } catch (proxyError) {
          console.error('[代理停止] 清除代理时出错:', proxyError)
        }
      }
      
      // 停止后端代理服务
      const result = await apiCall('/proxy/stop', {
        method: 'POST'
      })
      console.log('[代理停止] 后端停止结果:', result)
      
      if (result.success) {
        setProxyStatus(prev => ({ ...prev, running: false, status: 'stopped' }))
        
        if (systemPermission.granted) {
          toast.success('🎉 抓包已停止！', {
            description: '✅ 代理服务已停止，系统代理已自动清除',
            duration: 4000
          })
        } else {
          toast.success('🎯 代理服务已停止', {
            description: '⚠️ 请手动清除系统代理设置（如果之前手动设置过）',
            duration: 6000
          })
        }
      } else {
        throw new Error(result.error || result.message || '停止失败')
      }
    } catch (error) {
      console.error('停止代理失败:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      toast.error(`❌ 停止失败: ${errorMessage}`)
    } finally {
      setIsLoading(false)
    }
  }, [systemPermission, apiCall])

  // 重启代理服务
  const restartProxy = useCallback(async () => {
    try {
      setIsLoading(true)
      console.log('[代理重启] 开始重启代理服务...')
      
      const result = await apiCall('/proxy/restart', {
        method: 'POST'
      })
      console.log('[代理重启] 结果:', result)
      
      if (result.success) {
        setProxyStatus(prev => ({ ...prev, running: true }))
        toast.success('🔄 代理服务已重启')
        // 重启后自动获取资源
        setTimeout(fetchResources, 1000)
      } else {
        throw new Error(result.error || result.message || '重启失败')
      }
    } catch (error) {
      console.error('重启代理失败:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      toast.error(`❌ 重启失败: ${errorMessage}`)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 安装证书
  const installCertificate = useCallback(async () => {
    try {
      setIsLoading(true)
      console.log('[证书安装] 开始安装证书...')
      
      // 优先使用 Electron 客户端安装（支持跨平台）
      if (typeof window !== 'undefined' && window.permissionAPI) {
        console.log('[证书安装] 使用 Electron 客户端安装')
        
        // 先检查平台支持
        const permissionState = await window.permissionAPI.getPermissionState()
        if (!permissionState.platformSupported) {
          toast.error('❌ 不支持的平台', {
            description: `当前平台 ${permissionState.platform} 暂不支持自动证书安装`
          })
          return
        }
        
        toast.info(`🔐 正在 ${permissionState.platform} 上安装证书...`, {
          description: '可能需要输入管理员密码'
        })
        
        const result = await window.permissionAPI.installCertificate()
        
        if (result.success) {
          toast.success('📜 证书安装成功', {
            description: `✅ 在 ${permissionState.platform} 上成功安装HTTPS抓包证书，现在可以捕获加密流量了`
          })
          
          // 更新证书状态
          const statusResult = await window.permissionAPI.checkCertificateStatus()
          console.log('[证书状态] 更新:', statusResult)
        } else if (result.cancelled) {
          toast.warning('⚠️ 证书安装被取消', {
            description: '用户取消了证书安装授权，HTTPS流量将无法解密'
          })
        } else {
          // 根据平台提供具体的错误提示
          const platformSpecificTips = {
            'macOS': '请确保有管理员权限，可以尝试在"系统偏好设置 → 安全性与隐私"中允许此操作',
            'Windows': '请以管理员身份运行应用，或在"用户账户控制"弹窗中选择"是"',
            'Linux': '请确保有sudo权限，或使用pkexec/gksudo进行授权'
          }
          
          const platformTip = platformSpecificTips[permissionState.platform as keyof typeof platformSpecificTips] || '请检查系统权限设置'
          
          toast.error(`❌ ${permissionState.platform} 证书安装失败`, {
            description: `${result.error || '未知错误'}。${platformTip}`
          })
        }
      } else {
        // 降级到后端API（仅用于备用）
        console.log('[证书安装] 使用后端API安装')
        const result = await apiCall('/proxy/setup/certificate', {
          method: 'POST'
        })
        
        if (result.success) {
          toast.success('📜 证书安装成功', {
            description: '现在可以抓包HTTPS流量了'
          })
        } else {
          toast.error(`❌ 证书安装失败`, {
            description: `${result.error || '未知错误'}，可能需要管理员权限`
          })
        }
      }
    } catch (error) {
      console.error('安装证书失败:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      toast.error(`❌ 证书安装异常`, {
        description: `系统错误: ${errorMessage}`
      })
    } finally {
      setIsLoading(false)
    }
  }, [apiCall])

  // 检查系统代理状态
  const checkSystemProxy = useCallback(async () => {
    try {
      console.log('[系统代理] 检查系统代理状态...')
      
      const result = await apiCall('/proxy/system/proxy-status')
      console.log('[系统代理] 状态:', result)
      
      if (result.success) {
        const proxyEnabled = result.proxy_enabled
        const message = proxyEnabled 
          ? `✅ 系统代理已设置: ${result.proxy_address}`
          : '❌ 系统代理未设置'
        
        toast.info('🔍 系统代理状态', { description: message })
      } else {
        throw new Error(result.error || '检查失败')
      }
    } catch (error) {
      console.error('检查系统代理失败:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      toast.error(`❌ 检查失败: ${errorMessage}`)
    }
  }, [])

  // 清空资源列表
  const clearResources = useCallback(async () => {
    try {
      const result = await apiCall('/proxy/resources/clear', { method: 'POST' })
      if (result.success) {
        setResources([])
        setSelectedResources(new Set())
        toast.success('资源列表已清空')
      }
    } catch (error) {
      console.error('清空资源列表失败:', error)
      toast.error('清空资源列表失败')
    }
  }, [])

  // 下载资源
  const downloadResource = useCallback(async (resourceId: string) => {
    try {
      const result = await apiCall(`/proxy/resources/${resourceId}/download`, { method: 'POST' })
      if (result.success) {
        toast.success(t.resourceDownloadStarted)
        await fetchResources() // 刷新资源状态
      }
    } catch (error) {
      console.error('下载资源失败:', error)
      toast.error('下载资源失败')
    }
  }, [t.resourceDownloadStarted])

  // 转录资源
  const transcribeResource = useCallback(async (resourceId: string) => {
    try {
      const result = await apiCall(`/proxy/resources/${resourceId}/transcribe`, { method: 'POST' })
      if (result.success) {
        toast.success(t.resourceTranscribeStarted)
        await fetchResources() // 刷新资源状态
      }
    } catch (error) {
      console.error('转录资源失败:', error)
      toast.error('转录资源失败')
    }
  }, [t.resourceTranscribeStarted])

  // 批量操作选中的资源
  const handleBatchAction = useCallback(async (action: 'download' | 'transcribe') => {
    const selectedIds = Array.from(selectedResources)
    if (selectedIds.length === 0) {
      toast.warning('请先选择资源')
      return
    }

    for (const resourceId of selectedIds) {
      if (action === 'download') {
        await downloadResource(resourceId)
      } else {
        await transcribeResource(resourceId)
      }
    }
  }, [selectedResources, downloadResource, transcribeResource])

  // 权限请求函数
  const requestPermissions = useCallback(async () => {
    try {
      setIsLoading(true)
      
      // 检查是否在Electron环境中
      if (typeof window !== 'undefined' && (window as any).electronAPI?.requestPermissions) {
        // Electron环境
        const result = await (window as any).electronAPI.requestPermissions()
        
        if (result?.success) {
          setPermissionStatus({
            screen: result.permissions?.screen || 'unknown',
            microphone: result.permissions?.microphone || 'unknown'
          })
          
          toast.success('权限请求成功')
        } else {
          toast.error('权限请求失败: ' + (result?.error || '未知错误'))
        }
      } else {
        // 浏览器环境 - 使用Web API
        const permissions = {
          screen: 'unknown',
          microphone: 'unknown'
        }
        
        try {
          // 请求麦克风权限
          const micStream = await navigator.mediaDevices.getUserMedia({ audio: true })
          permissions.microphone = 'granted'
          micStream.getTracks().forEach(track => track.stop()) // 立即停止流
          toast.success('🎤 麦克风权限已获取')
        } catch (micError) {
          permissions.microphone = 'denied'
          console.warn('麦克风权限被拒绝:', micError)
        }
        
        try {
          // 请求屏幕共享权限（仅作为测试，实际使用时会弹出选择窗口）
          if (navigator.mediaDevices.getDisplayMedia) {
            const screenStream = await navigator.mediaDevices.getDisplayMedia({ video: true })
            permissions.screen = 'granted'
            screenStream.getTracks().forEach(track => track.stop()) // 立即停止流
            toast.success('🖥️ 屏幕共享权限已获取')
          }
        } catch (screenError) {
          permissions.screen = 'denied'
          console.warn('屏幕共享权限被拒绝:', screenError)
        }
        
        setPermissionStatus(permissions)
        
        if (permissions.microphone === 'granted' || permissions.screen === 'granted') {
          toast.success('媒体权限请求完成')
        } else {
          toast.warning('部分媒体权限被拒绝，这可能影响某些功能')
        }
      }
    } catch (error) {
      console.error('权限请求失败:', error)
      toast.error('权限请求过程中发生错误: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 检查权限状态
  const checkPermissions = useCallback(async () => {
    try {
      if (typeof window !== 'undefined' && navigator.permissions) {
        // 现代浏览器权限API
        const micPermission = await navigator.permissions.query({ name: 'microphone' as PermissionName })
        setPermissionStatus(prev => ({
          ...prev,
          microphone: micPermission.state
        }))
      }
    } catch (error) {
      // 某些浏览器可能不支持权限API
      console.log('权限API不可用，将在用户操作时检查')
    }
  }, [])

  // 一键配置代理（首次使用优化）
  const oneClickSetup = useCallback(async () => {
    setIsLoading(true)
    try {
      // 1. 启动代理服务
      const result = await apiCall('/proxy/start', { method: 'POST' })
      if (result.success) {
        // 2. 显示配置指南
        setShowProxyInstructions(true)
        
        // 3. 尝试复制代理地址到剪贴板
        try {
          await navigator.clipboard.writeText(`127.0.0.1:${proxyStatus.port}`)
          toast.success('代理已启动！代理地址已复制到剪贴板')
        } catch {
          toast.success('代理已启动！请按指南配置浏览器代理')
        }
        
        await checkProxyStatus()
      }
    } catch (error) {
      console.error('一键配置失败:', error)
      toast.error('配置失败，请手动启动代理服务')
    } finally {
      setIsLoading(false)
    }
  }, [proxyStatus.port])

  // 测试代理连接
  const testConnection = useCallback(async () => {
    try {
      setIsLoading(true)
      console.log('[测试连接] 开始测试代理连接...')
      
      const result = await apiCall('/proxy/test/connection', { method: 'POST' })
      console.log('[测试连接] 结果:', result)
      
      if (result.success) {
        toast.success(`✅ ${result.message || '代理连接测试成功！'}`)
      } else {
        toast.error(`❌ 连接测试失败: ${result.message || '未知错误'}`)
      }
    } catch (error) {
      console.error('测试连接失败:', error)
      toast.error('❌ 无法连接到代理服务，请检查代理是否正常启动')
    } finally {
      setIsLoading(false)
    }
  }, [apiCall])

  // 组件初始化
  useEffect(() => {
    checkProxyStatus()
    fetchResources()
    checkPermissions()
  }, [])

  // 智能自动启动逻辑
  useEffect(() => {
    const autoStart = async () => {
      try {
        const result = await apiCall('/proxy/status')
        if (result.success && result.available !== false && !result.running) {
          console.log('代理可用但未运行，准备自动启动...')
          
          // 检查是否是首次使用（可以根据localStorage或其他方式判断）
          const isFirstTime = !localStorage.getItem('proxy-auto-started')
          
                     if (isFirstTime) {
             // 首次使用，显示友好提示
             toast.info('🚀 正在为您自动启动代理服务...这是首次使用，系统将自动配置抓包功能')
             localStorage.setItem('proxy-auto-started', 'true')
           }
          
          setTimeout(async () => {
            try {
              await startProxy()
            } catch (error) {
              console.error('自动启动失败:', error)
              if (isFirstTime) {
                toast.error('自动启动失败，请点击"一键配置"手动启动')
              }
            }
          }, 1000)
        }
      } catch (error) {
        console.error('检查代理状态失败:', error)
      }
    }
    
    // 只在组件首次加载时执行一次
    autoStart()
  }, [])

  // 定期刷新状态（独立的 useEffect）
  useEffect(() => {
    const interval = setInterval(() => {
      checkProxyStatus()
      // 只有在代理运行时才刷新资源列表
      if (proxyStatus.running) {
        fetchResources()
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [proxyStatus.running])

  // 格式化文件大小
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '-'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  // 格式化时间
  const formatTime = (isoString: string) => {
    return new Date(isoString).toLocaleString()
  }

  // 获取状态徽章颜色
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'detected': return 'default'
      case 'downloading': return 'secondary'
      case 'completed': return 'default'
      case 'failed': return 'destructive'
      default: return 'default'
    }
  }

  return (
    <div className="space-y-6">
      {/* 代理控制面板 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                智能抓包
              </CardTitle>
              <CardDescription>
                参考res-downloader，自动配置系统代理，一键开始抓包
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHelp(true)}
            >
              <HelpCircle className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* 主状态显示 - res-downloader风格 */}
          <div className="flex items-center justify-center p-8 bg-gradient-to-br from-blue-50 to-green-50 dark:from-blue-950/50 dark:to-green-950/50 rounded-lg border-2 border-dashed border-blue-200 dark:border-blue-800">
            <div className="text-center space-y-4">
              {/* 状态指示器 */}
              <div className="flex justify-center">
                {proxyStatus.running ? (
                  <div className="relative">
                    <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                      <Zap className="h-8 w-8 text-white" />
                    </div>
                    <div className="absolute -inset-1 bg-green-400 rounded-full animate-ping opacity-75"></div>
                  </div>
                ) : (
                  <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <Power className="h-8 w-8 text-gray-600 dark:text-gray-400" />
                  </div>
                )}
              </div>
              
              {/* 状态文字 */}
              <div>
                <h3 className="text-lg font-semibold">
                  {proxyStatus.running ? '🎯 抓包进行中' : '⏸️ 抓包已停止'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {proxyStatus.running 
                    ? `在端口 ${proxyStatus.port} 监听网络流量` 
                    : '点击开启按钮开始智能抓包'}
                </p>
              </div>
              
              {/* 资源统计 */}
              {proxyStatus.running && (
                <div className="space-y-1">
                  <div className="flex items-center justify-center gap-2 text-sm text-green-600 dark:text-green-400">
                    <FileVideo className="h-4 w-4" />
                    已检测到 {proxyStatus.resources_count} 个媒体资源
                  </div>
                  
                  {/* 代理模式信息 */}
                  {proxyStatus.proxy_mode && (
                    <div className="flex items-center justify-center gap-2 text-xs text-blue-600 dark:text-blue-400">
                      {proxyStatus.proxy_mode === 'upstream' ? (
                        <>
                          <span className="inline-flex items-center gap-1">
                            🔗 代理链模式
                          </span>
                        </>
                      ) : (
                        <>
                          <span className="inline-flex items-center gap-1">
                            🚀 常规模式
                          </span>
                        </>
                      )}
                    </div>
                  )}
                  
                  {/* 详细模式信息 */}
                  {proxyStatus.proxy_mode_info && (
                    <div className="text-xs text-center text-muted-foreground max-w-md mx-auto">
                      {proxyStatus.proxy_mode_info}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* 权限状态显示 */}
          {systemPermission.checked && (
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg border">
              <div className="flex items-center gap-2">
                {systemPermission.granted ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                )}
                <span className="text-sm font-medium">
                  {systemPermission.granted ? '✅ 已获得系统权限' : '⚠️ 需要系统权限'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-xs text-muted-foreground">
                {systemPermission.granted 
                  ? '可自动管理系统代理' 
                  : '手动模式或请求授权'}
                </div>
                {!systemPermission.granted && (
                  <Button
                    onClick={requestSystemAuthorization}
                    disabled={isLoading}
                    size="sm"
                    variant="outline"
                    className="h-7 px-2 text-xs"
                  >
                    <Shield className="h-3 w-3 mr-1" />
                    重新授权
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* 主要控制按钮 - res-downloader风格 */}
          <div className="grid grid-cols-2 gap-4">
            {!proxyStatus.running ? (
              <Button
                onClick={startProxy}
                disabled={isLoading}
                size="lg"
                className="col-span-2 h-16 text-lg font-semibold bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    启动中...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-5 w-5" />
                    {systemPermission.granted ? '🚀 一键开启抓包' : '⚡ 开启抓包'}
                  </>
                )}
              </Button>
            ) : (
              <>
                <Button
                  onClick={stopProxy}
                  disabled={isLoading}
                  size="lg"
                  variant="outline"
                  className="h-16 text-lg"
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Square className="mr-2 h-4 w-4" />
                  )}
                  停止抓包
                </Button>
                <Button
                  onClick={restartProxy}
                  disabled={isLoading}
                  size="lg"
                  variant="outline"
                  className="h-16 text-lg"
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RotateCcw className="mr-2 h-4 w-4" />
                  )}
                  重启服务
                </Button>
              </>
            )}
          </div>
          
          {/* 功能按钮组 */}
          <div className="grid grid-cols-2 gap-3">
            <Button
              onClick={testConnection}
              disabled={isLoading}
              variant="outline"
              size="sm"
              className="flex flex-col h-20 p-2"
            >
              <Wifi className="h-4 w-4 mb-1" />
              <span className="text-xs">测试连接</span>
            </Button>
            
            <Button
              onClick={clearResources}
              disabled={isLoading}
              variant="outline"
              size="sm"
              className="flex flex-col h-20 p-2"
            >
              <Trash2 className="h-4 w-4 mb-1" />
              <span className="text-xs">清空记录</span>
            </Button>
          </div>
          
          {/* 高级功能折叠面板 */}
          <details className="group">
            <summary className="cursor-pointer p-3 bg-muted rounded-lg hover:bg-muted/80 transition-colors">
              <div className="flex items-center justify-between">
                <span className="font-medium flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  高级设置
                </span>
                <ChevronDown className="h-4 w-4 group-open:rotate-180 transition-transform" />
              </div>
            </summary>
            
            <div className="mt-4 space-y-3 pl-4 border-l-2 border-muted">
              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={installCertificate}
                  disabled={isLoading}
                  variant="outline"
                  size="sm"
                  className="flex flex-col h-16 p-2"
                >
                  <Shield className="h-4 w-4 mb-1" />
                  <span className="text-xs">安装证书</span>
                </Button>
                
                <Button
                  onClick={checkSystemProxy}
                  disabled={isLoading}
                  variant="outline"
                  size="sm"
                  className="flex flex-col h-16 p-2"
                >
                  <Info className="h-4 w-4 mb-1" />
                  <span className="text-xs">检查代理</span>
                </Button>
              </div>
              
              <div className="text-xs text-muted-foreground space-y-1">
                <p>• 证书安装：启用HTTPS流量抓包（需要管理员权限）</p>
                <p>• 检查代理：查看当前系统代理设置状态</p>
              </div>
            </div>
          </details>
          
        </CardContent>
      </Card>

      {/* 资源列表 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{t.detectedResources}</CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchResources}
              >
                <RefreshCw className="h-4 w-4" />
                {t.refreshResources}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearResources}
                disabled={resources.length === 0}
              >
                <Trash2 className="h-4 w-4" />
                {t.clearResources}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {resources.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <Globe className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{t.noResources}</p>
              {!proxyStatus.running && (
                <p className="text-sm mt-2">请先启动代理服务</p>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {/* 批量操作 */}
              {selectedResources.size > 0 && (
                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                  <span className="text-sm">
                    已选择 {selectedResources.size} 个资源
                  </span>
                  <Button
                    size="sm"
                    onClick={() => handleBatchAction('download')}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    批量下载
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => handleBatchAction('transcribe')}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    批量转录
                  </Button>
                </div>
              )}

              {/* 资源列表 */}
              {resources.map((resource) => (
                <Card key={resource.id} className="p-4">
                  <div className="flex items-start gap-4">
                    <Checkbox
                      checked={selectedResources.has(resource.id)}
                      onCheckedChange={(checked) => {
                        const newSelected = new Set(selectedResources)
                        if (checked) {
                          newSelected.add(resource.id)
                        } else {
                          newSelected.delete(resource.id)
                        }
                        setSelectedResources(newSelected)
                      }}
                    />
                    
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium line-clamp-1">{resource.title}</h4>
                        <Badge variant={getStatusBadgeColor(resource.status)}>
                          {resource.status}
                        </Badge>
                      </div>
                      
                      {resource.description && (
                        <p className="text-sm text-muted-foreground">{resource.description}</p>
                      )}
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Globe className="h-3 w-3" />
                          {resource.platform}
                        </span>
                                                 <span className="flex items-center gap-1">
                           <FileVideo className="h-3 w-3" />
                           {resource.media_type}
                         </span>
                        {resource.content_type && (
                          <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {resource.content_type.split('/')[1] || resource.content_type}
                          </span>
                        )}
                                                 {resource.file_size && (
                           <span className="flex items-center gap-1">
                             <Copy className="h-3 w-3" />
                             {formatFileSize(resource.file_size)}
                           </span>
                         )}
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatTime(resource.detected_at)}
                        </span>
                      </div>
                      
                      {resource.referer && (
                        <div className="text-xs text-blue-500 truncate">
                          来源: {resource.referer}
                        </div>
                      )}
                      
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          onClick={() => downloadResource(resource.id)}
                          disabled={resource.status === 'downloading'}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t.downloadResource}
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => transcribeResource(resource.id)}
                        >
                          <Play className="h-4 w-4 mr-2" />
                          {t.transcribeResource}
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 权限授权对话框 */}
      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-500" />
              {authDialogContent.title}
            </DialogTitle>
            <DialogDescription className="space-y-3 pt-2">
              <p>{authDialogContent.message}</p>
              {authDialogContent.description && (
                <p className="text-sm text-muted-foreground bg-muted p-3 rounded-lg">
                  {authDialogContent.description}
                </p>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowAuthDialog(false)}
            >
              取消
            </Button>
            <Button
              onClick={() => {
                if (authDialogContent.onConfirm) {
                  authDialogContent.onConfirm()
                } else {
                  setShowAuthDialog(false)
                }
              }}
              className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
            >
              确认
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 帮助对话框 */}
      <Dialog open={showHelp} onOpenChange={setShowHelp}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{t.helpTitle}</DialogTitle>
          </DialogHeader>
          <div className="prose prose-sm max-w-none">
            <pre className="whitespace-pre-wrap text-sm">{t.helpContent}</pre>
          </div>
        </DialogContent>
      </Dialog>

      {/* 代理设置说明对话框 */}
      <Dialog open={showProxyInstructions} onOpenChange={setShowProxyInstructions}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t.proxyInstructions}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {t.autoProxySetup}
              </AlertDescription>
            </Alert>
            
            <div className="space-y-2">
              <h4 className="font-medium">手动设置代理:</h4>
              <div className="bg-muted p-3 rounded-lg text-sm space-y-1">
                <div>HTTP代理: 127.0.0.1:{proxyStatus.port}</div>
                <div>HTTPS代理: 127.0.0.1:{proxyStatus.port}</div>
              </div>
            </div>
            
            <div className="space-y-2 text-sm">
              <div>• {t.macOSProxy}</div>
              <div>• {t.windowsProxy}</div>
            </div>
            
            <Alert>
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>
                {t.browserUsage}
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowProxyInstructions(false)}>
              知道了
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 后端状态指示器 */}
      <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
        <div className={`w-3 h-3 rounded-full ${
          backendStatus === 'connected' ? 'bg-green-500' : 
          backendStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
        }`} />
        <span className="text-sm font-medium">
          后端状态: {
            backendStatus === 'connected' ? '已连接' :
            backendStatus === 'connecting' ? '连接中...' : '连接失败'
          }
        </span>
        {backendStatus === 'error' && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={checkBackendStatus}
            className="ml-auto"
          >
            重试连接
          </Button>
        )}
      </div>

      {/* 错误信息显示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
} 