"use client"

import { useState, useRef, useCallback } from "react"
import { transcriptionApi } from "@/lib/api"
import { useToast } from "@/components/toast-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import {
  Upload,
  File,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  FileAudio,
  FileVideo,
  Languages,
  Settings,
  Play,
} from "lucide-react"

interface FileUploadItem {
  id: string
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
  result?: {
    transcription: string
    language: string
    confidence: number
    provider: string
  }
}

interface EnhancedFileUploadProps {
  onUploadComplete?: (result: any) => void
  onError?: (error: string) => void
  language: "zh" | "en"
}

const translations = {
  zh: {
    uploadFiles: "上传文件",
    dropFilesHere: "将文件拖拽到这里",
    orClickToSelect: "或点击选择文件",
    supportedFormats: "支持的格式",
    maxFileSize: "最大文件大小",
    audioFormats: "音频格式：MP3, M4A, WAV, AAC, FLAC",
    videoFormats: "视频格式：MP4, MOV, MKV, AVI, WMV",
    selectFiles: "选择文件",
    uploadSettings: "上传设置",
    language: "语言",
    autoDetect: "自动检测",
    provider: "转录服务",
    startUpload: "开始上传",
    clearAll: "清空所有",
    uploading: "上传中",
    processing: "转录中",
    completed: "已完成",
    failed: "失败",
    pending: "等待中",
    retryUpload: "重试",
    removeFile: "移除",
    previewResult: "预览结果",
    confidence: "置信度",
    fileSize: "文件大小",
    duration: "时长",
    transcriptionResult: "转录结果",
    noFilesSelected: "未选择文件",
    selectFilesToUpload: "请选择要上传的文件",
    fileTooBig: "文件过大",
    fileSizeLimit: "文件大小不能超过 100MB",
    unsupportedFormat: "不支持的格式",
    invalidFileType: "请选择支持的音频或视频文件",
    uploadInProgress: "上传进行中",
    cannotModifyDuringUpload: "上传过程中无法修改设置",
    allFilesCompleted: "所有文件处理完成",
    someFilesFailed: "部分文件处理失败",
    chinese: "中文",
    english: "英文",
    japanese: "日文",
    korean: "韩文",
  },
  en: {
    uploadFiles: "Upload Files",
    dropFilesHere: "Drop files here",
    orClickToSelect: "or click to select files",
    supportedFormats: "Supported Formats",
    maxFileSize: "Max File Size",
    audioFormats: "Audio: MP3, M4A, WAV, AAC, FLAC",
    videoFormats: "Video: MP4, MOV, MKV, AVI, WMV",
    selectFiles: "Select Files",
    uploadSettings: "Upload Settings",
    language: "Language",
    autoDetect: "Auto Detect",
    provider: "Transcription Service",
    startUpload: "Start Upload",
    clearAll: "Clear All",
    uploading: "Uploading",
    processing: "Processing",
    completed: "Completed",
    failed: "Failed",
    pending: "Pending",
    retryUpload: "Retry",
    removeFile: "Remove",
    previewResult: "Preview Result",
    confidence: "Confidence",
    fileSize: "File Size",
    duration: "Duration",
    transcriptionResult: "Transcription Result",
    noFilesSelected: "No files selected",
    selectFilesToUpload: "Please select files to upload",
    fileTooBig: "File too large",
    fileSizeLimit: "File size cannot exceed 100MB",
    unsupportedFormat: "Unsupported format",
    invalidFileType: "Please select supported audio or video files",
    uploadInProgress: "Upload in progress",
    cannotModifyDuringUpload: "Cannot modify settings during upload",
    allFilesCompleted: "All files completed successfully",
    someFilesFailed: "Some files failed to process",
    chinese: "Chinese",
    english: "English",
    japanese: "Japanese",
    korean: "Korean",
  },
}

export default function EnhancedFileUpload({ 
  onUploadComplete, 
  onError, 
  language 
}: EnhancedFileUploadProps) {
  const t = translations[language]
  const toast = useToast()
  
  const [uploadItems, setUploadItems] = useState<FileUploadItem[]>([])
  const [dragOver, setDragOver] = useState(false)
  const [selectedLanguage, setSelectedLanguage] = useState("auto")
  const [selectedProvider, setSelectedProvider] = useState("auto")
  const [isUploading, setIsUploading] = useState(false)
  
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 支持的文件类型
  const supportedTypes = [
    // 音频格式
    'audio/mpeg', 'audio/mp3', 'audio/m4a', 'audio/wav', 
    'audio/aac', 'audio/flac', 'audio/ogg',
    // 视频格式
    'video/mp4', 'video/quicktime', 'video/x-msvideo',
    'video/x-matroska', 'video/x-ms-wmv', 'video/webm'
  ]

  // 最大文件大小 (100MB)
  const maxFileSize = 100 * 1024 * 1024

  // 验证文件
  const validateFile = (file: File): { valid: boolean; error?: string } => {
    if (file.size > maxFileSize) {
      return { valid: false, error: t.fileSizeLimit }
    }
    
    if (!supportedTypes.includes(file.type)) {
      return { valid: false, error: t.invalidFileType }
    }
    
    return { valid: true }
  }

  // 生成唯一ID
  const generateId = () => Math.random().toString(36).substr(2, 9)

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  // 获取文件图标
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('audio/')) {
      return <FileAudio className="w-6 h-6 text-blue-500" />
    } else if (file.type.startsWith('video/')) {
      return <FileVideo className="w-6 h-6 text-purple-500" />
    }
    return <File className="w-6 h-6 text-gray-500" />
  }

  // 获取状态颜色
  const getStatusColor = (status: FileUploadItem['status']) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-600'
      case 'uploading': return 'bg-blue-100 text-blue-600'
      case 'processing': return 'bg-yellow-100 text-yellow-600'
      case 'completed': return 'bg-green-100 text-green-600'
      case 'error': return 'bg-red-100 text-red-600'
      default: return 'bg-gray-100 text-gray-600'
    }
  }

  // 获取状态文本
  const getStatusText = (status: FileUploadItem['status']) => {
    switch (status) {
      case 'pending': return t.pending
      case 'uploading': return t.uploading
      case 'processing': return t.processing
      case 'completed': return t.completed
      case 'error': return t.failed
      default: return t.pending
    }
  }

  // 处理文件选择
  const handleFileSelect = useCallback((files: FileList) => {
    const newItems: FileUploadItem[] = []
    
    Array.from(files).forEach(file => {
      const validation = validateFile(file)
      
      if (validation.valid) {
        newItems.push({
          id: generateId(),
          file,
          progress: 0,
          status: 'pending'
        })
      } else {
        // 显示错误
        toast.error(validation.error || t.invalidFileType)
        onError?.(validation.error || t.invalidFileType)
      }
    })
    
    setUploadItems(prev => [...prev, ...newItems])
  }, [onError, t])

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFileSelect(files)
    }
  }, [handleFileSelect])

  // 文件输入处理
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      handleFileSelect(files)
    }
    // 清空input值以允许重新选择相同文件
    e.target.value = ''
  }, [handleFileSelect])

  // 移除文件
  const removeFile = (id: string) => {
    setUploadItems(prev => prev.filter(item => item.id !== id))
  }

  // 清空所有文件
  const clearAllFiles = () => {
    setUploadItems([])
  }

  // 重试上传
  const retryUpload = async (id: string) => {
    const item = uploadItems.find(item => item.id === id)
    if (!item) return

    setUploadItems(prev => prev.map(item => 
      item.id === id 
        ? { ...item, status: 'pending', progress: 0, error: undefined }
        : item
    ))

    await uploadSingleFile(item)
  }

  // 上传单个文件
  const uploadSingleFile = async (item: FileUploadItem) => {
    try {
      // 更新状态为上传中
      setUploadItems(prev => prev.map(i => 
        i.id === item.id 
          ? { ...i, status: 'uploading', progress: 10 }
          : i
      ))

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadItems(prev => prev.map(i => 
          i.id === item.id && i.progress < 50
            ? { ...i, progress: i.progress + 10 }
            : i
        ))
      }, 500)

      // 调用API上传文件
      const result = await transcriptionApi.transcribeFile(item.file, {
        language: selectedLanguage !== "auto" ? selectedLanguage : undefined,
        provider: selectedProvider !== "auto" ? selectedProvider : undefined,
      })

      clearInterval(progressInterval)

      if (result.success) {
        // 更新状态为处理中
        setUploadItems(prev => prev.map(i => 
          i.id === item.id 
            ? { 
                ...i, 
                status: 'processing', 
                progress: 75,
                result: {
                  transcription: result.data?.transcript || "",
                  language: result.data?.language || "unknown",
                  confidence: 0.95,
                  provider: result.data?.provider || "unknown"
                }
              }
            : i
        ))

        // 模拟处理完成
        setTimeout(() => {
          setUploadItems(prev => prev.map(i => 
            i.id === item.id 
              ? { ...i, status: 'completed', progress: 100 }
              : i
          ))
          
          onUploadComplete?.(result)
          toast.success(t.completed, `${item.file.name} 转录完成`)
        }, 2000)
      } else {
        // 上传失败
        setUploadItems(prev => prev.map(i => 
          i.id === item.id 
            ? { 
                ...i, 
                status: 'error', 
                progress: 0,
                error: result.message || t.failed
              }
            : i
        ))
      }
    } catch (error) {
      // 处理错误
      setUploadItems(prev => prev.map(i => 
        i.id === item.id 
          ? { 
              ...i, 
              status: 'error', 
              progress: 0,
              error: error instanceof Error ? error.message : t.failed
            }
          : i
      ))
      
      const errorMessage = error instanceof Error ? error.message : t.failed
      toast.error(t.failed, errorMessage)
      onError?.(errorMessage)
    }
  }

  // 开始上传所有文件
  const startUpload = async () => {
    const pendingItems = uploadItems.filter(item => item.status === 'pending')
    
    if (pendingItems.length === 0) {
      toast.warning(t.noFilesSelected, t.selectFilesToUpload)
      onError?.(t.selectFilesToUpload)
      return
    }

    setIsUploading(true)

    try {
      // 并发上传（限制同时上传数量）
      const concurrentLimit = 3
      const chunks = []
      
      for (let i = 0; i < pendingItems.length; i += concurrentLimit) {
        chunks.push(pendingItems.slice(i, i + concurrentLimit))
      }

      for (const chunk of chunks) {
        await Promise.all(chunk.map(item => uploadSingleFile(item)))
      }

      // 检查结果
      const finalItems = uploadItems
      const failedItems = finalItems.filter(item => item.status === 'error')
      
      if (failedItems.length === 0) {
        toast.success(t.allFilesCompleted, `成功处理 ${finalItems.length} 个文件`)
        onUploadComplete?.({ message: t.allFilesCompleted })
      } else {
        toast.warning(t.someFilesFailed, `${failedItems.length} 个文件处理失败`)
        onError?.(t.someFilesFailed)
      }
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 拖拽上传区域 */}
      <Card
        className={`border-2 border-dashed transition-colors cursor-pointer ${
          dragOver 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-blue-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Upload className={`w-12 h-12 mb-4 ${dragOver ? 'text-blue-500' : 'text-gray-400'}`} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {dragOver ? t.dropFilesHere : t.uploadFiles}
          </h3>
          <p className="text-gray-500 text-center mb-4">
            {t.dropFilesHere} {t.orClickToSelect}
          </p>
          
          <div className="text-sm text-gray-400 space-y-1 text-center">
            <p><strong>{t.supportedFormats}:</strong></p>
            <p>{t.audioFormats}</p>
            <p>{t.videoFormats}</p>
            <p><strong>{t.maxFileSize}:</strong> 100MB</p>
          </div>
          
          <Button className="mt-4" onClick={(e) => {
            e.stopPropagation()
            fileInputRef.current?.click()
          }}>
            <Upload className="w-4 h-4 mr-2" />
            {t.selectFiles}
          </Button>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="audio/*,video/*"
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* 上传设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            {t.uploadSettings}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                <Languages className="w-4 h-4 inline mr-1" />
                {t.language}
              </label>
              <Select 
                value={selectedLanguage} 
                onValueChange={setSelectedLanguage}
                disabled={isUploading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">{t.autoDetect}</SelectItem>
                  <SelectItem value="zh">{t.chinese}</SelectItem>
                  <SelectItem value="en">{t.english}</SelectItem>
                  <SelectItem value="ja">{t.japanese}</SelectItem>
                  <SelectItem value="ko">{t.korean}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">
                <Play className="w-4 h-4 inline mr-1" />
                {t.provider}
              </label>
              <Select 
                value={selectedProvider} 
                onValueChange={setSelectedProvider}
                disabled={isUploading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">{t.autoDetect}</SelectItem>
                  <SelectItem value="openai">OpenAI Whisper</SelectItem>
                  <SelectItem value="mock">Mock Provider</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {isUploading && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {t.cannotModifyDuringUpload}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 文件列表 */}
      {uploadItems.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                {t.uploadFiles} ({uploadItems.length})
              </CardTitle>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={clearAllFiles}
                  disabled={isUploading}
                >
                  {t.clearAll}
                </Button>
                <Button 
                  onClick={startUpload}
                  disabled={isUploading || uploadItems.every(item => item.status !== 'pending')}
                >
                  {isUploading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="w-4 h-4 mr-2" />
                  )}
                  {t.startUpload}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            {uploadItems.map((item, index) => (
              <div key={item.id}>
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  {getFileIcon(item.file)}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium truncate">{item.file.name}</h4>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(item.status)}>
                          {getStatusText(item.status)}
                        </Badge>
                        
                        {item.status === 'error' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => retryUpload(item.id)}
                          >
                            {t.retryUpload}
                          </Button>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(item.id)}
                          disabled={item.status === 'uploading' || item.status === 'processing'}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-2">
                      <span>{formatFileSize(item.file.size)}</span>
                      <span>{item.file.type}</span>
                      {item.result?.confidence && (
                        <span>{t.confidence}: {(item.result.confidence * 100).toFixed(1)}%</span>
                      )}
                    </div>
                    
                    {(item.status === 'uploading' || item.status === 'processing') && (
                      <Progress value={item.progress} className="h-2" />
                    )}
                    
                    {item.error && (
                      <Alert className="mt-2">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{item.error}</AlertDescription>
                      </Alert>
                    )}
                    
                    {item.result && item.status === 'completed' && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                        <p className="font-medium mb-1">{t.transcriptionResult}:</p>
                        <p className="text-gray-700 line-clamp-2">
                          {item.result.transcription.slice(0, 150)}...
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                {index < uploadItems.length - 1 && <Separator className="my-3" />}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  )
} 