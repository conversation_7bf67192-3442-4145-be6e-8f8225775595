import type { Metadata } from 'next'
import './globals.css'
import { ToastProvider } from "@/components/toast-provider"
import ErrorBoundary from "@/components/error-boundary"

export const metadata: Metadata = {
  title: 'VideoSense - AI音视频转录笔记工具',
  description: 'VideoSense是一款运行在用户个人电脑上的客户端智能笔记工具',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh">
      <body>
        <ErrorBoundary>
          <ToastProvider>
            {children}
          </ToastProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
