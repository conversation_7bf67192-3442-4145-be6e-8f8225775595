/**
 * 测试代理连接的API路由
 * 验证浏览器是否正确配置了代理设置
 */
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { proxy } = await request.json()
    
    // 检查请求是否通过代理转发
    const forwardedFor = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const userAgent = request.headers.get('user-agent')
    
    // 如果请求来自代理，通常会有这些头部信息
    const isProxied = !!(forwardedFor || realIp || 
                        request.headers.get('x-forwarded-proto') ||
                        request.headers.get('x-forwarded-host'))
    
    return NextResponse.json({
      success: true,
      proxied: isProxied,
      headers: {
        'x-forwarded-for': forwardedFor,
        'x-real-ip': realIp,
        'user-agent': userAgent
      },
      message: isProxied ? '检测到代理连接' : '未检测到代理连接',
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to test proxy connection',
      message: '测试代理连接失败'
    }, { status: 500 })
  }
} 