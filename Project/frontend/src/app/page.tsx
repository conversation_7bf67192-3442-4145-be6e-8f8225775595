"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { 
  FileText, 
  Play, 
  List, 
  History, 
  Settings, 
  Globe,
  Upload,
  Mic,
  Clock,
  Star
} from "lucide-react"

// 导入高级组件
import EnhancedFileUpload from "@/components/enhanced-file-upload"
import HistoryView from "@/components/history-view"
import ProxyCapture from "@/components/proxy-capture"
import { useToast } from "@/components/toast-provider"

export default function VideoSenseApp() {
  const [currentLanguage, setCurrentLanguage] = useState<'zh' | 'en'>('zh')
  const [activeTab, setActiveTab] = useState('upload')
  const toast = useToast()

  const toggleLanguage = () => {
    const newLang = currentLanguage === 'zh' ? 'en' : 'zh'
    setCurrentLanguage(newLang)
    toast.info(
      newLang === 'zh' ? '已切换到中文' : 'Switched to English',
      newLang === 'zh' ? '界面语言已更改' : 'Interface language changed'
    )
  }

  const handleUploadComplete = (result: any) => {
    toast.success(
      currentLanguage === 'zh' ? '转录完成' : 'Transcription Completed',
      currentLanguage === 'zh' ? '文件已成功转录并保存到历史记录' : 'File has been successfully transcribed and saved to history'
    )
    // 自动切换到历史记录标签
    setActiveTab('history')
  }

  const handleUploadError = (error: string) => {
    toast.error(
      currentLanguage === 'zh' ? '转录失败' : 'Transcription Failed',
      error
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">VS</span>
              </div>
              <div className="flex items-center space-x-2">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">VideoSense</h1>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  v2.0
                </Badge>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                size="sm"
                onClick={toggleLanguage}
                className="flex items-center space-x-2"
              >
                <Globe className="w-4 h-4" />
                <span>{currentLanguage === 'zh' ? '中文' : 'English'}</span>
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                {currentLanguage === 'zh' ? '设置' : 'Settings'}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="upload" className="flex items-center space-x-2">
              <Upload className="w-4 h-4" />
              <span>{currentLanguage === 'zh' ? '文件转录' : 'File Upload'}</span>
            </TabsTrigger>
            <TabsTrigger value="live" className="flex items-center space-x-2">
              <Mic className="w-4 h-4" />
              <span>{currentLanguage === 'zh' ? '智能抓包' : 'Smart Capture'}</span>
            </TabsTrigger>
            <TabsTrigger value="tasks" className="flex items-center space-x-2">
              <List className="w-4 h-4" />
              <span>{currentLanguage === 'zh' ? '任务管理' : 'Task Manager'}</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center space-x-2">
              <History className="w-4 h-4" />
              <span>{currentLanguage === 'zh' ? '历史记录' : 'History'}</span>
            </TabsTrigger>
          </TabsList>

          {/* 文件转录标签页 */}
          <TabsContent value="upload" className="space-y-6">
            <div className="text-center py-4">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {currentLanguage === 'zh' ? '文件转录' : 'File Transcription'}
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {currentLanguage === 'zh' 
                  ? '将音频、视频文件上传进行AI转录，支持多种格式和语言'
                  : 'Upload audio and video files for AI transcription, supporting multiple formats and languages'
                }
              </p>
            </div>

            <EnhancedFileUpload 
              language={currentLanguage}
              onUploadComplete={handleUploadComplete}
              onError={handleUploadError}
            />

            {/* 功能特性展示 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <Card>
                <CardHeader className="text-center">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <CardTitle className="text-lg">
                    {currentLanguage === 'zh' ? '高精度转录' : 'High Accuracy'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    {currentLanguage === 'zh' 
                      ? 'SiliconFlow AI技术驱动，支持中英日韩等多语言'
                      : 'Powered by SiliconFlow AI, supporting multiple languages'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Clock className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <CardTitle className="text-lg">
                    {currentLanguage === 'zh' ? '时间戳标记' : 'Timestamp Markers'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    {currentLanguage === 'zh' 
                      ? '为每个段落添加精确时间标记，便于定位内容'
                      : 'Precise timestamp markers for easy content navigation'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Star className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <CardTitle className="text-lg">
                    {currentLanguage === 'zh' ? '智能编辑' : 'Smart Editing'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    {currentLanguage === 'zh' 
                      ? '支持Markdown格式，内置编辑器和导出功能'
                      : 'Markdown support with built-in editor and export features'
                    }
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 智能抓包标签页 */}
          <TabsContent value="live" className="space-y-6">
            <div className="text-center py-4">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {currentLanguage === 'zh' ? '智能抓包' : 'Smart Capture'}
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {currentLanguage === 'zh' 
                  ? '通过网络代理自动检测和捕获音视频资源，支持YouTube、Bilibili等主流平台'
                  : 'Automatically detect and capture audio/video resources through network proxy, supporting YouTube, Bilibili and other platforms'
                }
              </p>
            </div>

            <ProxyCapture language={currentLanguage} />
          </TabsContent>

          {/* 任务管理标签页 */}
          <TabsContent value="tasks" className="space-y-6">
            <div className="text-center py-4">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {currentLanguage === 'zh' ? '任务管理' : 'Task Manager'}
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {currentLanguage === 'zh' 
                  ? '查看和管理所有转录任务的进行状态'
                  : 'View and manage the status of all transcription tasks'
                }
              </p>
            </div>

            <Card>
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <List className="w-8 h-8 text-gray-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {currentLanguage === 'zh' ? '暂无进行中的任务' : 'No active tasks'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {currentLanguage === 'zh' 
                    ? '开始文件转录或智能抓包后，任务将在这里显示'
                    : 'Tasks will appear here after starting file transcription or smart capture'
                  }
                </p>
                <Button 
                  onClick={() => setActiveTab('upload')}
                  className="mr-2"
                >
                  {currentLanguage === 'zh' ? '开始转录' : 'Start Transcription'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 历史记录标签页 */}
          <TabsContent value="history" className="space-y-6">
            <HistoryView language={currentLanguage} />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
