# VideoSense Frontend - 升级版

## 🎉 版本更新概览

此版本是基于v0原型设计的完整升级版本，融合了现代化UI设计和现有后端API集成。

## ✨ 新功能特性

### 🎨 全新UI设计
- **现代化界面**: 基于shadcn/ui组件库的精美设计
- **响应式布局**: 完美适配桌面和移动端
- **暗色主题支持**: 完整的明暗主题切换
- **国际化支持**: 中英文双语界面

### 📋 核心功能模块
1. **文件转录**: 
   - 拖拽上传音视频文件
   - 多格式支持 (MP4, MOV, MP3, WAV等)
   - 转录服务提供商选择
   - 语言检测和选择

2. **历史记录管理**:
   - 分页浏览转录历史
   - 搜索和筛选功能
   - 编辑和导出结果
   - 统计信息展示

3. **播放即录** (开发中):
   - 实时音频检测
   - 一键开始转录
   - 未来版本推出

4. **任务管理** (开发中):
   - 转录进度监控
   - 任务状态管理
   - 未来版本完善

## 🛠 技术栈

### 前端框架
- **Next.js 15**: React 19 + TypeScript
- **Tailwind CSS**: 样式系统
- **shadcn/ui**: 组件库
- **Radix UI**: 无障碍组件基础

### 开发工具
- **ESLint**: 代码规范
- **TypeScript**: 类型安全
- **Axios**: HTTP客户端

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
前端服务将在 http://localhost:3000 启动

### 生产构建
```bash
npm run build
npm start
```

## 📁 项目结构

```
frontend/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── globals.css     # 全局样式
│   │   ├── layout.tsx      # 根布局
│   │   └── page.tsx        # 主页面
│   ├── components/         # 组件目录
│   │   ├── ui/            # UI基础组件
│   │   ├── FileUpload.tsx  # 文件上传组件
│   │   ├── editor-view.tsx # 编辑器视图
│   │   └── task-detail-view.tsx # 任务详情
│   ├── hooks/             # 自定义Hooks
│   │   ├── use-mobile.tsx  # 移动端检测
│   │   └── use-toast.ts    # Toast通知
│   └── lib/               # 工具库
│       ├── utils.ts       # 工具函数
│       └── api.ts         # API接口
├── public/                # 静态资源
├── package.json           # 项目配置
└── tailwind.config.ts     # Tailwind配置
```

## 🔗 API集成

### 后端接口
- **基础URL**: `http://127.0.0.1:8000`
- **转录接口**: `POST /transcribe`
- **历史记录**: `GET /history`
- **健康检查**: `GET /health`

### 请求示例
```typescript
// 文件转录
const formData = new FormData()
formData.append('file', file)
formData.append('provider', 'mock')
formData.append('language', 'zh')

const response = await axios.post('/transcribe', formData, {
  headers: { 'Content-Type': 'multipart/form-data' }
})
```

## 🎯 使用指南

### 文件转录流程
1. 切换到"文件转录"标签页
2. 选择转录服务提供商和语言
3. 拖拽或选择音视频文件
4. 等待转录完成
5. 在历史记录中查看结果

### 历史记录管理
1. 切换到"历史记录"标签页
2. 浏览所有转录记录
3. 点击编辑按钮修改文本
4. 使用导出功能下载结果

## 🔧 配置说明

### 环境变量
在项目根目录创建 `.env.local` 文件：
```env
NEXT_PUBLIC_API_URL=http://127.0.0.1:8000
```

### 自定义主题
修改 `src/app/globals.css` 中的CSS变量来自定义主题颜色。

## 🚨 注意事项

1. **后端依赖**: 确保后端服务已启动在 http://127.0.0.1:8000
2. **文件大小**: 支持最大100MB的音视频文件
3. **浏览器兼容**: 建议使用Chrome、Safari、Firefox等现代浏览器

## 📝 开发日志

### v2.0.0 (2025-06-19)
- ✅ 完整迁移v0原型设计
- ✅ 集成shadcn/ui组件库
- ✅ 实现多标签页界面
- ✅ 添加国际化支持
- ✅ 完成历史记录功能
- ✅ 优化文件上传体验
- ✅ 修复所有构建错误

### 下一步计划
- 🔄 集成真实转录API
- 🔄 完善任务管理功能
- 🔄 添加播放即录功能
- 🔄 实现转录结果编辑器

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 发起 Pull Request

## 📞 支持

如有问题或建议，请查看项目文档或联系开发团队。
