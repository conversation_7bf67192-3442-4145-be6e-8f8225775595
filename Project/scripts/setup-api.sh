#!/bin/bash

# VideoSense API配置设置脚本
# 用于快速配置SiliconFlow API密钥

set -e

echo "🚀 VideoSense API配置设置"
echo "=========================="

# 检查是否在正确的目录
if [ ! -f "backend/config/api_keys.example.py" ]; then
    echo "❌ 错误：请在VideoSense项目根目录运行此脚本"
    exit 1
fi

# 检查是否已经配置
if [ -f "backend/config/api_keys.py" ]; then
    echo "⚠️  检测到已存在的API配置文件"
    read -p "是否要覆盖现有配置？(y/N): " overwrite
    if [[ $overwrite != "y" && $overwrite != "Y" ]]; then
        echo "操作已取消"
        exit 0
    fi
fi

# 复制示例配置文件
echo "📁 创建API配置文件..."
cp backend/config/api_keys.example.py backend/config/api_keys.py

# 获取用户输入
echo ""
echo "🔑 请输入SiliconFlow API配置信息："
echo "（您可以在 https://siliconflow.cn 控制台获取API密钥）"
echo ""

read -p "请输入SiliconFlow API密钥 (sk-...): " api_key

# 验证API密钥格式
if [[ ! $api_key =~ ^sk-.+ ]]; then
    echo "⚠️  警告：API密钥格式可能不正确（应以'sk-'开头）"
    read -p "是否继续？(y/N): " continue_setup
    if [[ $continue_setup != "y" && $continue_setup != "Y" ]]; then
        echo "操作已取消"
        exit 0
    fi
fi

# 更新配置文件
echo "⚙️  更新配置文件..."

# 使用sed替换API密钥
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' "s/your_siliconflow_api_key_here/$api_key/g" backend/config/api_keys.py
else
    # Linux
    sed -i "s/your_siliconflow_api_key_here/$api_key/g" backend/config/api_keys.py
fi

echo "✅ API配置完成！"
echo ""
echo "🔒 安全提示："
echo "- api_keys.py 已被添加到 .gitignore，不会被提交到Git"
echo "- 请勿在任何公开场合分享API密钥"
echo "- 建议定期更换API密钥"
echo ""
echo "🧪 验证配置："
echo "1. 启动后端服务：cd backend && uvicorn app.main:app --reload"
echo "2. 访问状态检查：http://localhost:8000/api/transcription/status"
echo "3. 验证API配置：POST http://localhost:8000/api/transcription/validate-config"
echo ""
echo "📖 详细文档：backend/config/README.md"
echo ""
echo "🎉 设置完成！VideoSense现在可以使用SiliconFlow进行语音转文本了。" 