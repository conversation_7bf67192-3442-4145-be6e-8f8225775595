const { shell } = require('electron')
const { execSync, spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

class PermissionManager {
  constructor() {
    this.permissionGranted = false
    this.certificateInstalled = false
    this.loadPermissionState()
  }

  // 加载权限状态
  loadPermissionState() {
    try {
      const configPath = path.join(__dirname, 'permission-state.json')
      if (fs.existsSync(configPath)) {
        const state = JSON.parse(fs.readFileSync(configPath, 'utf-8'))
        this.permissionGranted = state.permissionGranted || false
        this.certificateInstalled = state.certificateInstalled || false
        console.log('🔐 权限状态已加载:', {
          permissionGranted: this.permissionGranted,
          certificateInstalled: this.certificateInstalled
        })
      }
    } catch (error) {
      console.log('⚠️ 加载权限状态失败:', error)
    }
  }

  // 保存权限状态
  savePermissionState() {
    try {
      const configPath = path.join(__dirname, 'permission-state.json')
      fs.writeFileSync(configPath, JSON.stringify({
        permissionGranted: this.permissionGranted,
        certificateInstalled: this.certificateInstalled,
        lastUpdated: new Date().toISOString()
      }), 'utf-8')
      console.log('💾 权限状态已保存')
    } catch (error) {
      console.error('❌ 保存权限状态失败:', error)
    }
  }

  // 一键授权：设置系统代理权限
  async requestOneTimeAuthorization() {
    try {
      console.log('🔐 请求一次性代理设置权限...')
      
      // 检测网络接口名称（Wi-Fi 或 其他）
      let networkService = 'Wi-Fi'
      try {
        const services = execSync('networksetup -listallnetworkservices', { encoding: 'utf-8' })
        if (services.includes('Wi-Fi')) {
          networkService = 'Wi-Fi'
        } else if (services.includes('Ethernet')) {
          networkService = 'Ethernet'
        } else {
          // 获取第一个可用的网络服务
          const lines = services.split('\n').filter(line => line && !line.includes('*'))
          if (lines.length > 1) {
            networkService = lines[1].trim()
          }
        }
        console.log(`🌐 检测到网络服务: ${networkService}`)
      } catch (e) {
        console.log('⚠️ 网络服务检测失败，使用默认 Wi-Fi')
      }
      
      // 使用 AppleScript 请求管理员权限
      const script = `
        tell application "System Events"
          try
            display dialog "VideoSense 需要一次性授权以自动管理系统代理设置。

✅ 授权后的便利：
• 一键启动抓包，无需手动设置
• 自动配置和清理代理设置
• 类似知名工具的无感体验

❓ 这是做什么：
• 允许 VideoSense 自动设置系统代理为 127.0.0.1:8899
• 仅在抓包时临时使用，停止后自动恢复

🔒 隐私保护：
• 所有数据本地处理，不上传任何信息
• 权限仅用于代理设置，无其他系统访问" ¬
              with title "VideoSense - 一次性权限授权" ¬
              buttons {"稍后手动", "立即授权"} ¬
              default button "立即授权" ¬
              with icon note ¬
              giving up after 60
            
            if button returned of result is "立即授权" then
              -- 请求管理员权限并测试
              do shell script "networksetup -getwebproxy ${networkService}" with administrator privileges
              return "authorized"
            else
              return "cancelled"
            end if
          on error errMsg
            if errMsg contains "User canceled" then
              return "cancelled"
            else
              return "error:" & errMsg
            end if
          end try
        end tell
      `
      
      const result = execSync(`osascript -e '${script.replace(/\n\s*/g, ' ')}'`, { 
        encoding: 'utf-8',
        timeout: 65000 
      }).trim()
      
      console.log('🔐 权限请求结果:', result)
      
      if (result === 'authorized') {
        this.permissionGranted = true
        this.savePermissionState()
        
        return {
          success: true,
          message: '🎉 权限授权成功！现在可以一键启动抓包了。'
        }
      } else if (result === 'cancelled') {
        return {
          success: false,
          cancelled: true,
          message: '用户选择稍后手动设置，将提供手动配置指导。'
        }
      } else if (result.startsWith('error:')) {
        throw new Error(result.substring(6))
      } else {
        throw new Error('权限请求超时或失败')
      }
    } catch (error) {
      console.error('❌ 一键授权失败:', error)
      return {
        success: false,
        message: '授权过程中出现错误，将使用手动设置方式。',
        error: error.message
      }
    }
  }

  // 自动设置系统代理
  async setSystemProxy(enable = true, host = '127.0.0.1', port = 8899) {
    if (!this.permissionGranted) {
      console.log('❌ 未获得权限，无法自动设置代理')
      return {
        success: false,
        requiresAuthorization: true,
        message: '需要先进行一次性授权'
      }
    }

    try {
      console.log(`${enable ? '🔛' : '🔴'} ${enable ? '设置' : '清除'}系统代理...`)
      
      // 检测网络服务名称
      let networkService = 'Wi-Fi'
      try {
        const services = execSync('networksetup -listallnetworkservices', { encoding: 'utf-8' })
        if (services.includes('Wi-Fi')) {
          networkService = 'Wi-Fi'
        } else if (services.includes('Ethernet')) {
          networkService = 'Ethernet'
        } else {
          const lines = services.split('\n').filter(line => line && !line.includes('*'))
          if (lines.length > 1) {
            networkService = lines[1].trim()
          }
        }
      } catch (e) {
        console.log('⚠️ 使用默认网络服务 Wi-Fi')
      }
      
      if (enable) {
        // 设置代理
        console.log(`📡 为 ${networkService} 设置代理: ${host}:${port}`)
        
        execSync(`networksetup -setwebproxy "${networkService}" ${host} ${port}`, { 
          encoding: 'utf-8',
          timeout: 10000 
        })
        execSync(`networksetup -setsecurewebproxy "${networkService}" ${host} ${port}`, { 
          encoding: 'utf-8',
          timeout: 10000 
        })
        execSync(`networksetup -setwebproxystate "${networkService}" on`, { 
          encoding: 'utf-8',
          timeout: 10000 
        })
        execSync(`networksetup -setsecurewebproxystate "${networkService}" on`, { 
          encoding: 'utf-8',
          timeout: 10000 
        })
        
        console.log('✅ 系统代理设置成功')
      } else {
        // 清除代理
        console.log(`🧹 清除 ${networkService} 的代理设置`)
        
        execSync(`networksetup -setwebproxystate "${networkService}" off`, { 
          encoding: 'utf-8',
          timeout: 10000 
        })
        execSync(`networksetup -setsecurewebproxystate "${networkService}" off`, { 
          encoding: 'utf-8',
          timeout: 10000 
        })
        
        console.log('✅ 系统代理已清除')
      }
      
      return {
        success: true,
        message: enable ? '✅ 系统代理已自动设置' : '✅ 系统代理已自动清除',
        networkService: networkService
      }
    } catch (error) {
      console.error('❌ 设置系统代理失败:', error)
      return {
        success: false,
        message: '自动设置失败，请检查网络设置或手动配置代理',
        error: error.message
      }
    }
  }

  // 检查系统代理状态
  async checkSystemProxyStatus() {
    try {
      // 检测当前网络服务
      let networkService = 'Wi-Fi'
      try {
        const services = execSync('networksetup -listallnetworkservices', { encoding: 'utf-8' })
        if (services.includes('Wi-Fi')) {
          networkService = 'Wi-Fi'
        } else if (services.includes('Ethernet')) {
          networkService = 'Ethernet'
        } else {
          const lines = services.split('\n').filter(line => line && !line.includes('*'))
          if (lines.length > 1) {
            networkService = lines[1].trim()
          }
        }
      } catch (e) {
        console.log('⚠️ 网络服务检测失败')
      }
      
      const webProxy = execSync(`networksetup -getwebproxy "${networkService}"`, { encoding: 'utf-8' })
      const httpsProxy = execSync(`networksetup -getsecurewebproxy "${networkService}"`, { encoding: 'utf-8' })
      
      const isWebProxyEnabled = webProxy.includes('Enabled: Yes')
      const isHttpsProxyEnabled = httpsProxy.includes('Enabled: Yes')
      const isVideoSenseProxy = webProxy.includes('127.0.0.1') && webProxy.includes('8899')
      
      return {
        webProxyEnabled: isWebProxyEnabled,
        httpsProxyEnabled: isHttpsProxyEnabled,
        isVideoSenseProxy: isVideoSenseProxy,
        canAutoManage: this.permissionGranted,
        networkService: networkService
      }
    } catch (error) {
      console.error('❌ 检查代理状态失败:', error)
      return {
        webProxyEnabled: false,
        httpsProxyEnabled: false,
        isVideoSenseProxy: false,
        canAutoManage: false,
        networkService: 'Unknown'
      }
    }
  }

  // 获取权限状态
  getPermissionState() {
    return {
      permissionGranted: this.permissionGranted,
      certificateInstalled: this.certificateInstalled
    }
  }

  // 重置权限状态（用于测试或重新授权）
  resetPermissions() {
    this.permissionGranted = false
    this.certificateInstalled = false
    this.savePermissionState()
    console.log('🔄 权限状态已重置')
  }
}

module.exports = PermissionManager 