const { contextBridge, ipcRenderer } = require('electron')

// 安全地暴露API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // 文件操作
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  
  // 文件选择和拖拽事件监听
  onFileSelected: (callback) => {
    ipcRenderer.on('file-selected', (event, filePath) => callback(filePath))
  },
  
  onFileDropped: (callback) => {
    ipcRenderer.on('file-dropped', (event, filePath) => callback(filePath))
  },
  
  // 媒体捕捉相关API
  mediaCapture: {
    // 检测系统中的媒体源
    detectSources: () => ipcRenderer.invoke('detect-media-sources'),
    
    // 请求媒体访问权限
    requestPermissions: () => ipcRenderer.invoke('request-media-permissions'),
    
    // 开始捕捉指定媒体源
    startCapture: (sourceId, options) => ipcRenderer.invoke('start-media-capture', sourceId, options),
    
    // 停止媒体捕捉
    stopCapture: () => ipcRenderer.invoke('stop-media-capture'),
    
    // 获取当前捕捉状态
    getStatus: () => ipcRenderer.invoke('get-capture-status'),
    
    // 监听捕捉状态变化
    onStatusChange: (callback) => {
      ipcRenderer.on('capture-status-changed', (event, status) => callback(status))
    },
    
    // 监听媒体源变化
    onSourcesChange: (callback) => {
      ipcRenderer.on('media-sources-changed', (event, sources) => callback(sources))
    },
    
    // 移除监听器
    removeAllListeners: () => {
      ipcRenderer.removeAllListeners('capture-status-changed')
      ipcRenderer.removeAllListeners('media-sources-changed')
    }
  },
  
  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel)
  },
  
  // 平台信息
  platform: process.platform,
  
  // 是否为开发模式
  isDev: process.env.NODE_ENV === 'development'
})

// 暴露媒体流处理API
contextBridge.exposeInMainWorld('mediaAPI', {
  // 获取用户媒体流（用于音频捕捉）
  getUserMedia: async (constraints) => {
    try {
      return await navigator.mediaDevices.getUserMedia(constraints)
    } catch (error) {
      throw new Error('获取媒体流失败: ' + error.message)
    }
  },
  
  // 获取显示媒体流（用于屏幕/应用捕捉）
  getDisplayMedia: async (constraints) => {
    try {
      return await navigator.mediaDevices.getDisplayMedia(constraints)
    } catch (error) {
      throw new Error('获取显示媒体流失败: ' + error.message)
    }
  },
  
  // 枚举媒体设备
  enumerateDevices: async () => {
    try {
      return await navigator.mediaDevices.enumerateDevices()
    } catch (error) {
      throw new Error('枚举媒体设备失败: ' + error.message)
    }
  },
  
  // 音频处理工具
  audioUtils: {
    // 创建音频上下文
    createAudioContext: () => new (window.AudioContext || window.webkitAudioContext)(),
    
    // 创建媒体录制器
    createMediaRecorder: (stream, options) => new MediaRecorder(stream, options),
    
    // 支持的音频格式
    getSupportedMimeTypes: () => {
      const types = [
        'audio/webm;codecs=opus',
        'audio/webm',
        'audio/mp4;codecs=mp4a.40.2',
        'audio/mp4',
        'audio/wav',
        'audio/ogg;codecs=opus'
      ]
      return types.filter(type => MediaRecorder.isTypeSupported(type))
    }
  }
})

// 音频流处理管理
let currentAudioStream = null
let mediaRecorder = null
let audioContext = null

// 监听主进程的音频流请求
ipcRenderer.on('request-audio-stream', async (event, sourceId) => {
  try {
    console.log('Requesting audio stream for source:', sourceId)
    
    // 简化方案：直接请求用户选择屏幕/应用
    const stream = await navigator.mediaDevices.getDisplayMedia({
      audio: true,
      video: true  // 必须包含video，但可以后续处理
    })
    
    console.log('Got display media stream:', {
      audioTracks: stream.getAudioTracks().length,
      videoTracks: stream.getVideoTracks().length
    })
    
    // 检查是否有音频轨道
    const audioTracks = stream.getAudioTracks()
    if (audioTracks.length === 0) {
      throw new Error('所选媒体源不包含音频轨道，请选择包含音频的应用或窗口')
    }
    
    // 可选：停止视频轨道以节省资源（保留视频轨道可能有助于保持流稳定）
    const videoTracks = stream.getVideoTracks()
    videoTracks.forEach(track => {
      track.enabled = false  // 禁用而不是停止，保持流活跃
    })
    
    currentAudioStream = stream
    ipcRenderer.send('audio-stream-ready', sourceId)
    
  } catch (error) {
    console.error('Failed to get audio stream:', error)
    let errorMessage = error.message
    
    // 提供更友好的错误信息
    if (error.name === 'NotAllowedError') {
      errorMessage = '用户拒绝了屏幕共享权限'
    } else if (error.name === 'NotSupportedError') {
      errorMessage = '当前浏览器不支持屏幕音频捕捉'
    } else if (error.name === 'AbortError') {
      errorMessage = '用户取消了屏幕选择'
    }
    
    ipcRenderer.send('audio-stream-error', errorMessage)
  }
})

// 监听音频处理设置请求
ipcRenderer.on('setup-audio-processing', async (event, streamId) => {
  try {
    if (!currentAudioStream) {
      throw new Error('No audio stream available')
    }
    
    // 创建音频上下文
    audioContext = new (window.AudioContext || window.webkitAudioContext)()
    
    // 创建媒体录制器
    mediaRecorder = new MediaRecorder(currentAudioStream, {
      mimeType: 'audio/webm;codecs=opus'
    })
    
    // 设置音频数据处理
    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        // 将音频数据发送到主进程
        const reader = new FileReader()
        reader.onload = () => {
          const arrayBuffer = reader.result
          ipcRenderer.send('audio-data', new Uint8Array(arrayBuffer))
        }
        reader.readAsArrayBuffer(event.data)
      }
    }
    
    mediaRecorder.onerror = (error) => {
      console.error('MediaRecorder error:', error)
    }
    
    // 开始录制（每1秒发送一次数据）
    mediaRecorder.start(1000)
    
    console.log('Audio processing started')
    
  } catch (error) {
    console.error('Failed to setup audio processing:', error)
  }
})

// 监听停止捕捉
ipcRenderer.on('stop-audio-capture', () => {
  if (mediaRecorder && mediaRecorder.state === 'recording') {
    mediaRecorder.stop()
  }
  
  if (currentAudioStream) {
    currentAudioStream.getTracks().forEach(track => track.stop())
    currentAudioStream = null
  }
  
  if (audioContext) {
    audioContext.close()
    audioContext = null
  }
  
  mediaRecorder = null
  console.log('Audio capture stopped')
})

// 暴露音频流状态API
contextBridge.exposeInMainWorld('audioStreamAPI', {
  // 获取音频流状态
  getStreamStatus: () => {
    return {
      hasStream: !!currentAudioStream,
      isRecording: mediaRecorder && mediaRecorder.state === 'recording',
      audioContextState: audioContext ? audioContext.state : 'closed'
    }
  },
  
  // 监听转录结果
  onTranscriptionResult: (callback) => {
    ipcRenderer.on('transcription-result', (event, result) => callback(result))
  },
  
  // 监听捕捉状态变化
  onCaptureStatusChange: (callback) => {
    ipcRenderer.on('capture-status-changed', (event, status) => callback(status))
  },
  
  // 移除监听器
  removeAllListeners: () => {
    ipcRenderer.removeAllListeners('transcription-result')
    ipcRenderer.removeAllListeners('capture-status-changed')
  }
})

// 暴露权限管理API给渲染进程
contextBridge.exposeInMainWorld('permissionAPI', {
  // 请求一次性授权
  requestAuthorization: () => ipcRenderer.invoke('request-authorization'),
  
  // 系统代理管理
  setSystemProxy: (enable, host, port) => ipcRenderer.invoke('set-system-proxy', enable, host, port),
  
  checkProxyStatus: () => ipcRenderer.invoke('check-proxy-status'),
  
  // 权限状态管理
  getPermissionState: () => ipcRenderer.invoke('get-permission-state'),
  
  resetPermissions: () => ipcRenderer.invoke('reset-permissions'),
  
  // 证书管理功能
  installCertificate: () => ipcRenderer.invoke('install-certificate'),
  
  checkCertificateStatus: () => ipcRenderer.invoke('check-certificate-status')
})

// 阻止右键菜单（可选）
document.addEventListener('contextmenu', (e) => {
  // 在生产环境中阻止右键菜单
  if (process.env.NODE_ENV !== 'development') {
    e.preventDefault()
  }
})

// 阻止拖拽文件到页面导致页面跳转
document.addEventListener('dragover', (e) => {
  e.preventDefault()
})

document.addEventListener('drop', (e) => {
  e.preventDefault()
  
  // 处理拖拽的文件
  const files = Array.from(e.dataTransfer.files)
  if (files.length > 0) {
    const file = files[0]
    // 检查文件类型
    const allowedTypes = [
      'audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/x-m4a',
      'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska'
    ]
    
    const allowedExtensions = [
      '.mp3', '.wav', '.m4a', '.mp4', '.mov', '.avi', '.mkv'
    ]
    
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    
    if (allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension)) {
      // 通知主进程有文件被拖拽
      window.postMessage({
        type: 'file-dropped',
        filePath: file.path || file.name,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      }, '*')
    } else {
      // 显示错误提示
      window.postMessage({
        type: 'file-error',
        message: '不支持的文件格式，请选择音频或视频文件'
      }, '*')
    }
  }
}) 