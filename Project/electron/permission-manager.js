const { shell } = require('electron')
const { execSync, spawn } = require('child_process')
const path = require('path')
const fs = require('fs')
const os = require('os')
const https = require('https')

/**
 * 跨平台权限管理器
 * 负责处理系统代理设置和mitmproxy根证书安装
 * 支持 macOS、Windows 和 Linux 操作系统
 */
class PermissionManager {
  constructor() {
    this.permissionGranted = false
    this.certificateInstalled = false
    this.platform = process.platform // 'darwin', 'win32', 'linux'
    this.loadPermissionState()
    
    console.log(`🖥️ 检测到操作系统: ${this.getPlatformName()}`)
  }

  /**
   * 获取用户友好的平台名称
   */
  getPlatformName() {
    switch (this.platform) {
      case 'darwin': return 'macOS'
      case 'win32': return 'Windows'
      case 'linux': return 'Linux'
      default: return this.platform
    }
  }

  /**
   * 检查当前平台是否支持
   */
  isPlatformSupported() {
    return ['darwin', 'win32', 'linux'].includes(this.platform)
  }

  // 加载权限状态
  loadPermissionState() {
    try {
      const configPath = path.join(__dirname, 'permission-state.json')
      if (fs.existsSync(configPath)) {
        const state = JSON.parse(fs.readFileSync(configPath, 'utf-8'))
        this.permissionGranted = state.permissionGranted || false
        this.certificateInstalled = state.certificateInstalled || false
        console.log('🔐 权限状态已加载:', {
          permissionGranted: this.permissionGranted,
          certificateInstalled: this.certificateInstalled,
          platform: this.getPlatformName()
        })
      }
    } catch (error) {
      console.log('⚠️ 加载权限状态失败:', error)
    }
  }

  // 保存权限状态
  savePermissionState() {
    try {
      const configPath = path.join(__dirname, 'permission-state.json')
      fs.writeFileSync(configPath, JSON.stringify({
        permissionGranted: this.permissionGranted,
        certificateInstalled: this.certificateInstalled,
        platform: this.platform,
        lastUpdated: new Date().toISOString()
      }), 'utf-8')
      console.log('💾 权限状态已保存')
    } catch (error) {
      console.error('❌ 保存权限状态失败:', error)
    }
  }

  /**
   * 从后端API获取mitmproxy证书内容
   */
  async fetchCertificateFromBackend(backendUrl = 'http://localhost:8000') {
    return new Promise((resolve, reject) => {
      const url = `${backendUrl}/api/proxy/certificate`
      const protocol = url.startsWith('https:') ? https : require('http')
      
      protocol.get(url, (res) => {
        let data = ''
        res.on('data', chunk => data += chunk)
        res.on('end', () => {
          try {
            const result = JSON.parse(data)
            if (result.success && result.certificate) {
              resolve(result.certificate)
            } else {
              reject(new Error(result.error || '获取证书失败'))
            }
          } catch (e) {
            reject(new Error('解析证书响应失败'))
          }
        })
      }).on('error', (err) => {
        // 网络错误，抛出异常让上层处理
        reject(new Error('获取证书失败'))
      })
    })
  }

  /**
   * 获取内置证书（备用方案）
   */
  getBuiltinCertificate() {
    return `-----BEGIN CERTIFICATE-----
MIIDwzCCAqugAwIBAgIUFAnC6268dp/z1DR9E1UepiWgWzkwDQYJKoZIhvcNAQEL
BQAwcDELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUNob25ncWluZzESMBAGA1UEBwwJ
Q2hvbmdxaW5nMQ4wDAYDVQQKDAVnb3dhczEWMBQGA1UECwwNSVQgRGVwYXJ0bWVu
dDERMA8GA1UEAwwIZ293YXMuY24wIBcNMjQwMjE4MDIwOTI2WhgPMjEyNDAxMjUw
MjA5MjZaMHAxCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAlDaG9uZ3FpbmcxEjAQBgNV
BAcMCUNob25ncWluZzEOMAwGA1UECgwFZ293YXMxFjAUBgNVBAsMDUlUIERlcGFy
dG1lbnQxETAPBgNVBAMMCGdvd2FzLmNuMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A
MIIBCgKCAQEA3A7dt7eoqAaBxv2Npjo8Z7VkGvXT93jZfpgAuuNuQ5RLcnOnMzQC
CrrjPcLfsAMA0AIK3eUWsXXKSR9SZTJBLQRZCJHZ9AIPfA+58JVQPTjd8UIuQZJf
rDf6FjhPJTsLzcjTU+mT7t6lEimPEl2VWN9eXWqs9nkVrJtqLao6m1hoYfXOxRh6
96/WgBtPHcmjujryteBiSITVflDjx+YQzDGsbqw7fM52klMPd2+w/vmhJ4pxq6P7
Ni2OBvdXYDPIuLfPFFqG16arORjBkyNCJy19iOuh5LXh+EUX11wvbLwNgsTd8j9v
eBSD+4HUUNQhiXiXJbs7I7cdFYthvb609QIDAQABo1MwUTAdBgNVHQ4EFgQUdI8p
aY1A47rWCRvQKSTRCCk6FoMwHwYDVR0jBBgwFoAUdI8paY1A47rWCRvQKSTRCCk6
FoMwDwYDVR0TAQH/BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEArMCAfqidgXL7
cW5TAZTCqnUeKzbbqMJgk6iFsma8scMRsUXz9ZhF0UVf98376KvoJpy4vd81afbi
TehQ8wVBuKTtkHeh/MkXMWC/FU4HqSjtvxpic2+Or5dMjIrfa5VYPgzfqNaBIUh4
InD5lo8b/n5V+jdwX7RX9VYAKug6QZlCg5YSKIvgNRChb36JmrGcvsp5R0Vejnii
e3oowvgwikqm6XR6BEcRpPkztqcKST7jPFGHiXWsAqiibc+/plMW9qebhfMXEGhQ
5yVNeSxX2zqasZvP/fRy+3I5iVilxtKvJuVpPZ0UZzGS0CJ/lF67ntibktiPa3sR
D8HixYbEDg==
-----END CERTIFICATE-----`
  }

  /**
   * 跨平台证书安装功能
   * 支持 macOS、Windows 和 Linux
   */
  async installCertificate() {
    if (!this.isPlatformSupported()) {
      return {
        success: false,
        error: `不支持的操作系统: ${this.getPlatformName()}`
      }
    }

    try {
      console.log(`📜 开始在 ${this.getPlatformName()} 上安装证书...`)
      
      // 1. 获取证书内容
      let certificateContent
      try {
        // 优先从后端API获取证书
        certificateContent = await this.fetchCertificateFromBackend()
        console.log('✅ 从后端API获取证书成功')
      } catch (backendError) {
        console.log('⚠️ 从后端API获取证书失败，使用内置证书')
        console.log(`后端错误: ${backendError.message}`)
        certificateContent = this.getBuiltinCertificate()
        console.log('✅ 使用内置证书')
      }
      
      // 2. 创建临时证书文件
      const tempDir = os.tmpdir()
      const certFileName = `videosense-cert-${Date.now()}.crt`
      const certFilePath = path.join(tempDir, certFileName)
      
      fs.writeFileSync(certFilePath, certificateContent, 'utf-8')
      console.log(`📁 证书文件已创建: ${certFilePath}`)
      
      let result
      try {
        // 3. 根据平台执行相应的安装命令
        switch (this.platform) {
          case 'darwin':
            result = await this.installCertificateMacOS(certFilePath)
            break
          case 'win32':
            result = await this.installCertificateWindows(certFilePath)
            break
          case 'linux':
            result = await this.installCertificateLinux(certFilePath)
            break
          default:
            throw new Error(`不支持的平台: ${this.platform}`)
        }
        
        if (result.success) {
          this.certificateInstalled = true
          this.savePermissionState()
          console.log('✅ 证书安装成功')
        }
        
        return result
      } finally {
        // 4. 清理临时文件
        try {
          if (fs.existsSync(certFilePath)) {
            fs.unlinkSync(certFilePath)
            console.log('🧹 临时证书文件已清理')
          }
        } catch (cleanupError) {
          console.warn('⚠️ 清理临时文件失败:', cleanupError)
        }
      }
    } catch (error) {
      console.error('❌ 证书安装失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * macOS 证书安装
   */
  async installCertificateMacOS(certFilePath) {
    try {
      const script = `
        try
          do shell script "security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain '${certFilePath}'" with administrator privileges
          return "success"
        on error errMsg
          return "error:" & errMsg
        end try
      `
      
      const result = execSync(`osascript`, {
        input: script,
        encoding: 'utf-8',
        timeout: 30000
      }).trim()
      
      if (result.includes('success')) {
        return {
          success: true,
          message: '✅ macOS 证书已成功安装到系统钥匙串'
        }
      } else if (result.startsWith('error:')) {
        const errorMsg = result.substring(6)
        if (errorMsg.includes('User canceled')) {
          return {
            success: false,
            cancelled: true,
            message: '用户取消了证书安装'
          }
        } else {
          throw new Error(errorMsg)
        }
      } else {
        throw new Error('证书安装失败：未知结果')
      }
    } catch (error) {
      return {
        success: false,
        error: `macOS 证书安装失败: ${error.message}`
      }
    }
  }

  /**
   * Windows 证书安装
   */
  async installCertificateWindows(certFilePath) {
    try {
      // 使用 PowerShell 以管理员权限执行 certutil 命令
      const psScript = `
        Start-Process -FilePath "certutil" -ArgumentList "-addstore", "-f", "Root", "${certFilePath.replace(/\\/g, '\\\\')}" -Verb RunAs -Wait -WindowStyle Hidden
        if ($LASTEXITCODE -eq 0) { Write-Output "success" } else { Write-Output "error:certutil failed with exit code $LASTEXITCODE" }
      `
      
      const result = execSync(`powershell -Command "${psScript}"`, {
        encoding: 'utf-8',
        timeout: 30000
      }).trim()
      
      if (result === 'success') {
        return {
          success: true,
          message: '✅ Windows 证书已成功安装到根证书存储'
        }
      } else {
        throw new Error(result.replace('error:', ''))
      }
    } catch (error) {
      return {
        success: false,
        error: `Windows 证书安装失败: ${error.message}`
      }
    }
  }

  /**
   * Linux 证书安装
   */
  async installCertificateLinux(certFilePath) {
    try {
      // 使用 pkexec 以管理员权限复制证书并更新证书存储
      const targetPath = '/usr/local/share/ca-certificates/videosense.crt'
      
      // 先尝试复制证书文件
      const copyResult = execSync(`pkexec cp "${certFilePath}" "${targetPath}"`, {
        encoding: 'utf-8',
        timeout: 30000
      })
      
      // 然后更新证书存储
      const updateResult = execSync('pkexec update-ca-certificates', {
        encoding: 'utf-8',
        timeout: 30000
      })
      
      return {
        success: true,
        message: '✅ Linux 证书已成功安装到系统证书存储'
      }
    } catch (error) {
      // 如果 pkexec 不可用，尝试其他方法
      try {
        console.log('⚠️ pkexec 不可用，尝试使用 gksudo...')
        const targetPath = '/usr/local/share/ca-certificates/videosense.crt'
        
        execSync(`gksudo "cp '${certFilePath}' '${targetPath}'"`, {
          encoding: 'utf-8',
          timeout: 30000
        })
        
        execSync('gksudo update-ca-certificates', {
          encoding: 'utf-8',
          timeout: 30000
        })
        
        return {
          success: true,
          message: '✅ Linux 证书已成功安装到系统证书存储'
        }
      } catch (fallbackError) {
        return {
          success: false,
          error: `Linux 证书安装失败: ${error.message}. 请确保系统安装了 pkexec 或 gksudo.`
        }
      }
    }
  }

  /**
   * 检查证书安装状态
   */
  async checkCertificateStatus() {
    try {
      let isInstalled = false
      
      switch (this.platform) {
        case 'darwin':
          try {
            // 直接查找gowas.cn证书，这是实际的证书名称
            const result = execSync('security find-certificate -c "gowas.cn" /Library/Keychains/System.keychain', {
              encoding: 'utf-8'
            })
            isInstalled = result.includes('gowas.cn')
            console.log('✅ 找到 gowas.cn 证书')
          } catch (e) {
            // 如果找不到gowas.cn，尝试查找其他可能的名称
            try {
              const result2 = execSync('security find-certificate -c "mitmproxy" /Library/Keychains/System.keychain', {
                encoding: 'utf-8'
              })
              isInstalled = result2.includes('mitmproxy')
              console.log('✅ 找到 mitmproxy 证书')
            } catch (e2) {
              console.log('⚠️ 未找到证书')
              isInstalled = false
            }
          }
          break
          
        case 'win32':
          try {
            const result = execSync('certutil -store root | findstr /i videosense', {
              encoding: 'utf-8'
            })
            isInstalled = result.length > 0
          } catch (e) {
            isInstalled = false
          }
          break
          
        case 'linux':
          try {
            isInstalled = fs.existsSync('/usr/local/share/ca-certificates/videosense.crt')
          } catch (e) {
            isInstalled = false
          }
          break
      }
      
      this.certificateInstalled = isInstalled
      this.savePermissionState()
      
      return {
        installed: isInstalled,
        platform: this.getPlatformName()
      }
    } catch (error) {
      console.error('❌ 检查证书状态失败:', error)
      return {
        installed: false,
        platform: this.getPlatformName(),
        error: error.message
      }
    }
  }

  // 一键授权：设置系统代理权限（简化版）
  async requestOneTimeAuthorization() {
    if (!this.isPlatformSupported()) {
      return {
        success: false,
        error: `不支持的操作系统: ${this.getPlatformName()}`
      }
    }

    try {
      console.log(`🔐 在 ${this.getPlatformName()} 上请求一次性代理设置权限...`)
      
      // 直接尝试设置代理来测试权限
      const testResult = await this.setSystemProxy(true, '127.0.0.1', 8899)
      
      if (testResult.success) {
        this.permissionGranted = true
        this.savePermissionState()
        
        // 立即清除测试代理
        await this.setSystemProxy(false)
        
        return {
          success: true,
          message: `🎉 ${this.getPlatformName()} 权限授权成功！现在可以一键启动抓包了。`
        }
      } else if (testResult.cancelled) {
        return {
          success: false,
          cancelled: true,
          message: '用户取消了权限授权，将提供手动配置指导。'
        }
      } else {
        return {
          success: false,
          message: '授权过程中出现错误，将使用手动设置方式。',
          error: testResult.error
        }
      }
    } catch (error) {
      console.error('❌ 一键授权失败:', error)
      return {
        success: false,
        message: '授权过程中出现错误，将使用手动设置方式。',
        error: error.message
      }
    }
  }

  /**
   * 跨平台系统代理设置
   */
  async setSystemProxy(enable = true, host = '127.0.0.1', port = 8899) {
    if (!this.isPlatformSupported()) {
      return {
        success: false,
        error: `不支持的操作系统: ${this.getPlatformName()}`
      }
    }

    try {
      console.log(`${enable ? '🔛' : '🔴'} 在 ${this.getPlatformName()} 上${enable ? '设置' : '清除'}系统代理...`)
      
      let result
      switch (this.platform) {
        case 'darwin':
          result = await this.setSystemProxyMacOS(enable, host, port)
          break
        case 'win32':
          result = await this.setSystemProxyWindows(enable, host, port)
          break
                 case 'linux':
           result = await this.setSystemProxyLinux(enable, host, port)
           break
        default:
          throw new Error(`不支持的平台: ${this.platform}`)
      }
      
      return result
    } catch (error) {
      console.error('❌ 设置系统代理失败:', error)
      return {
        success: false,
        message: '自动设置失败，请检查网络设置或手动配置代理',
        error: error.message,
        requiresAuthorization: true
      }
    }
  }

  /**
   * macOS 系统代理设置（保留原有逻辑）
   */
  async setSystemProxyMacOS(enable, host, port) {
    // 检测网络服务名称
    let networkService = 'Wi-Fi'
    try {
      const services = execSync('networksetup -listallnetworkservices', { encoding: 'utf-8' })
      if (services.includes('Wi-Fi')) {
        networkService = 'Wi-Fi'
      } else if (services.includes('Ethernet')) {
        networkService = 'Ethernet'
      } else {
        const lines = services.split('\n').filter(line => line && !line.includes('*'))
        if (lines.length > 1) {
          networkService = lines[1].trim()
        }
      }
    } catch (e) {
      console.log('⚠️ 使用默认网络服务 Wi-Fi')
    }
    
    // 直接尝试命令，如果失败再用AppleScript
    try {
      if (enable) {
        // 尝试直接设置代理
        execSync(`networksetup -setwebproxy '${networkService}' ${host} ${port}`, { timeout: 10000, stdio: 'pipe' })
        execSync(`networksetup -setsecurewebproxy '${networkService}' ${host} ${port}`, { timeout: 10000, stdio: 'pipe' })
        execSync(`networksetup -setwebproxystate '${networkService}' on`, { timeout: 10000, stdio: 'pipe' })
        execSync(`networksetup -setsecurewebproxystate '${networkService}' on`, { timeout: 10000, stdio: 'pipe' })
      } else {
        // 清除代理
        execSync(`networksetup -setwebproxystate '${networkService}' off`, { timeout: 10000, stdio: 'pipe' })
        execSync(`networksetup -setsecurewebproxystate '${networkService}' off`, { timeout: 10000, stdio: 'pipe' })
      }
      
      console.log(`✅ macOS系统代理${enable ? '设置' : '清除'}成功 (直接方式)`)
      return {
        success: true,
        message: enable ? '✅ macOS系统代理已自动设置' : '✅ macOS系统代理已自动清除',
        networkService: networkService
      }
    } catch (directError) {
      console.log('⚠️ 直接设置失败，尝试使用权限提升...')
      
      try {
        // 如果直接命令失败，使用简化的AppleScript
        const script = enable 
          ? `do shell script "networksetup -setwebproxy '${networkService}' ${host} ${port} && networksetup -setsecurewebproxy '${networkService}' ${host} ${port} && networksetup -setwebproxystate '${networkService}' on && networksetup -setsecurewebproxystate '${networkService}' on" with administrator privileges`
          : `do shell script "networksetup -setwebproxystate '${networkService}' off && networksetup -setsecurewebproxystate '${networkService}' off" with administrator privileges`
        
        const result = execSync(`osascript -e '${script}'`, { 
          encoding: 'utf-8',
          timeout: 30000,
          shell: true
        }).trim()
    
        console.log('🔐 macOS代理设置结果:', result)
    
        // AppleScript成功执行就认为成功
        console.log(`✅ macOS系统代理${enable ? '设置' : '清除'}成功 (权限提升)`)
        return {
          success: true,
          message: enable ? '✅ macOS系统代理已自动设置' : '✅ macOS系统代理已自动清除',
          networkService: networkService
        }
      } catch (appleScriptError) {
        console.log('❌ AppleScript也失败了:', appleScriptError.message)
        
        if (appleScriptError.message.includes('User canceled') || appleScriptError.message.includes('用户取消')) {
          return {
            success: false,
            cancelled: true,
            message: '用户取消了权限授权',
            error: appleScriptError.message
          }
        } else {
          return {
            success: false,
            message: '自动设置失败，请检查网络设置或手动配置代理',
            error: appleScriptError.message,
            requiresAuthorization: true
          }
        }
      }
    }
  }

  /**
   * Windows 系统代理设置
   */
  async setSystemProxyWindows(enable, host, port) {
    try {
      if (enable) {
        // 设置代理
        const psScript = `
          $regPath = "HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings"
          Set-ItemProperty -Path $regPath -Name "ProxyServer" -Value "${host}:${port}"
          Set-ItemProperty -Path $regPath -Name "ProxyEnable" -Value 1
          Write-Output "success"
        `
        
        const result = execSync(`powershell -Command "${psScript}"`, {
          encoding: 'utf-8',
          timeout: 30000
        }).trim()
        
        if (result === 'success') {
          return {
            success: true,
            message: `✅ Windows系统代理已设置为 ${host}:${port}`
          }
        } else {
          throw new Error('Windows代理设置失败')
        }
      } else {
        // 清除代理
        const psScript = `
          $regPath = "HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings"
          Set-ItemProperty -Path $regPath -Name "ProxyEnable" -Value 0
          Write-Output "success"
        `
        
        const result = execSync(`powershell -Command "${psScript}"`, {
          encoding: 'utf-8',
          timeout: 30000
        }).trim()
        
        if (result === 'success') {
          return {
            success: true,
            message: '✅ Windows系统代理已清除'
          }
        } else {
          throw new Error('Windows代理清除失败')
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Windows代理设置失败: ${error.message}`
      }
    }
  }

  /**
   * Linux 系统代理设置
   */
  async setSystemProxyLinux(enable, host, port) {
    try {
      if (enable) {
        // 设置代理
        const proxyUrl = `http://${host}:${port}`
        
        // 使用 gsettings 设置 GNOME 代理
        try {
          execSync(`gsettings set org.gnome.system.proxy.http host "${host}"`, { timeout: 10000 })
          execSync(`gsettings set org.gnome.system.proxy.http port ${port}`, { timeout: 10000 })
          execSync(`gsettings set org.gnome.system.proxy.https host "${host}"`, { timeout: 10000 })
          execSync(`gsettings set org.gnome.system.proxy.https port ${port}`, { timeout: 10000 })
          execSync('gsettings set org.gnome.system.proxy mode "manual"', { timeout: 10000 })
          
          return {
            success: true,
            message: `✅ Linux系统代理已设置为 ${host}:${port}`
          }
        } catch (gsettingsError) {
          console.log('⚠️ gsettings 不可用，使用环境变量方式')
          
          // 备用方案：提示用户手动设置环境变量
          return {
            success: true,
            message: `✅ Linux代理配置已准备，请在终端中执行以下命令：\nexport http_proxy=${proxyUrl}\nexport https_proxy=${proxyUrl}`,
            manual: true
          }
        }
      } else {
        // 清除代理
        try {
          execSync('gsettings set org.gnome.system.proxy mode "none"', { timeout: 10000 })
          
          return {
            success: true,
            message: '✅ Linux系统代理已清除'
          }
        } catch (gsettingsError) {
          return {
            success: true,
            message: '✅ Linux代理已清除，如果之前使用环境变量，请在终端中执行：\nunset http_proxy\nunset https_proxy',
            manual: true
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Linux代理设置失败: ${error.message}`
      }
    }
  }

  /**
   * 检查系统代理状态（跨平台）
   */
  async checkSystemProxyStatus() {
    try {
      let status = {
        webProxyEnabled: false,
        httpsProxyEnabled: false,
        isVideoSenseProxy: false,
        canAutoManage: this.permissionGranted,
        platform: this.getPlatformName()
      }
      
      switch (this.platform) {
        case 'darwin':
          status = { ...status, ...(await this.checkSystemProxyStatusMacOS()) }
          break
        case 'win32':
          status = { ...status, ...(await this.checkSystemProxyStatusWindows()) }
          break
        case 'linux':
          status = { ...status, ...(await this.checkSystemProxyStatusLinux()) }
          break
        default:
          status.error = `不支持的平台: ${this.platform}`
      }
      
      return status
    } catch (error) {
      console.error('❌ 检查代理状态失败:', error)
      return {
        webProxyEnabled: false,
        httpsProxyEnabled: false,
        isVideoSenseProxy: false,
        canAutoManage: false,
        platform: this.getPlatformName(),
        error: error.message
      }
    }
  }

  async checkSystemProxyStatusMacOS() {
    // 检测当前网络服务
    let networkService = 'Wi-Fi'
    try {
      const services = execSync('networksetup -listallnetworkservices', { encoding: 'utf-8' })
      if (services.includes('Wi-Fi')) {
        networkService = 'Wi-Fi'
      } else if (services.includes('Ethernet')) {
        networkService = 'Ethernet'
      } else {
        const lines = services.split('\n').filter(line => line && !line.includes('*'))
        if (lines.length > 1) {
          networkService = lines[1].trim()
        }
      }
    } catch (e) {
      console.log('⚠️ 网络服务检测失败')
    }
    
    const webProxy = execSync(`networksetup -getwebproxy "${networkService}"`, { encoding: 'utf-8' })
    const httpsProxy = execSync(`networksetup -getsecurewebproxy "${networkService}"`, { encoding: 'utf-8' })
    
    const isWebProxyEnabled = webProxy.includes('Enabled: Yes')
    const isHttpsProxyEnabled = httpsProxy.includes('Enabled: Yes')
    const isVideoSenseProxy = webProxy.includes('127.0.0.1') && webProxy.includes('8899')
    
    return {
      webProxyEnabled: isWebProxyEnabled,
      httpsProxyEnabled: isHttpsProxyEnabled,
      isVideoSenseProxy: isVideoSenseProxy,
      networkService: networkService
    }
  }

  async checkSystemProxyStatusWindows() {
    try {
      const psScript = `
        $regPath = "HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings"
        $proxyEnable = Get-ItemProperty -Path $regPath -Name "ProxyEnable" -ErrorAction SilentlyContinue
        $proxyServer = Get-ItemProperty -Path $regPath -Name "ProxyServer" -ErrorAction SilentlyContinue
        
        $enabled = if ($proxyEnable) { $proxyEnable.ProxyEnable -eq 1 } else { $false }
        $server = if ($proxyServer) { $proxyServer.ProxyServer } else { "" }
        $isVideoSense = $server -like "*127.0.0.1:8899*"
        
        Write-Output "$enabled|$server|$isVideoSense"
      `
      
      const result = execSync(`powershell -Command "${psScript}"`, {
        encoding: 'utf-8',
        timeout: 10000
      }).trim()
      
      const [enabled, server, isVideoSense] = result.split('|')
      
      return {
        webProxyEnabled: enabled === 'True',
        httpsProxyEnabled: enabled === 'True',
        isVideoSenseProxy: isVideoSense === 'True',
        proxyServer: server
      }
    } catch (error) {
      return {
        webProxyEnabled: false,
        httpsProxyEnabled: false,
        isVideoSenseProxy: false,
        error: error.message
      }
    }
  }

  async checkSystemProxyStatusLinux() {
    try {
      let gnomeProxyEnabled = false
      let proxyMode = ''
      
      try {
        proxyMode = execSync('gsettings get org.gnome.system.proxy mode', {
          encoding: 'utf-8',
          timeout: 5000
        }).trim().replace(/'/g, '')
        gnomeProxyEnabled = proxyMode === 'manual'
      } catch (e) {
        // gsettings 不可用
      }
      
      // 检查环境变量
      const envProxyEnabled = process.env.http_proxy || process.env.HTTP_PROXY || false
      const isVideoSenseProxy = envProxyEnabled && envProxyEnabled.includes('127.0.0.1:8899')
      
      return {
        webProxyEnabled: gnomeProxyEnabled || !!envProxyEnabled,
        httpsProxyEnabled: gnomeProxyEnabled || !!envProxyEnabled,
        isVideoSenseProxy: isVideoSenseProxy,
        proxyMode: proxyMode,
        envProxy: envProxyEnabled
      }
    } catch (error) {
      return {
        webProxyEnabled: false,
        httpsProxyEnabled: false,
        isVideoSenseProxy: false,
        error: error.message
      }
    }
  }

  // 获取权限状态
  getPermissionState() {
    return {
      permissionGranted: this.permissionGranted,
      certificateInstalled: this.certificateInstalled,
      platform: this.getPlatformName(),
      platformSupported: this.isPlatformSupported()
    }
  }

  // 重置权限状态（用于测试或重新授权）
  resetPermissions() {
    this.permissionGranted = false
    this.certificateInstalled = false
    this.savePermissionState()
    console.log('🔄 权限状态已重置')
    
    return {
      success: true,
      message: '权限状态已重置，请重新进行授权'
    }
  }
}

module.exports = PermissionManager
