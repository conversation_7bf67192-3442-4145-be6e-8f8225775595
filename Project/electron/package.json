{"name": "videosense-electron", "version": "1.0.0", "description": "VideoSense AI音视频转录工具 - 桌面客户端", "main": "main.js", "author": "VideoSense Team", "license": "MIT", "private": true, "homepage": "./", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "debug": "DEBUG=true NODE_ENV=development electron .", "build": "npm run build:frontend && npm run build:backend && npm run build:electron", "build:frontend": "cd ../frontend && npm run build", "build:backend": "echo 'Backend build not needed - Python runtime'", "build:electron": "electron-builder", "build:mac": "electron-builder --mac", "build:win": "electron-builder --win", "build:linux": "electron-builder --linux", "dist": "npm run build && electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "devDependencies": {"electron": "^28.3.3", "electron-builder": "^24.13.3"}, "build": {"appId": "com.videosense.app", "productName": "VideoSense", "copyright": "Copyright © 2025 VideoSense Team", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["main.js", "preload.js", "assets/**/*", "../frontend/out/**/*", "../backend/**/*", "!../backend/venv/**/*", "!../backend/__pycache__/**/*", "!../backend/*.log", "!../backend/test_*", "!../backend/create_test_*"], "extraResources": [{"from": "../backend", "to": "backend", "filter": ["**/*", "!venv/**/*", "!__pycache__/**/*", "!*.log", "!test_*", "!create_test_*"]}], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist"}, "dmg": {"title": "VideoSense ${version}", "icon": "assets/icon.icns", "background": "assets/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "publisherName": "VideoSense Team"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "VideoSense"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "AudioVideo", "synopsis": "AI音视频转录工具", "description": "基于人工智能技术的专业转录软件，支持多种音视频格式，提供高精度的文字转换服务。"}, "publish": {"provider": "generic", "url": "https://releases.videosense.com/"}, "compression": "maximum", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false}, "engines": {"node": ">=16.0.0"}}