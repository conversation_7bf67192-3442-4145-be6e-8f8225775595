const { app, <PERSON><PERSON><PERSON><PERSON>indow, <PERSON>u, dialog, shell, ipcMain, desktopCapturer, systemPreferences } = require('electron')
const path = require('path')
const { spawn } = require('child_process')
const fs = require('fs')
const http = require('http')
const PermissionManager = require('./permission-manager')

// 全局变量
let mainWindow = null
let backendProcess = null
let permissionManager = null
const isDev = process.env.NODE_ENV === 'development'
const BACKEND_PORT = 8000
const FRONTEND_PORT = 3000
const CONFIG_FILE = path.join(app.getPath('userData'), 'window-config.json')

// 保存窗口配置
function saveWindowConfig(window) {
  if (!window) return
  
  const bounds = window.getBounds()
  const config = {
    bounds,
    isMaximized: window.isMaximized(),
    timestamp: Date.now()
  }
  
  try {
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2))
  } catch (error) {
    console.error('Failed to save window config:', error)
  }
}

// 加载窗口配置
function loadWindowConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE)) {
      const data = fs.readFileSync(CONFIG_FILE, 'utf8')
      return JSON.parse(data)
    }
  } catch (error) {
    console.error('Failed to load window config:', error)
  }
  
  // 默认配置
  return {
    bounds: { width: 1400, height: 1000, x: undefined, y: undefined },
    isMaximized: false
  }
}

// 检查服务健康状态
function checkServiceHealth(port, maxRetries = 30, interval = 1000) {
  return new Promise((resolve, reject) => {
    let retries = 0
    
    const check = () => {
      const healthUrl = port === BACKEND_PORT ? `http://127.0.0.1:${port}/health` : `http://127.0.0.1:${port}`
      const req = http.get(healthUrl, (res) => {
        if (res.statusCode === 200) {
          resolve(true)
        } else {
          retry()
        }
      })
      
      req.on('error', () => {
        retry()
      })
      
      req.setTimeout(2000, () => {
        req.destroy()
        retry()
      })
    }
    
    const retry = () => {
      retries++
      if (retries >= maxRetries) {
        reject(new Error(`Service on port ${port} not ready after ${maxRetries} attempts`))
      } else {
        setTimeout(check, interval)
      }
    }
    
    check()
  })
}

// 启动后端服务
async function startBackendService() {
  if (isDev) {
    // 开发模式：假设后端已经手动启动
    console.log('Development mode: Checking if backend is running...')
    try {
      await checkServiceHealth(BACKEND_PORT, 5, 1000)
      console.log('Backend service is already running')
      return true
    } catch (error) {
      console.log('Backend not running, please start it manually: cd backend && python main.py')
      return false
    }
  } else {
    // 生产模式：自动启动后端服务
    console.log('Production mode: Starting backend service...')
    
    const backendPath = path.join(__dirname, '../backend')
    const pythonScript = path.join(backendPath, 'main.py')
    
    // 检查Python脚本是否存在
    if (!fs.existsSync(pythonScript)) {
      throw new Error('Backend script not found: ' + pythonScript)
    }
    
    // 启动Python后端
    backendProcess = spawn('python', [pythonScript], {
      cwd: backendPath,
      stdio: ['ignore', 'pipe', 'pipe']
    })
    
    backendProcess.stdout.on('data', (data) => {
      console.log('Backend stdout:', data.toString())
    })
    
    backendProcess.stderr.on('data', (data) => {
      console.error('Backend stderr:', data.toString())
    })
    
    backendProcess.on('close', (code) => {
      console.log('Backend process exited with code:', code)
      backendProcess = null
    })
    
    // 等待服务启动
    try {
      await checkServiceHealth(BACKEND_PORT)
      console.log('Backend service started successfully')
      return true
    } catch (error) {
      console.error('Failed to start backend service:', error)
      return false
    }
  }
}

// 创建应用菜单
function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '打开文件...',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: '音视频文件', extensions: ['mp3', 'wav', 'm4a', 'mp4', 'mov', 'avi', 'mkv'] },
                { name: '所有文件', extensions: ['*'] }
              ]
            }).then(result => {
              if (!result.canceled && result.filePaths.length > 0) {
                mainWindow.webContents.send('file-selected', result.filePaths[0])
              }
            })
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: '查看',
      submenu: [
        { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: '强制重新加载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: '切换开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: '切换全屏', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: '窗口',
      submenu: [
        { label: '最小化', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: '关闭', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于 VideoSense',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于 VideoSense',
              message: 'VideoSense v1.0.0',
              detail: 'AI音视频转录工具\n\n基于人工智能技术的专业转录软件，支持多种音视频格式，提供高精度的文字转换服务。'
            })
          }
        },
        { type: 'separator' },
        {
          label: '访问官网',
          click: () => {
            shell.openExternal('https://github.com/your-repo/videosense')
          }
        }
      ]
    }
  ]

  // macOS 特殊处理
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { label: '关于 ' + app.getName(), role: 'about' },
        { type: 'separator' },
        { label: '服务', role: 'services', submenu: [] },
        { type: 'separator' },
        { label: '隐藏 ' + app.getName(), accelerator: 'Command+H', role: 'hide' },
        { label: '隐藏其他', accelerator: 'Command+Shift+H', role: 'hideothers' },
        { label: '显示全部', role: 'unhide' },
        { type: 'separator' },
        { label: '退出', accelerator: 'Command+Q', click: () => app.quit() }
      ]
    })
  }

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// 媒体检测和捕捉功能
class MediaCapture {
  constructor() {
    this.activeSources = new Map()
    this.captureStream = null
    this.setupIpcHandlers()
  }

  // 设置IPC处理器
  setupIpcHandlers() {
    // 检测可用的媒体源
    ipcMain.handle('detect-media-sources', async () => {
      try {
        return await this.detectMediaSources()
      } catch (error) {
        console.error('Error detecting media sources:', error)
        throw error
      }
    })

    // 请求媒体访问权限
    ipcMain.handle('request-media-permissions', async () => {
      try {
        return await this.requestMediaPermissions()
      } catch (error) {
        console.error('Error requesting permissions:', error)
        throw error
      }
    })

    // 开始捕捉指定媒体源
    ipcMain.handle('start-media-capture', async (event, sourceId, options = {}) => {
      try {
        return await this.startCapture(sourceId, options)
      } catch (error) {
        console.error('Error starting capture:', error)
        throw error
      }
    })

    // 停止媒体捕捉
    ipcMain.handle('stop-media-capture', async () => {
      try {
        return await this.stopCapture()
      } catch (error) {
        console.error('Error stopping capture:', error)
        throw error
      }
    })

    // 获取捕捉状态
    ipcMain.handle('get-capture-status', async () => {
      return {
        isCapturing: !!this.captureStream,
        activeSources: Array.from(this.activeSources.values())
      }
    })
  }

  // 检测系统中的媒体源
  async detectMediaSources() {
    try {
      // 获取所有可用的媒体源（屏幕和音频）
      const sources = await desktopCapturer.getSources({
        types: ['window', 'screen'],
        thumbnailSize: { width: 150, height: 150 },
        fetchWindowIcons: true
      })

      // 过滤和分类媒体源
      const mediaSources = []
      
      for (const source of sources) {
        // 检测是否可能包含音频内容
        const hasAudio = await this.detectAudioInSource(source)
        
        if (hasAudio || this.isKnownMediaApplication(source.name)) {
          const mediaSource = {
            id: source.id,
            name: source.name,
            type: this.getSourceType(source),
            hasAudio: hasAudio,
            thumbnail: source.thumbnail?.toDataURL() || null,
            appIcon: source.appIcon?.toDataURL() || null,
            display_id: source.display_id,
            status: 'detected'
          }
          
          mediaSources.push(mediaSource)
          this.activeSources.set(source.id, mediaSource)
        }
      }

      // 添加模拟的流媒体检测（实际实现中会检测浏览器标签页）
      const browserSources = await this.detectBrowserMediaSources()
      mediaSources.push(...browserSources)

      console.log(`Detected ${mediaSources.length} media sources`)
      return mediaSources
    } catch (error) {
      console.error('Failed to detect media sources:', error)
      throw new Error('无法检测媒体源: ' + error.message)
    }
  }

  // 检测音频源中是否有音频内容
  async detectAudioInSource(source) {
    // 基于应用名称和窗口标题进行判断，排除命令行工具
    const sourceName = source.name.toLowerCase()
    
    // 排除明显的非媒体源
    const excludeKeywords = [
      'terminal', 'console', 'cmd', 'powershell', 'bash', 'zsh',
      'npm', 'node', 'python', 'java', 'code', 'vscode', 'cursor',
      'finder', 'explorer', 'desktop'
    ]
    
    // 如果包含排除关键词，直接返回false
    if (excludeKeywords.some(keyword => sourceName.includes(keyword))) {
      return false
    }
    
    // 音频媒体关键词
    const audioKeywords = [
      'youtube', 'spotify', 'netflix', 'bilibili', 'twitch',
      'music', 'player', 'chrome', 'firefox', 'safari', 'edge',
      '音乐', '播放', '直播'
    ]
    
    return audioKeywords.some(keyword => sourceName.includes(keyword))
  }

  // 判断是否为已知的媒体应用
  isKnownMediaApplication(appName) {
    const mediaApps = [
      'Google Chrome', 'Mozilla Firefox', 'Safari', 'Microsoft Edge',
      'Spotify', 'iTunes', 'VLC', 'QuickTime Player', 'IINA',
      'Bilibili', 'YouTube Desktop', 'Discord', 'Zoom', 'Teams',
      'Tencent Meeting', 'DingTalk', 'Skype', 'FaceTime'
    ]
    
    return mediaApps.some(app => appName.includes(app))
  }

  // 获取媒体源类型
  getSourceType(source) {
    const name = source.name.toLowerCase()
    
    if (name.includes('chrome') || name.includes('firefox') || 
        name.includes('safari') || name.includes('edge')) {
      return 'browser'
    } else if (name.includes('spotify') || name.includes('music') || 
               name.includes('itunes')) {
      return 'music'
    } else if (name.includes('zoom') || name.includes('teams') || 
               name.includes('meeting')) {
      return 'meeting'
    } else if (name.includes('screen')) {
      return 'screen'
    } else {
      return 'application'
    }
  }

  // 检测浏览器中的媒体源
  async detectBrowserMediaSources() {
    // 实际实现中，这里会通过浏览器扩展或其他方式检测正在播放的媒体
    // 目前返回空数组，等待真实实现
    return []
  }

  // 请求媒体访问权限
  async requestMediaPermissions() {
    try {
      console.log('Starting permission request...')
      
      // macOS权限检查
      if (process.platform === 'darwin') {
        const initialPermissions = {
          screen: systemPreferences.getMediaAccessStatus('screen'),
          microphone: systemPreferences.getMediaAccessStatus('microphone'),
          camera: systemPreferences.getMediaAccessStatus('camera')
        }
        
        console.log('Initial permissions:', initialPermissions)

        // 如果需要屏幕录制权限，请求权限
        if (initialPermissions.screen !== 'granted') {
          console.log('Requesting screen recording permission...')
          try {
            // 对于屏幕录制权限，通常需要用户手动在系统偏好设置中授予
            const granted = await systemPreferences.askForMediaAccess('screen')
            console.log('Screen permission request result:', granted)
            
            // 等待一下再检查状态
            await new Promise(resolve => setTimeout(resolve, 1000))
            initialPermissions.screen = systemPreferences.getMediaAccessStatus('screen')
          } catch (error) {
            console.error('Screen access request error:', error)
          }
        }

        // 如果需要麦克风权限，请求权限
        if (initialPermissions.microphone !== 'granted') {
          console.log('Requesting microphone permission...')
          try {
            const granted = await systemPreferences.askForMediaAccess('microphone')
            console.log('Microphone permission request result:', granted)
            
            await new Promise(resolve => setTimeout(resolve, 1000))
            initialPermissions.microphone = systemPreferences.getMediaAccessStatus('microphone')
          } catch (error) {
            console.error('Microphone access request error:', error)
          }
        }
        
        const finalPermissions = {
          screen: systemPreferences.getMediaAccessStatus('screen'),
          microphone: systemPreferences.getMediaAccessStatus('microphone'),
          camera: systemPreferences.getMediaAccessStatus('camera')
        }
        
        console.log('Final permissions:', finalPermissions)
        
        // 如果屏幕录制权限仍未获得，显示手动设置提示
        if (finalPermissions.screen !== 'granted') {
          // 向前端发送需要手动设置的消息
          if (mainWindow) {
            mainWindow.webContents.send('permission-manual-setup-required', {
              type: 'screen',
              message: '屏幕录制权限需要在"系统偏好设置 > 安全性与隐私 > 隐私 > 屏幕录制"中手动启用',
              instructions: [
                '1. 打开"系统偏好设置"',
                '2. 点击"安全性与隐私"',
                '3. 点击"隐私"选项卡',
                '4. 在左侧列表中选择"屏幕录制"',
                '5. 勾选"VideoSense"复选框'
              ]
            })
          }
        }
        
        return finalPermissions
      } else {
        // Windows/Linux - 权限通常在运行时授予
        console.log('Non-macOS platform, returning granted permissions')
        return {
          screen: 'granted',
          microphone: 'granted',
          camera: 'granted'
        }
      }
    } catch (error) {
      console.error('Permission request failed:', error)
      throw new Error('权限请求失败: ' + error.message)
    }
  }

  // 开始捕捉媒体
  async startCapture(sourceId, options = {}) {
    try {
      const source = this.activeSources.get(sourceId)
      if (!source) {
        throw new Error('未找到指定的媒体源')
      }

      // 停止之前的捕捉
      if (this.captureStream) {
        await this.stopCapture()
      }

      console.log(`Starting capture for source: ${source.name}`)
      
      // 真实的音频流捕捉
      this.captureStream = await this.getCaptureStream(sourceId)
      
      // 设置音频数据处理
      await this.setupAudioProcessing(this.captureStream)
      
      // 更新源状态
      source.status = 'capturing'
      source.startTime = Date.now()
      
      // 通知渲染进程捕捉开始
      if (mainWindow) {
        mainWindow.webContents.send('capture-status-changed', {
          isCapturing: true,
          sourceId: sourceId,
          sourceName: source.name
        })
      }
      
      // 返回捕捉开始的确认
      return {
        success: true,
        sourceId: sourceId,
        sourceName: source.name,
        startTime: source.startTime,
        message: '媒体捕捉已开始'
      }
    } catch (error) {
      console.error('Capture start failed:', error)
      throw new Error('开始捕捉失败: ' + error.message)
    }
  }

  // 获取音频流
  async getCaptureStream(sourceId) {
    try {
      console.log('Getting capture stream for source:', sourceId)

      // 向渲染进程请求音频流（让用户选择要捕捉的屏幕/应用）
      const streamId = await new Promise((resolve, reject) => {
        // 设置一次性监听器
        const handleStreamReady = (event, streamId) => {
          ipcMain.removeListener('audio-stream-error', handleStreamError)
          resolve(streamId)
        }
        
        const handleStreamError = (event, error) => {
          ipcMain.removeListener('audio-stream-ready', handleStreamReady)
          reject(new Error(error))
        }
        
        ipcMain.once('audio-stream-ready', handleStreamReady)
        ipcMain.once('audio-stream-error', handleStreamError)
        
        // 发送音频流请求到渲染进程
        if (mainWindow) {
          mainWindow.webContents.send('request-audio-stream', sourceId)
        } else {
          reject(new Error('主窗口不可用'))
        }
        
        // 30秒超时（给用户足够时间选择）
        setTimeout(() => {
          ipcMain.removeListener('audio-stream-ready', handleStreamReady)
          ipcMain.removeListener('audio-stream-error', handleStreamError)
          reject(new Error('音频流获取超时 - 请确保选择了包含音频的应用或窗口'))
        }, 30000)
      })
      
      console.log('Audio stream acquired successfully')
      return streamId
    } catch (error) {
      console.error('Failed to get capture stream:', error)
      throw error
    }
  }

  // 设置音频处理
  async setupAudioProcessing(streamId) {
    try {
      // 发送到渲染进程进行音频处理
      if (mainWindow) {
        mainWindow.webContents.send('setup-audio-processing', streamId)
      }
      
      // 设置音频数据接收
      ipcMain.on('audio-data', (event, audioBuffer) => {
        this.processAudioData(audioBuffer)
      })
      
      console.log('Audio processing setup completed')
    } catch (error) {
      console.error('Failed to setup audio processing:', error)
      throw error
    }
  }

  // 处理音频数据
  async processAudioData(audioBuffer) {
    try {
      // 发送音频数据到后端进行转录
      const response = await this.sendAudioToBackend(audioBuffer)
      
      // 通知前端转录结果
      if (mainWindow && response.text) {
        mainWindow.webContents.send('transcription-result', {
          text: response.text,
          confidence: response.confidence || 0.9,
          timestamp: Date.now()
        })
      }
    } catch (error) {
      console.error('Failed to process audio data:', error)
    }
  }

  // 发送音频到后端
  async sendAudioToBackend(audioBuffer) {
    try {
      const FormData = require('form-data')
      const fetch = require('node-fetch')
      
      const form = new FormData()
      form.append('audio', Buffer.from(audioBuffer), {
        filename: 'audio.wav',
        contentType: 'audio/wav'
      })
      
      const response = await fetch(`http://127.0.0.1:${BACKEND_PORT}/transcribe/realtime`, {
        method: 'POST',
        body: form,
        headers: form.getHeaders()
      })
      
      if (!response.ok) {
        throw new Error(`Backend error: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('Failed to send audio to backend:', error)
      throw error
    }
  }

  // 停止媒体捕捉
  async stopCapture() {
    try {
      if (this.captureStream) {
        // 停止所有轨道
        this.captureStream.getTracks().forEach(track => track.stop())
        this.captureStream = null
      }

      // 更新所有源的状态
      for (const source of this.activeSources.values()) {
        if (source.status === 'capturing') {
          source.status = 'detected'
          source.endTime = Date.now()
          if (source.startTime) {
            source.duration = source.endTime - source.startTime
          }
        }
      }

      console.log('Media capture stopped')
      return {
        success: true,
        message: '媒体捕捉已停止'
      }
    } catch (error) {
      console.error('Capture stop failed:', error)
      throw new Error('停止捕捉失败: ' + error.message)
    }
  }

  // 清理资源
  cleanup() {
    if (this.captureStream) {
      this.captureStream.getTracks().forEach(track => track.stop())
    }
    this.activeSources.clear()
  }
}

// 创建媒体捕捉实例
let mediaCapture = null

// 创建主窗口
async function createWindow() {
  // 加载窗口配置
  const config = loadWindowConfig()
  
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    ...config.bounds,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true, // 启用webSecurity提高安全性
      allowRunningInsecureContent: false, // 禁止不安全内容
      experimentalFeatures: false, // 禁用实验性功能
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'default',
    show: false,
    icon: path.join(__dirname, 'assets', 'icon.png') // 应用图标
  })

  // 设置窗口标题
  mainWindow.setTitle('VideoSense - AI音视频转录工具')

  // 恢复窗口状态
  if (config.isMaximized) {
    mainWindow.maximize()
  }

  // 启动后端服务
  try {
    const backendReady = await startBackendService()
    if (!backendReady && !isDev) {
      dialog.showErrorBox('启动失败', '无法启动后端服务，请检查Python环境和依赖安装。')
      app.quit()
      return
    }
  } catch (error) {
    console.error('Failed to start backend:', error)
    if (!isDev) {
      dialog.showErrorBox('启动失败', '后端服务启动失败：' + error.message)
      app.quit()
      return
    }
  }

  // 等待前端服务（仅开发模式）
  if (isDev) {
    try {
      console.log('Checking frontend service...')
      await checkServiceHealth(FRONTEND_PORT, 10, 1000)
      mainWindow.loadURL(`http://127.0.0.1:${FRONTEND_PORT}`)
      
      // 开发模式打开开发者工具
      mainWindow.webContents.openDevTools()
    } catch (error) {
      console.error('Frontend service not available:', error)
      dialog.showErrorBox('连接失败', '无法连接到前端开发服务器，请确保已启动 npm run dev')
    }
  } else {
    // 生产模式：加载构建后的文件
    const indexPath = path.join(__dirname, '../frontend/out/index.html')
    console.log('Loading file from:', indexPath)
    if (fs.existsSync(indexPath)) {
      console.log('File exists, loading...')
      mainWindow.loadFile(indexPath)
      console.log('File load initiated')
    } else {
      console.error('File not found:', indexPath)
      dialog.showErrorBox('文件缺失', '前端构建文件不存在，请先运行构建命令。')
      app.quit()
      return
    }
  }

  // 文件拖拽支持
  mainWindow.webContents.on('will-navigate', (event, url) => {
    if (url.startsWith('file://')) {
      event.preventDefault()
      const filePath = url.replace('file://', '')
      mainWindow.webContents.send('file-dropped', filePath)
    }
  })

  // 当页面加载完成后显示窗口
  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show!')
    mainWindow.show()
    
    // macOS 获得焦点
    if (process.platform === 'darwin') {
      mainWindow.focus()
    }
  })

  // 添加错误处理
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Failed to load:', errorCode, errorDescription)
  })

  // 保存窗口状态
  mainWindow.on('close', () => {
    saveWindowConfig(mainWindow)
  })

  // 处理窗口关闭
  mainWindow.on('closed', () => {
    mainWindow = null
    
    // 停止后端服务
    if (backendProcess) {
      backendProcess.kill()
      backendProcess = null
    }
  })

  return mainWindow
}

// IPC 通信处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options)
  return result
})

// 权限管理 IPC 处理程序
ipcMain.handle('request-authorization', async () => {
  if (!permissionManager) {
    permissionManager = new PermissionManager()
  }
  return await permissionManager.requestOneTimeAuthorization()
})

ipcMain.handle('set-system-proxy', async (event, enable, host = '127.0.0.1', port = 8899) => {
  if (!permissionManager) {
    permissionManager = new PermissionManager()
  }
  return await permissionManager.setSystemProxy(enable, host, port)
})

ipcMain.handle('check-proxy-status', async () => {
  if (!permissionManager) {
    permissionManager = new PermissionManager()
  }
  return await permissionManager.checkSystemProxyStatus()
})

ipcMain.handle('get-permission-state', () => {
  if (!permissionManager) {
    permissionManager = new PermissionManager()
  }
  return permissionManager.getPermissionState()
})

ipcMain.handle('reset-permissions', () => {
  if (!permissionManager) {
    permissionManager = new PermissionManager()
  }
  return permissionManager.resetPermissions()
})

// 证书管理 IPC 处理程序
ipcMain.handle('install-certificate', async () => {
  if (!permissionManager) {
    permissionManager = new PermissionManager()
  }
  return await permissionManager.installCertificate()
})

ipcMain.handle('check-certificate-status', async () => {
  if (!permissionManager) {
    permissionManager = new PermissionManager()
  }
  return await permissionManager.checkCertificateStatus()
})

// 应用就绪
app.whenReady().then(async () => {
  // 初始化媒体捕捉
  mediaCapture = new MediaCapture()
  
  // 创建菜单
  createMenu()
  
  // 创建主窗口
  await createWindow()

  // macOS 应用激活处理
  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      await createWindow()
    }
  })
})

// 窗口全部关闭
app.on('window-all-closed', () => {
  // macOS 保持应用运行
  if (process.platform !== 'darwin') {
    // 停止后端服务
    if (backendProcess) {
      backendProcess.kill()
      backendProcess = null
    }
    app.quit()
  }
})

// 应用退出前清理
app.on('before-quit', () => {
  if (backendProcess) {
    backendProcess.kill()
    backendProcess = null
  }
  if (mediaCapture) {
    mediaCapture.cleanup()
  }
})

// 安全设置
app.on('web-contents-created', (event, contents) => {
  // 阻止新窗口创建
  contents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })

  // 阻止导航到外部URL
  contents.on('will-navigate', (event, url) => {
    const allowedOrigins = [
      `http://localhost:${FRONTEND_PORT}`,
      `http://127.0.0.1:${FRONTEND_PORT}`,
      `http://localhost:${BACKEND_PORT}`,
      `http://127.0.0.1:${BACKEND_PORT}`
    ]
    
    const urlObj = new URL(url)
    const origin = urlObj.origin
    
    if (!allowedOrigins.includes(origin) && !url.startsWith('file://')) {
      event.preventDefault()
      shell.openExternal(url)
    }
  })
}) 