# VideoSense - AI音视频转录工具

## 项目简介

VideoSense是一个基于AI的智能音视频转录工具，采用客户端架构，确保用户隐私安全。主要功能包括：

- **文件转录**：支持本地音视频文件的AI转录
- **播放即录**：实时检测和转录正在播放的媒体内容
- **智能抓包**：与翻墙工具兼容的智能代理模式
- **编辑器**：强大的文本编辑和管理功能

## 技术架构

```
VideoSense/
├── frontend/          # Next.js 前端界面
├── backend/           # FastAPI 后端服务
├── electron/          # Electron 桌面客户端
├── shared/           # 共享类型定义和常量
└── scripts/          # 构建和部署脚本
```

### 技术栈

- **前端**: Next.js 14 + TypeScript + Tailwind CSS + Shadcn/ui
- **后端**: FastAPI + Python 3.9 + SQLite + mitmproxy
- **桌面**: Electron + Node.js
- **AI服务**: SiliconFlow API

## 快速开始

### 开发环境设置

1. **安装依赖**
   ```bash
   # 前端依赖
   cd frontend && npm install
   
   # 后端依赖
   cd backend && pip install -r requirements.txt
   
   # Electron依赖
   cd electron && npm install
   ```

2. **启动开发服务**
   ```bash
   # 从根目录运行
   ./start-videosense.sh
   ```

### 生产构建

```bash
cd electron
npm run build
```

## 核心功能

### 1. 智能抓包模块
- 自动检测现有代理配置（Clash Pro、V2Ray等）
- 智能代理链模式：浏览器 → VideoSense → 现有代理 → 互联网
- 跨平台系统代理管理

### 2. AI转录模块
- SiliconFlow API集成
- 多格式音视频支持
- 高精度语音识别

### 3. 桌面客户端
- 跨平台支持（macOS/Windows/Linux）
- 系统权限管理
- 证书安装自动化

### 4. 前端界面
- 响应式设计
- 暗黑模式支持
- 实时状态监控

## 项目状态

| 模块 | 完成度 | 状态 |
|------|--------|------|
| 智能抓包 | 90% | 🟢 运行正常 |
| AI转录 | 100% | 🟢 功能完整 |
| 桌面客户端 | 80% | 🟡 基本可用 |
| 前端界面 | 100% | 🟢 用户友好 |

## 许可证

MIT License

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 支持

如有问题，请查看：
- [产品需求文档](../prd/VideoSense-PRD-v1.0.0.md)
- [任务管理文档](../task/) 